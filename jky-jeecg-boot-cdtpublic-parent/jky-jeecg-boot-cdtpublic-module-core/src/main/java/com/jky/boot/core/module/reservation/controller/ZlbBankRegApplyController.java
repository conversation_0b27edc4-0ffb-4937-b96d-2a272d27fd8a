package com.jky.boot.core.module.reservation.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.annotation.RedissonLock;
import com.jky.boot.core.module.gzpt.entity.BankRegApply;
import com.jky.boot.core.module.gzpt.entity.Studentinfo;
import com.jky.boot.core.module.gzpt.service.IBankRegApplyService;
import com.jky.boot.core.util.ZlbStuUtils;
import com.jky.crypto.annotation.DesensitizedAnno;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 学员多笔冻结申请
 *
 * <AUTHOR>
 * @version 2024-09-25
 */
@RestController
@RequestMapping("/zlb/bankRegApply")
@Slf4j
public class ZlbBankRegApplyController {

    @Autowired
    private IBankRegApplyService bankRegApplyService;

    /**
     * 学员多笔冻结申请列表
     */
    @GetMapping(value = "/list")
    @DesensitizedAnno
    public Result<?> zlbList(BankRegApply rec,
                             @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                             @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        if (StringUtils.isBlank(rec.getId())) {
            return Result.error("必要参数为空");
        }
        Studentinfo stu = ZlbStuUtils.getStuByZlbId(rec.getId());
        if (Objects.isNull(stu)) {
            return Result.error("请先报名！");
        }
        Page<BankRegApply> page = new Page<>(pageNo, pageSize);
        IPage<BankRegApply> pageList = bankRegApplyService.getApplyList(page, stu.getStunum(), rec.getApplyType());
        return Result.ok(pageList);
    }

    /**
     * 学员点击一个 多笔冻结 按钮
     * 根据学员当前的状态，系统自动生成一条待确认的多笔冻结申请
     */
    @PostMapping(value = "/add")
    @RedissonLock(key = "#rec.id", keyPrefix = "bankReg:apply:", waitTime = 1, lockTime = 5)
    public Result<?> zlbAdd(@RequestBody BankRegApply rec) {
        if (StringUtils.isBlank(rec.getId())) {
            return Result.error("必要参数为空");
        }
        // 校验学员存在性
        Studentinfo stu = ZlbStuUtils.getStuByZlbId(rec.getId());
        if (Objects.isNull(stu)) {
            return Result.error("请先报名");
        }
        return bankRegApplyService.saveDefaultApply(stu);
    }

    /**
     * 学员 确认 上面系统产生的 待确认的多笔冻结申请
     */
    @PostMapping(value = "/confirm")
    @RedissonLock(key = "#rec.applyId", keyPrefix = "bankReg:apply:", waitTime = 1, lockTime = 5)
    public Result<?> zlbConfirm(@RequestBody BankRegApply rec) {
        if (StringUtils.isBlank(rec.getApplyId()) || StringUtils.isBlank(rec.getId())) {
            return Result.error("必要参数为空");
        }
        // 校验学员存在性
        Studentinfo stu = ZlbStuUtils.getStuByZlbId(rec.getId());
        if (Objects.isNull(stu)) {
            return Result.error("请先报名");
        }

        return bankRegApplyService.setApplyConfirm(rec.getApplyId(), stu.getStunum());
    }


}
