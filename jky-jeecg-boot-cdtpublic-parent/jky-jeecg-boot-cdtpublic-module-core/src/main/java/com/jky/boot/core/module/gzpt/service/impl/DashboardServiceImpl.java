package com.jky.boot.core.module.gzpt.service.impl;

import com.jky.boot.common.redis.RedisCache;
import com.jky.boot.core.module.gzpt.dto.DashboardDto;
import com.jky.boot.core.module.gzpt.mapper.DashboardMapper;
import com.jky.boot.core.module.gzpt.service.IDashboardService;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

@Service
public class DashboardServiceImpl implements IDashboardService {

    private final DashboardMapper dashboardMapper;

    private final RedisTemplate redisTemplate;
    public DashboardServiceImpl(DashboardMapper dashboardMapper, RedisTemplate redisTemplate) {
        this.dashboardMapper = dashboardMapper;
        this.redisTemplate = redisTemplate;
    }

    @Override
    public DashboardDto total(String leader) {
        String key = "homePageData";
        DashboardDto dashboardDto = new DashboardDto();
        List<Map<String, Object>> weekAddStudentTotal = this.lastWeekStuAmount(leader);
        long coachTotal = dashboardMapper.getCoachTotal(leader);
        long carInfoTotal = dashboardMapper.getCarInfoTotal(leader);
        long institutionTotal = dashboardMapper.getInstitutionTotal(leader);
        long studentTotal = dashboardMapper.getStudentTotal(leader);
        dashboardDto.setCoachTotal(coachTotal);
        dashboardDto.setDrivingSchoolTotal(institutionTotal);
        dashboardDto.setStudentTotal(studentTotal);
        dashboardDto.setWeekAddStudentTotal(weekAddStudentTotal);
        dashboardDto.setInstructionalCarTotal(carInfoTotal);
        if (leader.length()==4||leader.length()==6){
            if (!redisTemplate.hasKey(key+leader)){
                redisTemplate.opsForValue().set(key+leader,dashboardDto,60*60*6, TimeUnit.SECONDS);
            }
        }
        return dashboardDto;
    }

    private List<Map<String, Object>> lastWeekStuAmount(String leader) {
        Date date = new Date();
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        List<String> dateList = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        dateList.add(dateFormat.format(calendar.getTime()));
        for(int i = 1; i <= 6; i++){
            calendar.add(Calendar.DATE, -1);
            dateList.add(dateFormat.format(calendar.getTime()));
        }
        List<Map<String, Object>> less = dashboardMapper.getWeekAddStudentTotal(leader, dateList);
        HashSet<Object> set = new HashSet<>();
        less.forEach(e -> set.add(e.get("create_time")));
        for(String one:dateList){
            if(!set.contains(one)){
                HashMap<String, Object> map = new HashMap<>();
                map.put("create_time", one);
                map.put("count", "0");
                less.add(map);
            }
        }
        return less.stream().sorted(Comparator.comparing(e -> (String) (e.get("create_time")))).collect(Collectors.toList());
    }
}
