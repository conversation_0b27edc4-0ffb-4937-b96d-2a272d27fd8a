package com.jky.boot.core.module.sxgzpt.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jky.crypto.annotation.CryptoFieldAnno;
import com.jky.crypto.annotation.DesensitizedFieldAnno;
import com.jky.crypto.enums.DesensitizedTypeEnum;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: t_m_stu_appointment
 * @Author: jeecg-boot
 * @Date:   2023-08-03
 * @Version: V1.0
 */
@Data
@TableName("t_m_stu_appointment")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="t_m_stu_appointment对象", description="t_m_stu_appointment")
public class TMStuAppointment implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private String id;
	/**学员名字*/
	@Excel(name = "学员名字", width = 15)
    @ApiModelProperty(value = "学员名字")
    private java.lang.String stuname;
	/**学员身份证*/
	@Excel(name = "学员身份证", width = 15)
    @ApiModelProperty(value = "学员身份证")
    @DesensitizedFieldAnno(value = DesensitizedTypeEnum.CUSTOM, start = 6, end = 4)
    private java.lang.String idcard;
	/**学员编号*/
	@Excel(name = "学员编号", width = 15)
    @ApiModelProperty(value = "学员编号")
    private java.lang.String stunum;

    /**全国统一编号*/
    @Excel(name = "全国统一编号", width = 15)
    @ApiModelProperty(value = "全国统一编号")
    private java.lang.String coachnum;

    /**姓名*/
    @Excel(name = "教练姓名", width = 15)
    @ApiModelProperty(value = "教练姓名")
    @CryptoFieldAnno
    private java.lang.String coachname;
	/**驾校编号*/
	@Excel(name = "驾校编号", width = 15)
    @ApiModelProperty(value = "驾校编号")
    private java.lang.String inscode;
	/**驾校名字*/
	@Excel(name = "驾校名字", width = 15)
    @ApiModelProperty(value = "驾校名字")
    private java.lang.String insname;
    /**培训车型 	下列编码单选：
     A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P*/
    @Excel(name = "培训车型", width = 15)
    @ApiModelProperty(value = "培训车型 	下列编码单选： A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P")
    private java.lang.String traintype;
    /**手机号码*/
    @Excel(name = "手机号码", width = 15)
    @ApiModelProperty(value = "手机号码")
    @DesensitizedFieldAnno(DesensitizedTypeEnum.MOBILE_PHONE)
    @CryptoFieldAnno
    private java.lang.String phone;
	/**预约编号（唯⼀）*/
	@Excel(name = "预约编号（唯⼀）", width = 15)
    @ApiModelProperty(value = "预约编号（唯⼀）")
    private java.lang.String orderNo;
	/**培训类型（1，课堂 2，模拟 3， 实操科⽬⼆ 4，实操科⽬三）*/
	@Excel(name = "培训类型（1，课堂 2，模拟 3， 实操科⽬⼆ 4，实操科⽬ 三）", width = 15)
    @ApiModelProperty(value = "培训类型（1，课堂 2，模拟 3， 实操科⽬⼆ 4，实操科⽬ 三）")
    @Dict(dicCode = "sys_type")
    private java.lang.Integer classType;
	/**培训状态（4取消， 9，超时取消 1 成功）*/
	@Excel(name = "培训状态（4取消， 9，超时取消 1 成功）", width = 15)
    @ApiModelProperty(value = "培训状态（4取消， 9，超时取消 1 成功）")
    @Dict(dicCode = "booking_status")
    private java.lang.Integer status;
	/**培训的模拟点名称或地址，如：（xxxx模拟中⼼）*/
	@Excel(name = "培训的模拟点名称或地址，如：（xxxx模拟中⼼）", width = 15)
    @ApiModelProperty(value = "培训的模拟点名称或地址，如：（xxxx模拟中⼼）")
    private java.lang.String address;
	/**培训时⻓*/
	@Excel(name = "培训时⻓", width = 15)
    @ApiModelProperty(value = "培训时⻓")
    private java.lang.Double classHour;
	/**培训内容*/
	@Excel(name = "培训内容", width = 15)
    @ApiModelProperty(value = "培训内容")
    private java.lang.String content;
	/**培训开始时间*/
	@Excel(name = "培训开始时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "培训开始时间")
    private java.util.Date startTime;
	/**培训结束时间*/
	@Excel(name = "培训结束时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "培训结束时间")
    private java.util.Date endTime;

	@TableField(exist = false)
	private String todayDate;
	@TableField(exist = false)
	private String date;
    @TableField(exist = false)
	private String zlbid;

    /**classType*/
    @TableField(exist = false)
	private Integer course;

    /**1表示可撤销，0表示不可撤销*/
    @TableField(exist = false)
	private Integer isRevoke;

    /**排班id*/
    @TableField(exist = false)
    private String schedId;


    /**创建时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
    /**修改时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private java.util.Date updateTime;
    /**修改人*/
    @ApiModelProperty(value = "修改人")
    private java.lang.String updateBy;
}
