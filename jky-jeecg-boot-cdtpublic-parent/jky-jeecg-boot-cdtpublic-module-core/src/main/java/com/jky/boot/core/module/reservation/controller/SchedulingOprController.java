package com.jky.boot.core.module.reservation.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.base.dao.SchedulingApply;
import com.jky.boot.base.dao.SchedulingOpr;
import com.jky.boot.base.dao.TimeTemplateDetails;
import com.jky.boot.base.service.ISchedulingApplyService;
import com.jky.boot.base.service.ISchedulingOprService;
import com.jky.boot.base.service.ITimeTemplateDetailsService;
import com.jky.boot.common.beanCopy.BeanConvertUtils;
import com.jky.boot.common.exception.JkyCdtpublicException;
import com.jky.boot.common.utils.DateUtils;
import com.jky.boot.core.module.gzpt.entity.Coach;
import com.jky.boot.core.module.gzpt.entity.Studentinfo;
import com.jky.boot.core.module.reservation.vo.ZlbOprVo;
import com.jky.boot.core.module.reservation.vo.oprAdd.InfoDetails;
import com.jky.boot.core.module.reservation.vo.oprAdd.OprAddVo;
import com.jky.boot.core.module.reservation.vo.oprAdd.OprParams;
import com.jky.boot.core.util.ZlbStuUtils;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 实操课程预约
 *
 * <AUTHOR>
 * @version 2024-09-29
 */
@Slf4j
@RestController
@RequestMapping("/schedule/opr")
public class SchedulingOprController {

    @Autowired
    private ISchedulingOprService schedulingOprService;
    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Autowired
    private ISchedulingApplyService schedulingApplyService;
    @Autowired
    private ITimeTemplateDetailsService timeTemplateDetailsService;

    /**
     * 分页列表查询
     */
    @GetMapping(value = "/list")
    public Result<?> queryPageList(SchedulingOpr schedulingOpr,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        List<String> insCodes = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(schedulingOpr.getInscode());
        schedulingOpr.setInscode(null);
        QueryWrapper<SchedulingOpr> queryWrapper = QueryGenerator.initQueryWrapper(schedulingOpr, req.getParameterMap());
        queryWrapper.in("inscode", insCodes);
        queryWrapper.orderByDesc("class_date, class_time");
        Page<SchedulingOpr> page = new Page<>(pageNo, pageSize);
        IPage<SchedulingOpr> pageList = schedulingOprService.page(page, queryWrapper);
        return Result.ok(pageList);
    }

    /**
     * 添加
     */
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody SchedulingOpr rec) {
        rec.setInscode(JkSecurityUtils.getDeptOrgCode());
        List<String> timeSlots = timeSlot(rec.getClassTime());
        // 查看该教练当天有无重复时段
        List<SchedulingOpr> list = schedulingOprService.list(
                Wrappers.<SchedulingOpr>lambdaQuery()
                        .eq(SchedulingOpr::getCoachnum, rec.getCoachnum())
                        .eq(SchedulingOpr::getClassDate, rec.getClassDate())
        );
        List<String> collect = list.stream().map(SchedulingOpr::getClassTime).collect(Collectors.toList());
        collect.retainAll(timeSlots);
        if (!collect.isEmpty()) {
            return Result.error("当日开班有重复时段，请重新选择时段");
        }
        for (String timeSlot : timeSlots) {
            SchedulingOpr one = new SchedulingOpr();
            BeanUtils.copyProperties(rec, one);
            one.setClassTime(timeSlot);
            String[] split = timeSlot.split("-");
            one.setStartTime(split[0]).setEndTime(split[1]);
            schedulingOprService.save(one);
        }
        return Result.ok("添加成功！");
    }

    /**
     * 添加-20250106改版
     */
    @PostMapping(value = "/addBatch")
    public Result<?> addNew(@RequestBody OprAddVo rec) {
        String insCode = JkSecurityUtils.getDeptOrgCode();
        String startDate = rec.getStartDate();
        String endDate = DateUtils.addDays(rec.getEndDate(), 1);
        List<String> dateList = new ArrayList<>();
        while (!startDate.equals(endDate)) {
            dateList.add(startDate);
            startDate = DateUtils.addDays(startDate, 1);
        }
        for (InfoDetails item : rec.getItems()) {
            threadPoolTaskExecutor.execute(() -> {
                for (String date : dateList) {
                    // 查询教练当天已占用的时段
                    List<SchedulingOpr> list = schedulingOprService.list(
                            Wrappers.<SchedulingOpr>lambdaQuery()
                                    .eq(SchedulingOpr::getCoachnum, item.getCoachnum())
                                    .eq(SchedulingOpr::getClassDate, date)
                    );
                    List<String> existClassTime = list.stream().map(SchedulingOpr::getClassTime).collect(Collectors.toList());
                    // 筛选未占用时段
                    List<TimeTemplateDetails> templateList = timeTemplateDetailsService.list(
                            Wrappers.<TimeTemplateDetails>lambdaQuery()
                                    .eq(TimeTemplateDetails::getTemplateId, item.getTemplateId())
                    );
                    templateList = templateList.stream().filter(e -> !existClassTime.contains(e.getClassTime())).collect(Collectors.toList());
                    List<SchedulingOpr> oprList = new ArrayList<>();
                    for (TimeTemplateDetails detail : templateList) {
                        SchedulingOpr res = new SchedulingOpr();
                        res.setInscode(insCode).setCoachnum(item.getCoachnum()).setSubject(item.getSubject());
                        res.setClassDate(DateUtils.parseDate(date, DateUtils.YYYY_MM_DD)).setTraintype(item.getTraintype());
                        res.setStartTime(detail.getStartTime()).setEndTime(detail.getEndTime());
                        res.setClassTime(detail.getStartTime() + "-" + detail.getEndTime());
                        oprList.add(res);
                    }
                    schedulingOprService.saveBatch(oprList);
                }
            });
        }
        return Result.ok("添加成功！");
    }

    /**
     * 编辑
     */
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> edit(@RequestBody SchedulingOpr schedulingOpr) {
        // 根据业务需求修改驾校编码
        schedulingOpr.setInscode(JkSecurityUtils.getOrgCodeByUnknown(schedulingOpr.getInscode()));
        // 如果 classTime 不为空，并且包含“-”，则分割字符串覆盖 startTime 和 endTime
        if (schedulingOpr.getClassTime() != null && schedulingOpr.getClassTime().contains("-")) {
            String[] times = schedulingOpr.getClassTime().split("-");
            if (times.length >= 2) {
                schedulingOpr.setStartTime(times[0]);
                schedulingOpr.setEndTime(times[1]);
            }
        }
        // 更新数据
        schedulingOprService.updateById(schedulingOpr);
        return Result.ok("编辑成功!");
    }

    /**
     * 通过id删除
     */
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id") String id) {
        schedulingOprService.removeById(id);
        // 删除相关预约记录
        schedulingApplyService.remove(
                Wrappers.<SchedulingApply>lambdaQuery()
                        .eq(SchedulingApply::getClassId, id)
        );
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     */
    @DeleteMapping("/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids") String ids) {
        // 检查是否存在已预约的记录
        List<String> idList = Arrays.asList(ids.split(","));
        List<SchedulingOpr> hasAppointed = schedulingOprService.list(
                new QueryWrapper<SchedulingOpr>()
                        .in("id", idList)
                        .eq("appoint", 1)
        );

        if (!hasAppointed.isEmpty()) {
            return Result.error("存在已预约的教学计划，不可批量删除");
        }

        // 执行删除
        schedulingOprService.removeByIds(idList);
        return Result.ok("删除成功");
    }

    /**
     * 通过id查询
     */
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id") String id) {
        SchedulingOpr schedulingOpr = schedulingOprService.getById(id);
        return Result.ok(schedulingOpr);
    }

    /**
     * 发布/撤回发布
     */
    @RequestMapping(value = "/pubOrCancel", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<?> pubOrCancel(@RequestBody OprParams rec) {
        List<String> list = Arrays.asList(rec.getIds().split(","));
        if (rec.getPublish() == 0) {
            long count = schedulingApplyService.count(
                    Wrappers.<SchedulingApply>lambdaQuery()
                            .in(SchedulingApply::getClassId, list)
                            .eq(SchedulingApply::getCancel, 0)
            );
            if (count > 0) {
                return Result.error("所选数据存在已经预约数据，不能取消发布!");
            }
        }
        for (String id : rec.getIds().split(",")) {
            schedulingOprService.update(
                    Wrappers.<SchedulingOpr>lambdaUpdate()
                            .set(SchedulingOpr::getPublish, rec.getPublish())
                            .set(SchedulingOpr::getPublishTime, rec.getPublish() == 1 ? new Date() : null)
                            .eq(SchedulingOpr::getId, id)
            );
        }
        return Result.ok("操作成功!");
    }

    /**
     * 批量取消发布
     */
    @PostMapping("/batchCancel")
    public Result<?> batchCancel(@RequestBody OprParams params) {
        if (StringUtils.isEmpty(params.getIds())) {
            return Result.error("请选择要取消发布的记录");
        }

        List<String> idList = Arrays.asList(params.getIds().split(","));

        // 检查是否存在已预约的记录
        boolean hasAppointed = !schedulingApplyService.list(
                Wrappers.<SchedulingApply>lambdaQuery()
                        .in(SchedulingApply::getClassId, idList)
                        .eq(SchedulingApply::getCancel, 0)
        ).isEmpty();

        if (hasAppointed) {
            return Result.error("存在已预约的教学计划，不可批量取消发布");
        }

        // 执行批量更新
        schedulingOprService.update(
                Wrappers.<SchedulingOpr>lambdaUpdate()
                        .in(SchedulingOpr::getId, idList)
                        .set(SchedulingOpr::getPublish, 0)
                        .set(SchedulingOpr::getPublishTime, null)
        );

        return Result.ok("取消发布成功");
    }

    /**
     * zlb 分页列表查询
     *
     * @param rec 查询参数对象
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @return 分页查询结果
     */
    @GetMapping(value = "/zlb/list")
    public Result<?> zlbList(SchedulingOpr rec,
                             @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                             @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        // 获取学员信息
        Studentinfo stu = ZlbStuUtils.getStuByZlbId(rec.getId());
        if (Objects.isNull(stu)) {
            return Result.error("请先报名！");
        }

        // 判断是否为当天查询，如果是当天查询则只显示当前时间之后的课程
        String nowHHmm = LocalTime.now().toString().substring(0, 5);
        String today = LocalDate.now().toString();
        String recDate = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, rec.getClassDate());
        boolean isToday = today.equals(recDate);

        // 构建查询条件
        LambdaQueryWrapper<SchedulingOpr> queryWrapper = Wrappers.lambdaQuery();
        // 根据学员所在驾校过滤
        queryWrapper.eq(SchedulingOpr::getInscode, stu.getInscode());
        queryWrapper.eq(StringUtils.isNotBlank(rec.getCoachnum()), SchedulingOpr::getCoachnum, rec.getCoachnum());
        String traintype = Objects.equals(stu.getTraintype(), "C2") ? "C1,C2" :stu.getTraintype();
        queryWrapper.in(SchedulingOpr::getTraintype, Arrays.asList(traintype.split(",")));
        // 根据科目过滤
        queryWrapper.eq(Objects.nonNull(rec.getSubject()), SchedulingOpr::getSubject, rec.getSubject());
        // 根据课程日期过滤
        queryWrapper.eq(SchedulingOpr::getClassDate, rec.getClassDate());
        // 如果是当天查询，只显示当前时间之后的课程
        queryWrapper.ge(isToday, SchedulingOpr::getStartTime, nowHHmm);
        // 只显示已发布的课程
        queryWrapper.eq(SchedulingOpr::getPublish, 1);
        // 按开始时间升序排序
        queryWrapper.orderByAsc(SchedulingOpr::getStartTime);

        // 执行分页查询
        Page<SchedulingOpr> page = new Page<>(pageNo, pageSize);
        IPage<SchedulingOpr> pageList = schedulingOprService.page(page, queryWrapper);
        if (pageList.getRecords().isEmpty()) {
            return Result.ok(pageList);
        }

        // 将查询结果转换为前端显示所需的VO对象
        IPage<ZlbOprVo> res = new Page<>(pageList.getCurrent(), pageList.getSize(), pageList.getTotal());
        List<ZlbOprVo> zlbOprVos = BeanConvertUtils.convertListTo(pageList.getRecords(), ZlbOprVo::new);
        res.setRecords(zlbOprVos);

        // 查询学员是否已经预约过这些课程
        List<String> ids = pageList.getRecords().stream().map(SchedulingOpr::getId).collect(Collectors.toList());
        List<SchedulingApply> list = schedulingApplyService.list(
                Wrappers.<SchedulingApply>lambdaQuery()
                        .eq(SchedulingApply::getStunum, stu.getStunum())
                        .eq(SchedulingApply::getCancel, 0)  // 只查询未取消的预约
                        .in(SchedulingApply::getClassId, ids)
        );

// 如果没有预约记录，直接返回结果
//        if (list.isEmpty()) {
//            return Result.ok(res);
//        }
        // 标记已经预约过的课程
        Map<String, Integer> map = list.stream().collect(Collectors.toMap(SchedulingApply::getClassId, SchedulingApply::getCancel));
        for (ZlbOprVo record : zlbOprVos) {
            Integer isCancel = map.get(String.valueOf(record.getId()));
            if (isCancel != null) {
                // 设置已预约标记
                record.setAlready(1);
            }
            if(Objects.equals(record.getTraintype(), "C1")){
                record.setTraintype("C1,C2");
            }
        }

        return Result.ok(res);
    }

    /**
     * 添加
     */
    @PostMapping(value = "/zlb/coach/add")
    public Result<?> zlbAdd(@RequestBody SchedulingOpr rec) {
        Coach coach = ZlbStuUtils.getCoachByZlbId(rec.getId());
        if (Objects.isNull(coach)) {
            return Result.error("无教练员信息");
        }

        // 验证教练状态是否符合要求
        if (!Objects.equals(coach.getStopTrain(), 0L)) {
            return Result.error("教练已停训，无法添加排班");
        }

        if (!StringUtils.equals(coach.getEmploystatus(), "0")) {
            return Result.error("教练非在职状态，无法添加排班");
        }

        if (!Objects.equals(coach.getAuditstate(), 1L)) {
            return Result.error("教练未通过审核，无法添加排班");
        }

        if (!Objects.equals(coach.getStatus(), 1L)) {
            return Result.error("教练未备案，无法添加排班");
        }

        // 验证教练是否为实操教练（带教类型包含实操：4、5、6、7）
        if (coach.getTeachtype() == null ||
            !(coach.getTeachtype() == 4 || coach.getTeachtype() == 5 ||
              coach.getTeachtype() == 6 || coach.getTeachtype() == 7)) {
            return Result.error("只有实操教练才可以排班");
        }

        rec.setId(null).setInscode(coach.getInscode()).setCoachnum(coach.getCoachnum());
        List<String> timeSlots = timeSlot(rec.getClassTime());

        // 多日批量添加
        String classDateEnd = DateUtils.addDays(rec.getClassDateEnd(), 1);
        String classDateStart = rec.getClassDateStart();
        while (!classDateStart.equals(classDateEnd)) {
            // 剔除教练当日的重复时段
            List<SchedulingOpr> list = schedulingOprService.list(
                    Wrappers.<SchedulingOpr>lambdaQuery()
                            .eq(SchedulingOpr::getCoachnum, rec.getCoachnum())
                            .eq(SchedulingOpr::getClassDate, classDateStart)
            );
            List<String> collect = list.stream().map(SchedulingOpr::getClassTime).collect(Collectors.toList());
            List<String> availableTimes = availableTime(collect, timeSlots);
            List<SchedulingOpr> oprList = new ArrayList<>();
            for (String timeSlot : availableTimes) {
                SchedulingOpr one = new SchedulingOpr();
                BeanUtils.copyProperties(rec, one);
                one.setClassDate(DateUtils.parseDate(classDateStart, DateUtils.YYYY_MM_DD));
                one.setClassTime(timeSlot);
                String[] split = timeSlot.split("-");
                one.setStartTime(split[0]).setEndTime(split[1]);
                one.setPublish(1).setPublishTime(new Date());
                oprList.add(one);
            }
            schedulingOprService.saveBatch(oprList);
            classDateStart = DateUtils.addDays(classDateStart, 1);
        }
        return Result.ok("添加成功！");
    }

    /**
     * 大时间段分割为小时间点
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 小时间点 list
     */
    public List<LocalTime> splitTimesByHour(LocalTime startTime, LocalTime endTime) {
        List<LocalTime> times = new ArrayList<>();
        // 如果结束时间在开始时间之前，则不处理
        if (endTime.isBefore(startTime)) {
            return times;
        }
        // 添加开始时间
        times.add(startTime);
        // 按小时分割
        while (times.get(times.size() - 1).isBefore(endTime)) {
            LocalTime nextTime = times.get(times.size() - 1).plusHours(1);
            // 避免超过结束时间
            if (nextTime.isAfter(endTime)) {
                break;
            }
            times.add(nextTime);
        }
        return times;
    }

    /**
     * 大时间段分割为成小时间段
     *
     * @param classTime 大时间段
     * @return 小时间段 list
     */
    public List<String> timeSlot(String classTime) {
        LocalTime startLimit = LocalTime.parse("05:00", DateTimeFormatter.ofPattern("HH:mm"));
        LocalTime endLimit = LocalTime.parse("22:00", DateTimeFormatter.ofPattern("HH:mm"));
        String[] split = classTime.split("-");
        LocalTime startTime = LocalTime.parse(split[0], DateTimeFormatter.ofPattern("HH:mm"));
        LocalTime endTime = LocalTime.parse(split[1], DateTimeFormatter.ofPattern("HH:mm"));
        if (startTime.isBefore(startLimit) || endTime.isAfter(endLimit)) {
            throw new JkyCdtpublicException("教学时间应在05:00-22:00内");
        }
        List<LocalTime> localTimes = splitTimesByHour(startTime, endTime);
        List<String> res = new ArrayList<>();
        for (int i = 0; i < localTimes.size() - 1; i++) {
            res.add(localTimes.get(i).toString() + "-" + localTimes.get(i + 1).toString());
        }
        return res;
    }

    /**
     * 筛选出不重复的时间段
     *
     * @param exists    已存在时间段
     * @param timeSlots 传入时间
     * @return 不重复的时间段
     */
    public List<String> availableTime(List<String> exists, List<String> timeSlots) {
        if (CollectionUtils.isEmpty(exists)) {
            return timeSlots;
        }
        List<String> res = new ArrayList<>();
        for (String timeSlot : timeSlots) {
            boolean flag = true;
            LocalTime newStart = LocalTime.parse(timeSlot.split("-")[0], DateTimeFormatter.ISO_LOCAL_TIME);
            LocalTime newEnd = LocalTime.parse(timeSlot.split("-")[1], DateTimeFormatter.ISO_LOCAL_TIME);
            for (String exist : exists) {
                LocalTime start = LocalTime.parse(exist.split("-")[0], DateTimeFormatter.ISO_LOCAL_TIME);
                LocalTime end = LocalTime.parse(exist.split("-")[1], DateTimeFormatter.ISO_LOCAL_TIME);
                if (start.isBefore(newEnd) && end.isAfter(newStart)) {
                    flag = false;
                    break;
                }
            }
            if (flag) {
                res.add(timeSlot);
            }
        }
        return res;
    }

    /**
     * 教练端查询实操课程
     */
    @GetMapping(value = "/zlb/coach/list")
    public Result<?> zlbCoachList(SchedulingOpr rec) {
        Coach coach = ZlbStuUtils.getCoachByZlbId(rec.getId());
        if (Objects.isNull(coach)) {
            return Result.error("请先登录浙里办");
        }
        List<SchedulingOpr> list = schedulingOprService.list(
                Wrappers.<SchedulingOpr>lambdaQuery()
                        .eq(SchedulingOpr::getInscode, coach.getInscode())
                        .eq(SchedulingOpr::getClassDate, rec.getClassDate())
                        .eq(SchedulingOpr::getCoachnum, coach.getCoachnum())
                        .eq(SchedulingOpr::getPublish, 1)
                        .orderByDesc(SchedulingOpr::getStartTime)
        );
        return Result.ok(list);
    }

    /**
     * 一键发布当天生成的计划
     */
    @RequestMapping(value = "/publishToday", method = {RequestMethod.POST})
    public Result<?> publishToday() {
        // 获取当前日期字符串 yyyy-MM-dd
        String today = DateUtils.getDate();

        // 查询当天创建且未发布的计划
        List<SchedulingOpr> todayPlans = schedulingOprService.list(
                Wrappers.<SchedulingOpr>lambdaQuery()
                        .apply("cast(create_time as date) = cast({0} as date)", today)
                        .eq(SchedulingOpr::getPublish, 0)
        );

        if (todayPlans.isEmpty()) {
            return Result.error("当天没有未发布的计划！");
        }

        // 批量更新发布状态
        schedulingOprService.update(
                Wrappers.<SchedulingOpr>lambdaUpdate()
                        .set(SchedulingOpr::getPublish, 1)
                        .set(SchedulingOpr::getPublishTime, new Date())
                        .apply("cast(create_time as date) = cast({0} as date)", today)
                        .eq(SchedulingOpr::getPublish, 0)
        );

        return Result.ok("发布成功，共发布" + todayPlans.size() + "条计划！");
    }
}
