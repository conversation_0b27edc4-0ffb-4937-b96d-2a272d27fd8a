package com.jky.boot.core.module.sxgzpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jky.crypto.annotation.DesensitizedFieldAnno;
import com.jky.crypto.annotation.IgnoreScanAnno;
import com.jky.crypto.enums.DesensitizedTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: t_m_evaluation
 * @Author: jeecg-boot
 * @Date:   2023-08-11
 * @Version: V1.0
 */
@Data
@TableName("t_m_evaluation")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="t_m_evaluation对象", description="t_m_evaluation")
public class TMEvaluation implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private String id;

    /**姓名*/
    @Excel(name = "评价对象名称", width = 15)
    @ApiModelProperty(value = "评价对象名称")
    @DesensitizedFieldAnno(DesensitizedTypeEnum.CHINESE_NAME)
    private String name;
//    /**统一编号*/
//    @Excel(name = "统一编号", width = 15)
//    @ApiModelProperty(value = "统一编号")
//    private java.lang.String codenum;


    /**培训机构编号 org_code*/
    @ApiModelProperty(value = "培训机构编号")
    private String inscode;

    @Excel(name = "驾校名称", width = 15)
    @ApiModelProperty(value = "驾校名称")
    private String insname;
	/**stunum*/
//	@Excel(name = "学员编号", width = 15)
    @ApiModelProperty(value = "学员编号")
    private String stunum;
//	/**评价对象*/
//	@Excel(name = "评价对象", width = 15)
//    @ApiModelProperty(value = "评价对象")
//    @Dict(dicCode = "evaluation_object")
//    private String evalobject;
	/**评价类型*/
//	@Excel(name = "评价类型", width = 15)
//    @ApiModelProperty(value = "评价类型")
//    @Dict(dicCode = "evaluation_type")
//    private String type;
	/**总体满意度 1:一星2:二星3:三星4:四星5:五星（最满意）*/
	@Excel(name = "总体满意度 1:一星2:二星3:三星4:四星5:五星（最满意）", width = 15)
    @ApiModelProperty(value = "总体满意度 1:一星2:二星3:三星4:四星5:五星（最满意）")
    @Dict(dicCode = "evaluate_satisfaction")
    private Integer overall;
	/**培训部分 1:第一部分2:第二部分3:第三部分4:第四部分*/
	@Excel(name = "培训部分 1:第一部分2:第二部分3:第三部分4:第四部分5:总体", width = 15)
    @ApiModelProperty(value = "培训部分 1:第一部分2:第二部分3:第三部分4:第四部分5:总体")
    @Dict(dicCode = "sys_km")
    private Integer part;

    /** 教学质量评分*/
    @Excel(name = "教学质量评分", width = 15)
	private Double teachingQuality;
    /** 教学态度评分*/
    @Excel(name = "教学态度评分", width = 15)
	private Double teachingAttitude;
    /** 车辆状况评分*/
    @Excel(name = "车辆状况评分", width = 15)
	private Double vehicleCondition;
    /** 教学环境评分*/
    @Excel(name = "教学环境评分", width = 15)
	private Double trainingEnvironment;
	/**评价时间 YYYYMMDDhhmmss*/
	@Excel(name = "评价时间 yyyy-MM-dd HH:mm:ss", width = 15)
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "评价时间 yyyy-MM-dd HH:mm:ss")
    private Date evaluatetime;
	/**评价用语列表 英文逗号分隔*/
	@Excel(name = "评价用语列表 英文逗号分隔", width = 15)
    @ApiModelProperty(value = "评价用语列表 英文逗号分隔")
    private String srvmanner;
	/**个性化评价*/
	@Excel(name = "个性化评价", width = 15)
    @ApiModelProperty(value = "个性化评价")
    private String personaleval;

    @TableField(exist = false)
    private String evaluatetimeBegin;
    @TableField(exist = false)
    private String evaluatetimeEnd;
    @TableField(exist = false)
    @IgnoreScanAnno
    private List<String> inscodeList;

	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
	/**创建者*/
    @ApiModelProperty(value = "创建者")
    private String createBy;
	/**更新者*/
    @ApiModelProperty(value = "更新者")
    private String updateBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

}
