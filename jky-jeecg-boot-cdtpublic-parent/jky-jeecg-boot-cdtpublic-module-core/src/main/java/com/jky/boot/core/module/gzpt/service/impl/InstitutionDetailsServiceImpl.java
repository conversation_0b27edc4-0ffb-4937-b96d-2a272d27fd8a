package com.jky.boot.core.module.gzpt.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jky.boot.core.module.gzpt.entity.InstitutionDetails;
import com.jky.boot.core.module.gzpt.mapper.InstitutionDetailsMapper;
import com.jky.boot.core.module.gzpt.service.IInstitutionDetailsService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description: 培训机构附加信息对象
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
@Service
public class InstitutionDetailsServiceImpl extends ServiceImpl<InstitutionDetailsMapper, InstitutionDetails> implements IInstitutionDetailsService {

    @Autowired
    private InstitutionDetailsMapper institutionDetailsMapper;

    /**
     * 查询培训机构附加信息
     *
     * @param inscode 培训机构附加信息主键
     * @return 培训机构附加信息
     */
    @Override
    public InstitutionDetails selectInstitutionDetailsByInscode(String inscode) {
        return institutionDetailsMapper.selectInstitutionDetailsByInscode(inscode);
    }

    /**
     * 查询培训机构附加信息列表
     *
     * @param institutionDetails 培训机构附加信息
     * @return 培训机构附加信息
     */
    @Override
    public List<InstitutionDetails> selectInstitutionDetailsList(InstitutionDetails institutionDetails) {
        return institutionDetailsMapper.selectInstitutionDetailsList(institutionDetails);
    }

    /**
     * 新增培训机构附加信息
     *
     * @param institutionDetails 培训机构附加信息
     * @return 结果
     */
    @Override
    public int insertInstitutionDetails(InstitutionDetails institutionDetails) {
        return institutionDetailsMapper.insertInstitutionDetails(institutionDetails);
    }

    /**
     * 修改培训机构附加信息
     *
     * @param institutionDetails 培训机构附加信息
     * @return 结果
     */
    @Override
    public int updateInstitutionDetails(InstitutionDetails institutionDetails) {
        return institutionDetailsMapper.updateInstitutionDetails(institutionDetails);
    }

    /**
     * 批量删除培训机构附加信息
     *
     * @param inscodes 需要删除的培训机构附加信息主键
     * @return 结果
     */
    @Override
    public int deleteInstitutionDetailsByInscodes(String[] inscodes) {
        return institutionDetailsMapper.deleteInstitutionDetailsByInscodes(inscodes);
    }

    /**
     * 删除培训机构附加信息信息
     *
     * @param inscode 培训机构附加信息主键
     * @return 结果
     */
    @Override
    public int deleteInstitutionDetailsByInscode(String inscode) {
        return institutionDetailsMapper.deleteInstitutionDetailsByInscode(inscode);
    }
}
