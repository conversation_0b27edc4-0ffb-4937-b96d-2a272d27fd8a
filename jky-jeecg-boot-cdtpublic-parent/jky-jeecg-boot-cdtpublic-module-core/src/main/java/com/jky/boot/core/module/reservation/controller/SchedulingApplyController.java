package com.jky.boot.core.module.reservation.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.annotation.RedissonLock;
import com.jky.boot.base.dao.*;
import com.jky.boot.base.service.*;
import com.jky.boot.common.utils.CityUtil;
import com.jky.boot.common.utils.CommonResponse;
import com.jky.boot.common.utils.DateUtils;
import com.jky.boot.common.utils.EnvControl;
import com.jky.boot.core.module.gzpt.entity.*;
import com.jky.boot.core.module.gzpt.service.*;
import com.jky.boot.core.module.gzpt.utils.PushJsUtil;
import com.jky.boot.core.module.reservation.vo.ZlbApplyVo;
import com.jky.boot.core.module.reservation.vo.ZlbSchApplyVo;
import com.jky.boot.core.util.ZlbStuUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.boot.starter.lock.client.RedissonLockClient;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 课堂排班学员预约
 *
 * <AUTHOR>
 * @version 2024-09-25
 */
@RestController
@RequestMapping("/schedule/apply")
@Slf4j
public class SchedulingApplyController {
    @Autowired
    private IBankRegService bankRegService;
    @Autowired
    private IStudentinfoService studentInfoService;
    @Autowired
    private ISchedulingClassService schedulingClassService;
    @Autowired
    private ISchedulingImiService schedulingImiService;
    @Autowired
    private ISchedulingOprService schedulingOprService;
    @Autowired
    private ISchedulingApplyService schedulingApplyService;
    @Autowired
    private RedissonLockClient redissonLock;
    @Autowired
    private IOpenClassStudentService openClassStudentService;
    @Autowired
    private IBankOrderService bankOrderService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private IStageTrainningTimeService stageTrainningTimeService;
    @Autowired
    private IClassRecordDetailService classRecordDetailService;
    @Autowired
    private ITrainSubjectCreditService trainSubjectCreditService;
    /**
     * 分页列表查询
     */
    @GetMapping(value = "/list")
    public Result<?> queryPageList(SchedulingApply schedulingApply,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        if (StringUtils.isEmpty(schedulingApply.getClassId())) {
            return Result.error("缺少必要参数");
        }
        QueryWrapper<SchedulingApply> queryWrapper = QueryGenerator.initQueryWrapper(schedulingApply, req.getParameterMap());
        queryWrapper.eq("cancel", 0);
        Page<SchedulingApply> page = new Page<>(pageNo, pageSize);
        IPage<SchedulingApply> pageList = schedulingApplyService.page(page, queryWrapper);
        return Result.ok(pageList);
    }

    /**
     * 驾校查看本驾校所有预约
     */
    @GetMapping(value = "/list/all")
    public Result<?> listAll(SchedulingApply rec,
                             @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                             @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        Page<ZlbSchApplyVo> page = new Page<>(pageNo, pageSize);
        IPage<ZlbSchApplyVo> pageList = schedulingApplyService.pcPage(page, rec);
        return Result.ok(pageList);
    }

    /**
     * 添加（仅课堂开班使用）
     */
    @Transactional
    @PostMapping(value = "/add")
    @RedissonLock(key = "#rec.classId", keyPrefix = "schedule:apply:", waitTime = 1, lockTime = 5)
    public Result<String> add(@RequestBody SchedulingApply rec) {
        if (StringUtils.isBlank(rec.getStunum())) {
            return Result.error("请获取学员编号之后添加");
        }
        // 校验冻结状态
        BankReg bankReg = bankRegService.getOneByStuNum(rec.getStunum());
        if (Objects.isNull(bankReg) || bankReg.getStatus() != 3) {
            return Result.error("请冻结完成后预约");
        }
        // 校验是否已经添加学员
        long count = schedulingApplyService.count(
                Wrappers.<SchedulingApply>lambdaQuery()
                        .eq(SchedulingApply::getClassId, rec.getClassId())
                        .eq(SchedulingApply::getStunum, rec.getStunum())
                        .eq(SchedulingApply::getCancel, 0)
        );
        if (count > 0) {
            return Result.error("已添加该学员！");
        }
        // 校验时间和人数
        SchedulingClass byId = schedulingClassService.getById(rec.getClassId());
        if (byId.getCurAmount().equals(byId.getStuAmount())) {
            return Result.error("当前课程人数已到上限!");
        }
        String deadline = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, byId.getClassDate()) + " " + byId.getClassTime().split("-")[0] + ":00";
        LocalDateTime parse = LocalDateTime.parse(deadline, DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS));
        if (parse.isBefore(LocalDateTime.now())) {
            return Result.error("报名时间截止");
        }
        // 保存预约记录
        OpenClassStudent openStu = openClassStudentService.getOneByStuNum(rec.getStunum());
        rec.setStuname(openStu.getStuname());
        rec.setPhone(openStu.getPhone());
        openStu.setReservation(1);
        openClassStudentService.updateById(openStu);
        schedulingApplyService.save(rec);
        // 课程预约人数 +1
        byId.setCurAmount(byId.getCurAmount() + 1);
        schedulingClassService.updateById(byId);
        // 推送计时
        if (EnvControl.Js_SWITCH) {
            CommonResponse resp = PushJsUtil.pushReservation(byId.getInscode(), byId, rec);
            if (resp.getErrorcode() != 0) {
                throw new RuntimeException(resp.getMessage());
            }
            rec.setPushJs(1);
            schedulingApplyService.updateById(rec);
        }
        return Result.ok("添加成功！");
    }

    /**
     * pc 模拟、实操添加
     */
    @Transactional
    @PostMapping(value = "/oprImi/add")
    @RedissonLock(key = "#rec.classId", keyPrefix = "schedule:apply:", waitTime = 1, lockTime = 5)
    public Result<?> oprImiAdd(@RequestBody SchedulingApply rec) {
        Studentinfo stu = studentInfoService.getOneByStuNum(rec.getStunum());
        // 校验冻结状态
        BankReg bankReg = bankRegService.getOneByStuNum(rec.getStunum());
        if (Objects.isNull(bankReg) || bankReg.getStatus() != 3) {
            return Result.error("请冻结完成后预约");
        }
        // 校验是否已经添加学员
        long count = schedulingApplyService.count(
                Wrappers.<SchedulingApply>lambdaQuery()
                        .eq(SchedulingApply::getClassId, rec.getClassId())
                        .eq(SchedulingApply::getCancel, 0)
                        .eq(SchedulingApply::getStunum, rec.getStunum())
        );
        if (count > 0) {
            return Result.error("您已预约该课程！");
        }
        // 校验是否人满
        Object obj;
        if (rec.getType() == 1) {
            SchedulingOpr byId = schedulingOprService.getById(rec.getClassId());
            if (byId.getAppoint() == 1) {
                return Result.error("当前课程已被预约!");
            }
            if (byId.getPublish() == 0) {
                return Result.error("请在发布课程后添加学员");
            }
            //如果是机器人教学预约科目二
            if (byId.getSubject()==2 && ( Objects.nonNull(stu.getIsassistance()) &&stu.getIsassistance()==1)) {
                return Result.error("机器人教学科目二不需要预约");
            }
            String deadline = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, byId.getClassDate()) + " " + byId.getEndTime() + ":00";
            LocalDateTime parse = LocalDateTime.parse(deadline, DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS));
            if (parse.isBefore(LocalDateTime.now())) {
                return Result.error("报名时间截止");
            }
            byId.setAppoint(1);
            schedulingOprService.updateById(byId);
            obj = byId;
        } else {
            SchedulingImi byId = schedulingImiService.getById(rec.getClassId());
            if (byId.getCurAmount().equals(byId.getStuAmount())) {
                return Result.error("当前课程人数已到上限!");
            }
            if (byId.getPublish() == 0) {
                return Result.error("请在发布课程后添加学员");
            }
            String deadline = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, byId.getClassDate()) + " " + byId.getEndTime() + ":00";
            LocalDateTime parse = LocalDateTime.parse(deadline, DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS));
            if (parse.isBefore(LocalDateTime.now())) {
                return Result.error("报名时间截止");
            }
            byId.setCurAmount(byId.getCurAmount() + 1);
            schedulingImiService.updateById(byId);
            obj = byId;
        }
        rec.setPushJs(1);
        rec.setStuname(stu.getName());
        rec.setPhone(stu.getPhone());
        schedulingApplyService.save(rec);
        // 推送计时
        if (EnvControl.Js_SWITCH) {
            CommonResponse resp = PushJsUtil.pushReservation(stu.getInscode(), obj, rec);
            if (resp.getErrorcode() != 0) {
                throw new RuntimeException(resp.getMessage());
            }
        }
        return Result.ok("添加成功！");
    }

    /**
     * 编辑
     */
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody SchedulingApply schedulingApply) {
        schedulingApplyService.updateById(schedulingApply);
        return Result.ok("编辑成功!");
    }

    /**
     * 通过id删除（仅在课堂排班中使用）
     */
    @DeleteMapping(value = "/delete")
    @Transactional
    public Result<String> delete(@RequestParam(name = "id") String id) {
        // 回滚可约人数
        SchedulingApply apply = schedulingApplyService.getById(id);
        String key = "schedule:apply:" + apply.getClassId();
        if (!redissonLock.tryLock(key, 2, 5)) {
            return Result.error("系统繁忙");
        }
        // 判断可操作时间
        SchedulingClass byId = schedulingClassService.getById(apply.getClassId());
        availableTime(byId.getClassDate(), byId.getClassTime());
        byId.setCurAmount(byId.getCurAmount() - 1);
        schedulingClassService.updateById(byId);
        // 删除预约记录
        schedulingApplyService.removeById(id);
        // 开班设置未预约
        openClassStudentService.update(
                Wrappers.<OpenClassStudent>lambdaUpdate()
                        .set(OpenClassStudent::getReservation, 0)
                        .eq(OpenClassStudent::getStunum, apply.getStunum())
        );
        // 撤销申请通知计时
        if (EnvControl.Js_SWITCH) {
            Studentinfo stu = studentInfoService.getOneByStuNum(apply.getStunum());
            CommonResponse resp = PushJsUtil.cancelReservation(stu, apply.getId());
            if (resp.getErrorcode() != 0) {
                throw new RuntimeException(resp.getMessage());
            }
        }
        redissonLock.unlock(key);
        return Result.ok("删除成功!");
    }

    /**
     * 预约删除，模拟、实操使用
     */
    @DeleteMapping(value = "/oprImi/delete")
    @Transactional
    public Result<String> oprImiDelete(@RequestParam(name = "type") Integer type,
                                       @RequestParam(name = "id") String id) {
        SchedulingApply apply = schedulingApplyService.getById(id);
        String key = "schedule:apply:" + apply.getClassId();
        if (!redissonLock.tryLock(key, 2, 5)) {
            return Result.error("系统繁忙");
        }
        // 回滚可约人数
        if (type == 1) {
            SchedulingOpr byId = schedulingOprService.getById(apply.getClassId());
            availableTime(byId.getClassDate(), byId.getClassTime());
            byId.setAppoint(0);
            schedulingOprService.updateById(byId);
        } else {
            SchedulingImi byId = schedulingImiService.getById(apply.getClassId());
            availableTime(byId.getClassDate(), byId.getClassTime());
            byId.setCurAmount(byId.getCurAmount() - 1);
            schedulingImiService.updateById(byId);
        }
        // 删除预约记录
        schedulingApplyService.removeById(id);
        // 撤销申请通知计时
        if (EnvControl.Js_SWITCH) {
            Studentinfo stu = studentInfoService.getOneByStuNum(apply.getStunum());
            CommonResponse resp = PushJsUtil.cancelReservation(stu, apply.getId());
            if (resp.getErrorcode() != 0) {
                throw new RuntimeException(resp.getMessage());
            }
        }
        redissonLock.unlock(key);
        return Result.ok("删除成功!");
    }

    /**
     * 通过id查询
     */
    @GetMapping(value = "/queryById")
    public Result<SchedulingApply> queryById(@RequestParam(name = "id") String id) {
        SchedulingApply schedulingApply = schedulingApplyService.getById(id);
        if (schedulingApply == null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(schedulingApply);
    }
    /**
     * zlb-我的预约
     */
    @GetMapping(value = "/zlb/list")
    public Result<?> zlbList(SchedulingApply rec,
                             @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                             @RequestParam(name = "pageSize", defaultValue = "1000") Integer pageSize) {
        Studentinfo stu = ZlbStuUtils.getStuByZlbId(rec.getId());
        if (Objects.isNull(stu)) {
            return Result.error("请先报名！");
        }
        Page<SchedulingApply> page = new Page<>(pageNo, pageSize);
        IPage<ZlbSchApplyVo> pageList = schedulingApplyService.zlbPage(page, stu.getStunum());
        return Result.ok(pageList);
    }

    /**
     * 添加
     */
    @PostMapping(value = "/zlb/add")
    @RedissonLock(key = "#rec.classId", keyPrefix = "schedule:apply:", waitTime = 1, lockTime = 5)
    public Result<?> zlbAdd(@RequestBody SchedulingApply rec) {
        // 校验学员存在性
        Studentinfo stu = ZlbStuUtils.getStuByZlbId(rec.getId());
        if (Objects.isNull(stu)) {
            return Result.error("请先报名");
        }
        // 校验冻结状态
        BankReg bankReg = bankRegService.getOneByStuNum(stu.getStunum());
        if (Objects.isNull(bankReg) || bankReg.getStatus() != 3) {
            return Result.error("请冻结完成后预约");
        }
//        long orderNum = bankOrderService.count(
//                Wrappers.lambdaQuery(BankOrder.class)
//                        .eq(BankOrder::getStunum, stu.getStunum())
//                        .eq(BankOrder::getConfirm, 0)
//                        .in(BankOrder::getOrdertype, 1, 2, 3)
//        );
//        if (orderNum > 0) {
//            return Result.error("存在未确认的学时订单，请在 学时确认 菜单中进行确认后预约");
//        }

        //学员需全部费用扣完，学时完成，接下来要预约，必须冻结 或免单
        //1、判断免单
        String redisKey = "student:freeOrder:" + stu.getStunum();
        boolean hasKey = redisUtil.hasKey(redisKey);
        if (!hasKey) {
            if (Objects.isNull(bankReg.getRemainingAmount())||bankReg.getRemainingAmount().compareTo(BigDecimal.ZERO) <= 0) {
                return Result.error("当前冻结账户余额为0，请联系驾校进行免单操作或继续冻结对应学时的金额");
            }
//            if (bankReg.getRemainingAmount()!=null&&bankReg.getRemainingAmount().compareTo(BigDecimal.ZERO) <= 0) {
//                long stageNum = stageTrainningTimeService.count(
//                        Wrappers.lambdaQuery(StageTrainningTime.class)
//                                .eq(StageTrainningTime::getStunum, stu.getStunum())
//                                .in(StageTrainningTime::getSubject, Arrays.asList(2L, 3L))
//                );
//                if (stageNum >= 2) {
//                    return Result.error("您的学时已完成，对应账户余额为0，请联系驾校进行免单操作或继续冻结对应学时的金额");
//                }
//            }
        }

        rec.setStunum(stu.getStunum());
        // 校验是否已经添加学员
        long count = schedulingApplyService.count(
                Wrappers.<SchedulingApply>lambdaQuery()
                        .eq(SchedulingApply::getClassId, rec.getClassId())
                        .eq(SchedulingApply::getCancel, 0)
                        .eq(SchedulingApply::getStunum, stu.getStunum())
        );
        if (count > 0) {
            return Result.error("您已预约该课程！");
        }
        rec.setId(String.valueOf(IdWorker.getId()));
        // 校验是否人满
        // 课程类型 1实操 2课堂 3模拟
        if (rec.getType() == 1) {
            SchedulingOpr byId = schedulingOprService.getById(rec.getClassId());
            if (byId.getAppoint() == 1) {
                return Result.error("当前课程已被预约!");
            }
            //如果是机器人教学预约科目二
            if (byId.getSubject()==2 && ( Objects.nonNull(stu.getIsassistance()) &&stu.getIsassistance()==1)) {
                return Result.error("机器人教学科目二不需要预约");
            }

            //  科二实操/科三实操 单独限制：
            //    1、课堂 模拟钱全部扣完 + 科目一报审+模拟学时完结
            //    3、如果是二笔冻结的学员（智能机器人） ，目前阶段必须是 22二笔冻结第二笔（科二+科三实操）
            //    2、如果是三笔冻结的学员 ，目前阶段必须是 科二实操冻结（第二笔） 或者  科三实操冻结（第三笔）

            if(StringUtils.equalsIgnoreCase(bankReg.getRegState(),"21")||StringUtils.equalsIgnoreCase(bankReg.getRegState(),"31")){
                return Result.error("请先学完 课堂+模拟课程后，再来预约实操课程");
            }
            long stage1Num = stageTrainningTimeService.count(
                    Wrappers.lambdaQuery(StageTrainningTime.class)
                            .eq(StageTrainningTime::getStunum, stu.getStunum())
                            .eq(StageTrainningTime::getSubject, 1L)
            );
            if (stage1Num <= 0) {
                return Result.error("科目一未报审!");
            }
            String applyDate = stu.getApplydate().replaceAll("-", "");
            TrainSubjectCredit param = new TrainSubjectCredit();
            param.setSubject(stu.getSubject());
            param.setType(CityUtil.getSubjectCreditType(applyDate));//大纲类型
            param.setTrainCarType(stu.getTraintype());
            List<TrainSubjectCredit> trainSubjectCreditList = trainSubjectCreditService.list();
            trainSubjectCreditList = trainSubjectCreditList.stream().filter(item -> item.getType().equals(param.getType()) && item.getTrainCarType().equals(param.getTrainCarType()) && item.getSubject().equals(param.getSubject())).collect(Collectors.toList());
            //额定模拟学时
            long simulatorTotalTime = trainSubjectCreditList.stream().filter(a -> a.getClasstype() == 3L ).mapToLong(TrainSubjectCredit::getCreditration).sum();

            QueryWrapper<ClassRecordDetail> queryWrappeer = new QueryWrapper<>();
            queryWrappeer.eq("stunum", stu.getStunum());
            queryWrappeer.apply("SUBSTR(SUBJCODE,1,1) = '3'");
            queryWrappeer.select("sum(duration_y) durationY");
            ClassRecordDetail lsae = classRecordDetailService.getOne(queryWrappeer);

            //汇总模拟学时
            long simulatorTime=0;
            if(Objects.nonNull(lsae)&&Objects.nonNull(lsae.getDurationY())){
                simulatorTime=lsae.getDurationY().longValue();
            }

            if(simulatorTime<simulatorTotalTime){
                return Result.error("模拟学时不达标,请先学完模拟课程后,再来预约实操课程,当前模拟学时："+simulatorTime+";额定模拟学时："+simulatorTotalTime+" ");
            }

            // 推送计时
            if (EnvControl.Js_SWITCH) {
                CommonResponse resp = PushJsUtil.pushReservation(stu.getInscode(), byId, rec);
                if (resp.getErrorcode() != 0) {
                    return Result.error(resp.getMessage());
                }
            }
            byId.setAppoint(1);
            schedulingOprService.updateById(byId);
        } else if (rec.getType() == 2) {
            OpenClassStudent openClassStudent = openClassStudentService.getOneByStuNum(stu.getStunum());
            if (Objects.isNull(openClassStudent) || openClassStudent.getAuditStatus() != 1) {
                return Result.error("请在开班且审核成功后预约课堂教学");
            }
            SchedulingClass byId = schedulingClassService.getById(rec.getClassId());
            if (byId.getCurAmount().equals(byId.getStuAmount())) {
                return Result.error("当前课程人数已到上限!");
            }
            // 推送计时
            if (EnvControl.Js_SWITCH) {
                CommonResponse resp = PushJsUtil.pushReservation(stu.getInscode(), byId, rec);
                if (resp.getErrorcode() != 0) {
                    return Result.error(resp.getMessage());
                }
            }
            byId.setCurAmount(byId.getCurAmount() + 1);
            schedulingClassService.updateById(byId);
        } else {
            SchedulingImi byId = schedulingImiService.getById(rec.getClassId());
            if (byId.getCurAmount().equals(byId.getStuAmount())) {
                return Result.error("当前课程人数已到上限!");
            }
            // 推送计时
            if (EnvControl.Js_SWITCH) {
                CommonResponse resp = PushJsUtil.pushReservation(stu.getInscode(), byId, rec);
                if (resp.getErrorcode() != 0) {
                    return Result.error(resp.getMessage());
                }
            }
            byId.setCurAmount(byId.getCurAmount() + 1);
            schedulingImiService.updateById(byId);
        }
        rec.setPushJs(1);
        rec.setStuname(stu.getName());
        rec.setPhone(stu.getPhone());
        boolean save = schedulingApplyService.save(rec);
        if(save){
            //删除免单标记
            redisUtil.del(redisKey);
        }
        return Result.ok("添加成功！");
    }

    /**
     * 预约取消
     */
    @PostMapping(value = "/zlb/cancel")
    @Transactional
    public Result<?> cancel(@RequestBody ZlbApplyVo rec) {
        // 校验学员存在性
        Studentinfo stu = ZlbStuUtils.getStuByZlbId(rec.getId());
        if (Objects.isNull(stu)) {
            return Result.error("请先报名");
        }
        // 操作合法性校验
        SchedulingApply apply = schedulingApplyService.getById(rec.getApplyId());
        if (!stu.getStunum().equals(apply.getStunum())) {
            return Result.error("操作非法");
        }
        if (apply.getCancel() == 1) {
            return Result.error("预约已取消，请勿重复操作");
        }
        String key = "schedule:apply:" + apply.getClassId();
        if (!redissonLock.tryLock(key, 2, 6)) {
            return Result.error("系统繁忙");
        }
        // 校验时间
        if (apply.getType() == 1) {
            SchedulingOpr byId = schedulingOprService.getById(apply.getClassId());
            if (new Date().after(byId.getClassDate())) {
                return Result.error("超过可取消时间");
            }
            byId.setAppoint(0);
            schedulingOprService.updateById(byId);
        } else if (apply.getType() == 2) {
            SchedulingClass byId = schedulingClassService.getById(apply.getClassId());
            if (new Date().after(byId.getClassDate())) {
                return Result.error("超过可取消时间");
            }
            byId.setCurAmount(byId.getCurAmount() - 1);
            schedulingClassService.updateById(byId);
            // 开班设置未预约
            openClassStudentService.update(
                    Wrappers.<OpenClassStudent>lambdaUpdate()
                            .set(OpenClassStudent::getReservation, 0)
                            .eq(OpenClassStudent::getStunum, apply.getStunum())
            );
        } else if (apply.getType() == 3) {
            SchedulingImi byId = schedulingImiService.getById(apply.getClassId());
            if (new Date().after(byId.getClassDate())) {
                return Result.error("超过可取消时间");
            }
            byId.setCurAmount(byId.getCurAmount() - 1);
            schedulingImiService.updateById(byId);
        }
        apply.setCancel(1).setPaid(9);
        schedulingApplyService.updateById(apply);

        // 推送计时
        if (EnvControl.Js_SWITCH) {
            if (apply.getPushJs() == 1) {
                CommonResponse resp = PushJsUtil.cancelReservation(stu, apply.getId());
                if (resp.getErrorcode() != 0) {
                    throw new RuntimeException("取消预约推送计时失败，请稍后再试");
                }
            }
        }
        redissonLock.unlock(key);
        return Result.ok("取消完成");
    }

    /**
     * 判断可操作时间
     */
    private void availableTime(Date classDate, String classTime) {
        String startTime = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, classDate) + " " + classTime.split("-")[0] + ":00";
        LocalDateTime parse = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern(DateUtils.YYYY_MM_DD_HH_MM_SS));
        if (parse.isBefore(LocalDateTime.now())) {
            throw new RuntimeException("操作截止");
        }
    }
}
