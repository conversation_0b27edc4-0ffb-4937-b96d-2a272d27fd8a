package com.jky.boot.core.module.gzpt.mapper;

import java.util.List;

import com.jky.boot.core.module.gzpt.vo.JhMsgsend;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.jky.boot.core.module.gzpt.entity.Bank;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 资金托管银行管理
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
@Mapper
public interface BankMapper extends BaseMapper<Bank> {
    /**
     *  建行msgsend
     * @return 返回值
     */
    int saveJhMegsend(JhMsgsend jhMsgsend);

    List<Bank> selectBankList(Bank bank);
}
