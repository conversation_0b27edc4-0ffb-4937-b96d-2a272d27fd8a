package com.jky.boot.core.module.gzpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: t_m_institution_fraction
 * @Author: jeecg-boot
 * @Date:   2023-06-21
 * @Version: V1.0
 */
@Data
@TableName("t_m_institution_fraction")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="t_m_institution_fraction对象", description="t_m_institution_fraction")
public class TMInstitutionFraction implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private String id;
	/**驾校编号*/
	@Excel(name = "驾校编号", width = 15)
    @ApiModelProperty(value = "驾校编号")
    private String inscode;
	/**驾校名称*/
	@Excel(name = "驾校名称", width = 15)
    @ApiModelProperty(value = "驾校名称")
    private String insname;
	/**县区编号*/
	@Excel(name = "县区编号", width = 15)
    @ApiModelProperty(value = "县区编号")
    private String district;
	/**类型:1.信用评价 2.市场信用 3.服务信用 4.安全信用 5.公共信用 6.加分项*/
	@Excel(name = "类型:1.信用评价 2.市场信用 3.服务信用 4.安全信用 5.公共信用 6.加分项", width = 15)
    @ApiModelProperty(value = "类型:1.信用评价 2.市场信用 3.服务信用 4.安全信用 5.公共信用 6.加分项")
    private BigDecimal type;
	/**总分*/
	@Excel(name = "总分", width = 15)
    @ApiModelProperty(value = "总分")
    private BigDecimal fraction;
	/**累计评分*/
	@Excel(name = "累计评分", width = 15)
    @ApiModelProperty(value = "累计评分")
    private BigDecimal totalFraction;
	/**驾校信用分*/
	@Excel(name = "驾校信用分", width = 15)
    @ApiModelProperty(value = "驾校信用分")
    private BigDecimal insTotalFraction;
	/**crdate*/
	@Excel(name = "crdate", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "crdate")
    private Date crdate;
	/**createTime*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "createTime")
    private Date createTime;
	/**createBy*/
    @ApiModelProperty(value = "createBy")
    private String createBy;
	/**updateTime*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "updateTime")
    private Date updateTime;
	/**updateBy*/
    @ApiModelProperty(value = "updateBy")
    private String updateBy;
	/**信用等级*/
	@Excel(name = "信用等级", width = 15)
    @ApiModelProperty(value = "信用等级")
    private String creditLevel;
}
