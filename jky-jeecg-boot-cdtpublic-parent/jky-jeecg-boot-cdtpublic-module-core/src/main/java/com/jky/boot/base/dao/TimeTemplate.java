package com.jky.boot.base.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 时间模板
 *
 * <AUTHOR>
 * @version 2025-01-06
 */
@Data
@TableName("time_template")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TimeTemplate {

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    /**
     * 驾校编号
     */
    private String inscode;
    /**
     * 模板名称
     */
    private String templateName;
    /**
     * 教学计划类型 1-实车 2-课堂 3-模拟
     */
    private Integer teachType;
}
