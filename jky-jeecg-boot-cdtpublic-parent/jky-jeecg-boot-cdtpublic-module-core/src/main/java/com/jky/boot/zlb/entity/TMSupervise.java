package com.jky.boot.zlb.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: t_m_supervise
 * @Author: jeecg-boot
 * @Date:   2023-06-20
 * @Version: V1.0
 */
@Data
@TableName("newjgpt.t_m_supervise")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="t_m_supervise对象", description="t_m_supervise")
public class TMSupervise implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    /**对象编号*/
    @Excel(name = "对象编号", width = 15)
    @ApiModelProperty(value = "对象编号")
    private String objnum;
    /**对象名称*/
    @Excel(name = "对象名称", width = 15)
    @ApiModelProperty(value = "对象名称")
    private String objname;
    /**对象类型 0.培训机构 1.教练员 2.教练车*/
    @Excel(name = "对象类型 0.培训机构 1.教练员 2.教练车", width = 15)
    @ApiModelProperty(value = "对象类型 0.培训机构 1.教练员 2.教练车")
    private BigDecimal type;
    /**创建时间*/
    @Excel(name = "创建时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date crdate;
    /**县区编码*/
    @Excel(name = "县区编码", width = 15)
    @ApiModelProperty(value = "县区编码")
    private String district;
    /**到期时间*/
    @Excel(name = "到期时间", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "到期时间")
    private Date expiretime;
    /**是否延期 0为不延期 1为延期*/
    @Excel(name = "是否延期 0为不延期 1为延期", width = 15)
    @ApiModelProperty(value = "是否延期 0为不延期 1为延期")
    private BigDecimal renewal;
    /**是否到期 0为未到期 1为到期*/
    @Excel(name = "是否有效 1为有效 0为无效", width = 15)
    @ApiModelProperty(value = "是否有效 1为有效 0为无效")
    private Integer state;
    /**原因*/
    @Excel(name = "原因", width = 15)
    @ApiModelProperty(value = "原因")
    private String reason;
    /**处理人*/
    @Excel(name = "处理人", width = 15)
    @ApiModelProperty(value = "处理人")
    private String operator;
    /**处理人姓名*/
    @Excel(name = "处理人姓名", width = 15)
    @ApiModelProperty(value = "处理人姓名")
    private String operatorName;
    /**createTime*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "createTime")
    private Date createTime;
    /**createBy*/
    @ApiModelProperty(value = "createBy")
    private String createBy;
    /**updateTime*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "updateTime")
    private Date updateTime;
    /**updateBy*/
    @ApiModelProperty(value = "updateBy")
    private String updateBy;

    @TableField(exist = false)
    private String optType;
}
