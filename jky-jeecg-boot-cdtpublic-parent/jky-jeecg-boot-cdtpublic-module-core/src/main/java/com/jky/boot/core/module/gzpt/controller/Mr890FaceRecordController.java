package com.jky.boot.core.module.gzpt.controller;

import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.common.utils.DateUtils;
import com.jky.boot.core.module.gzpt.entity.TMInsDevice;
import com.jky.boot.core.module.gzpt.entity.TMMr890;
import com.jky.boot.core.module.gzpt.entity.TMMr890FaceRecord;
import com.jky.boot.core.module.gzpt.service.ITMInsDeviceService;
import com.jky.boot.core.module.gzpt.service.ITMMr890FaceRecordService;
import com.jky.boot.core.module.gzpt.service.ITMMr890Service;
import com.jky.boot.core.module.gzpt.service.ITMMr890chLogService;
import com.jky.boot.core.util.OssZWYUtils;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import com.jky.crypto.annotation.DesensitizedAnno;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: mr890人脸记录查询Controller
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
@Api(tags="mr890人脸记录查询Controller")
@RestController
@RequestMapping("/gzpt/mr890Record")
@Slf4j
public class Mr890FaceRecordController extends JeecgController<TMMr890FaceRecord, ITMMr890FaceRecordService> {
    @Autowired
    private ITMMr890FaceRecordService mr890FaceRecordService;
    @Autowired
    private ITMMr890Service mr890Service;
    @Autowired
    private ITMInsDeviceService insDeviceService;
    @Autowired
    private OssZWYUtils ossZWYUtils;
    @Autowired
    private ITMMr890chLogService mr890chLogService;

    /**
     * 查询mr890人脸记录查询列表
     */
    @ApiOperation(value="查询mr890人脸记录查询列表", notes="查询mr890人脸记录查询列表")
//    @RequiresPermissions("gzpt:mr890Record:list")
    @GetMapping("/list")
    @DesensitizedAnno
    public Result<IPage<TMMr890FaceRecord>> list(TMMr890FaceRecord mr890FaceRecord,
                                                 @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                 @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                                 HttpServletRequest req) {
        List<String> mr890List = new ArrayList<>();
        // 获取部门的地区码
        String deptOrgCode = JkSecurityUtils.getDeptOrgCode();
        if(deptOrgCode.length() == 16 ){
            return Result.error("无权查看！");
        }
        QueryWrapper<TMMr890FaceRecord> queryWrapper = QueryGenerator.initQueryWrapper(mr890FaceRecord, req.getParameterMap());
        queryWrapper.setEntityClass(TMMr890FaceRecord.class);
        // 获取地区下的所有模拟点
        List<TMInsDevice> insDevices = insDeviceService.list(
            Wrappers.<TMInsDevice>lambdaQuery()
                .likeRight(TMInsDevice::getDistrict, deptOrgCode)
        );

        // 获取模拟点下的所有 mr890 设备 id
        List<String> insDeviceIds = insDevices.stream().map(TMInsDevice::getInsDeviceId).collect(Collectors.toList());
        List<TMMr890> list = mr890Service.list(
            Wrappers.<TMMr890>lambdaQuery()
                .in(TMMr890::getInsDeviceId, insDeviceIds)
        );
        list.forEach(per -> mr890List.add(per.getDeviceId()));

        if(CollectionUtils.isNotEmpty(mr890List)){
            queryWrapper.in("device_id", mr890List);
        }
        queryWrapper.orderByDesc("create_time");
        Page<TMMr890FaceRecord> page = new Page<>(pageNo, pageSize);
        IPage<TMMr890FaceRecord> pageList = mr890FaceRecordService.page(page, queryWrapper);
        if(pageList == null || pageList.getSize() == 0){
            return Result.OK(pageList);
        }
        pageList.getRecords().forEach(t->{
            String deviceId = t.getDeviceId();
            if (StringUtils.hasText(deviceId)) {
                TMMr890 mr890 = mr890Service.getById(deviceId);
                if (mr890 != null && StringUtils.hasText(mr890.getCenterName())) {
                    t.setCenterName(mr890.getCenterName());
                }
            }
            if (StringUtils.hasText(t.getSsoUrlPhoto()) && t.getSsoUrlPhoto().contains(OssZWYUtils.UPLOAD_PATH)) {
                t.setSsoUrlPhoto(ossZWYUtils.getPhotoUrl(t.getSsoUrlPhoto()));
//                t.setSsoUrlPhoto(OssZWYUtils.PROXY_URL+t.getSsoUrlPhoto());
            }
        });
        return Result.OK(pageList);
    }

//    /**
//     * 导出mr890人脸记录查询列表
//     */
//    @ApiOperation(value="导出mr890人脸记录查询列表", notes="导出mr890人脸记录查询列表")
////    @RequiresPermissions("gzpt:mr890Record:export")
//    @RequestMapping(value = "/export")
//    public ModelAndView exportXls(HttpServletRequest request, TMMr890FaceRecord mr890FaceRecord) {
//        List<TMMr890FaceRecord> list = mr890FaceRecordService.selectMr890FaceRecordList(mr890FaceRecord);
//
//        // 脱敏
//        try {
//            DesensitizedUtils.desensitization(list);
//        } catch (IllegalAccessException e) {
//            throw new RuntimeException(e);
//        }
//        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
//        mv.addObject(NormalExcelConstants.FILE_NAME, "mr890人脸记录查询数据");
//        mv.addObject(NormalExcelConstants.CLASS, TMMr890FaceRecord.class);
//        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("mr890人脸记录查询数据", "导出人:" + JkSecurityUtils.getRealname(), "mr890人脸记录查询数据"));
//        mv.addObject(NormalExcelConstants.DATA_LIST, list);
//        return mv;
//    }

    /**
     * 获取mr890人脸记录查询详细信息
     */
    @AutoLog(value = "获取mr890人脸记录查询详细信息")
    @ApiOperation(value="获取mr890人脸记录查询详细信息", notes="获取mr890人脸记录查询详细信息")
//    @RequiresPermissions("gzpt:mr890Record:query")
    @GetMapping(value = "/queryById")
    public Result<TMMr890FaceRecord> getInfoById(@RequestParam(name = "id", required = true) String id) {
        return Result.OK(mr890FaceRecordService.getById(id));
    }

    /**
     * 新增mr890人脸记录查询
     */
    @AutoLog(value = "新增mr890人脸记录查询")
    @ApiOperation(value="新增mr890人脸记录查询", notes="新增mr890人脸记录查询")
//    @RequiresPermissions("gzpt:mr890Record:add")
    @PostMapping
    public Result<String> add(@RequestBody TMMr890FaceRecord mr890FaceRecord) {
        mr890FaceRecordService.save(mr890FaceRecord);
        return Result.OK("添加成功！");
    }

    /**
     * 修改mr890人脸记录查询
     */
    @AutoLog(value = "修改mr890人脸记录查询")
    @ApiOperation(value="修改mr890人脸记录查询", notes="修改mr890人脸记录查询")
//    @RequiresPermissions("gzpt:mr890Record:edit")
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    public Result<String> edit(String id ,HttpServletRequest request) {
        TMMr890FaceRecord mr890FaceRecord = mr890FaceRecordService.getById(id);
        String logContent = mr890FaceRecord.getName()+mr890FaceRecord.getIdcard()+"人脸识别设置通过";
        mr890chLogService.keeplog(3,logContent,request);
        SymmetricCrypto aes = new SymmetricCrypto(SymmetricAlgorithm.AES, "9S51WGHAJ3L2HS6K".getBytes());
        Map<String, Object> aesMap = new HashMap<>();
        aesMap.put("deviceId", mr890FaceRecord.getDeviceId());
        aesMap.put("idcard", mr890FaceRecord.getIdcard());
        aesMap.put("createTime", DateUtils.dateTimeNow());
        String key = aes.encryptHex(JSON.toJSONString(aesMap));
        mr890FaceRecord.setKey(key);
        mr890FaceRecord.setStatus(2);
        mr890FaceRecord.setCreateTime(new Date());
        mr890FaceRecordService.updateMr890FaceRecord(mr890FaceRecord);
        return Result.OK("编辑成功！");
    }


    /**
     * 加密接口
     */
    @GetMapping("/encrypt")
    public Result<?> encryptData() {
        int page = 1;
        try {
            while (true){
                QueryWrapper<TMMr890FaceRecord> wrapper = new QueryWrapper<>();
                wrapper.setEntityClass(TMMr890FaceRecord.class);
                wrapper.eq("LENGTH(idcard)", 18);
                wrapper.last("limit 300");
                List<TMMr890FaceRecord> list = mr890FaceRecordService.list(wrapper);
                if(CollectionUtils.isEmpty(list)){
                    break;
                }
                mr890FaceRecordService.updateBatchById(list);
                log.info("[学员数据加密,第{}次循环]", page);
                page++;
            }
        }catch (Exception e){
            log.error("学员数据加密结束异常", e);
            return Result.error("fail");
        }
        log.info("[学员数据加密结束,一共{}页]",page);
        return Result.ok();
    }
}
