package com.jky.boot.core.module.gzpt.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.core.module.gzpt.dto.StatisticsSensitiveDTO;
import com.jky.boot.core.module.gzpt.dto.StatisticsYear2YearDTO;
import com.jky.boot.core.module.gzpt.vo.StatisticsSensitiveVO;
import com.jky.boot.core.module.gzpt.vo.StatisticsYear2YearVO;

import java.util.List;
import java.util.Map;

public interface StatisticsAnalyseService{

    List<StatisticsSensitiveVO> analyseSensitive(StatisticsSensitiveDTO statisticsAnalyseDTO);

   StatisticsYear2YearVO analyseYear2Year(StatisticsYear2YearDTO statisticsYear2YearDTO);
}
