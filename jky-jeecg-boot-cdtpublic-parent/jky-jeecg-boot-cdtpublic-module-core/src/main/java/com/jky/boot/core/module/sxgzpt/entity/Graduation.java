package com.jky.boot.core.module.sxgzpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jky.crypto.annotation.CryptoFieldAnno;
import com.jky.crypto.annotation.DesensitizedFieldAnno;
import com.jky.crypto.enums.DesensitizedTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 结业考核
 *
 * <AUTHOR>
 * @version 2023-10-23
 */
@Data
@TableName("t_m_graduation")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "t_m_graduation对象", description = "结业考核")
public class Graduation implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private String id;
    /**
     * 学员名称
     */
    @Excel(name = "学员名称", width = 15)
    @ApiModelProperty(value = "学员名称")
    @CryptoFieldAnno
    private String stuname;
    /**
     * 身份证
     */
    @Excel(name = "身份证", width = 15)
    @ApiModelProperty(value = "身份证")
    @DesensitizedFieldAnno(value = DesensitizedTypeEnum.CUSTOM, start = 6, end = 4)
    @CryptoFieldAnno
    private String idcard;
    /**
     * 所属驾校
     */
    @Excel(name = "所属驾校", width = 15)
    @ApiModelProperty(value = "所属驾校")
    @Dict(dictTable = "t_m_institution", dicCode = "inscode", dicText = "name")
    private String inscode;
    /**
     * 科目
     */
    @Excel(name = "科目", width = 15)
    @ApiModelProperty(value = "科目")
    @Dict(dicCode = "sys_subCode")
    private Integer subject;
    /**
     * 考核成绩
     */
    @Excel(name = "考核成绩", width = 15)
    @ApiModelProperty(value = "考核成绩")
    private Integer score;
    /**
     * 考核时间
     */
    @Excel(name = "考核时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "考核时间")
    private Date examDate;

    @ApiModelProperty(value = "考核现场照片(多张,分割)")
    private String photoUrl;
}
