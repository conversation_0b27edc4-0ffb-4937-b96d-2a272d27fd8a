package com.jky.boot.core.module.gzpt.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.base.dao.StuLogoutApply;
import com.jky.boot.base.service.IStuLogoutApplyService;
import com.jky.boot.core.module.gzpt.entity.StuTransfer;
import com.jky.boot.core.module.gzpt.entity.Studentinfo;
import com.jky.boot.core.module.gzpt.entity.TMMr890chLog;
import com.jky.boot.core.module.gzpt.service.IStuTransferService;
import com.jky.boot.core.module.gzpt.service.IStudentinfoService;
import com.jky.boot.core.module.gzpt.service.ITMMr890chLogService;
import com.jky.boot.core.module.gzpt.utils.CancelAccountUtils;
import com.jky.boot.core.module.gzpt.utils.SM4Utils;
import com.jky.boot.core.module.gzpt.vo.BlackOrLogoutVo;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import com.jky.crypto.annotation.DesensitizedAnno;
import com.jky.crypto.utils.JkyCryptorUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;

/**
 * 学员注销申请
 *
 * <AUTHOR>
 * @version 2024-02-22
 */
@Api(tags = "学员注销申请")
@RestController
@RequestMapping("/gzpt/stuLogoutApply")
@Slf4j
public class StuLogoutApplyController extends JeecgController<StuLogoutApply, IStuLogoutApplyService> {

    @Value("${jky.crypto-mybatis.default-key}")
    private String cryptoKey;
    @Autowired
    private IStuLogoutApplyService stuLogoutApplyService;
    @Autowired
    private ITMMr890chLogService mr890chLogService;
    @Autowired
    private IStudentinfoService stuService;
    @Autowired
    private IStudentinfoService studentinfoService;
    @Autowired
    private IStuTransferService stuTransferService;

    /**
     * 分页列表查询
     */
    @ApiOperation(value = "学员注销申请-分页列表查询", notes = "学员注销申请-分页列表查询")
    @GetMapping(value = "/list")
    @DesensitizedAnno
    public Result<IPage<StuLogoutApply>> queryPageList(StuLogoutApply stuLogoutApply,
                                                       @RequestParam(name = "pageNo", defaultValue = "1")
                                                               Integer pageNo,
                                                       @RequestParam(name = "pageSize", defaultValue = "10")
                                                               Integer pageSize,
                                                       HttpServletRequest req) {
        List<String> inscode = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(stuLogoutApply.getInscode(), stuLogoutApply.getInscodes());
        stuLogoutApply.setInscode(null);
        QueryWrapper<StuLogoutApply> queryWrapper =
                QueryGenerator.initQueryWrapper(stuLogoutApply, req.getParameterMap());
        queryWrapper.in("inscode", inscode);
        queryWrapper.orderByDesc("create_time");
        Page<StuLogoutApply> page = new Page<>(pageNo, pageSize);
        IPage<StuLogoutApply> pageList = stuLogoutApplyService.page(page, queryWrapper);
        return Result.OK(pageList);
    }


    /**
     * 学员注销申请审核
     */
    @ApiOperation(value = "学员注销申请审核", notes = "学员注销申请审核")
    @PostMapping(value = "/audit")
    @RequiresPermissions("gzpt:stuLogoutApply:audit")
    public Result<StuLogoutApply> audit(@RequestBody StuLogoutApply logoutApply) {
        StuLogoutApply apply = stuLogoutApplyService.getById(logoutApply.getId());
        Studentinfo stu = studentinfoService.getOneByStuNum(apply.getStunum());
        if (Objects.isNull(stu)) {
            return Result.error("未查到该学员的学员信息！");
        }
        if (apply.getAuditStatus() != 0) {
            return Result.error("该记录已审核，无需重复审核");
        }
        LoginUser loginUser = JkSecurityUtils.getLoginUser();
        if (logoutApply.getAuditStatus() == 2) {
            apply.setAuditStatus(2);
            apply.setAuditTime(new Date());
            apply.setAuditPerson(loginUser.getRealname());
            apply.setAuditRemark(logoutApply.getAuditRemark());
            stuLogoutApplyService.updateById(apply);
            return Result.ok("审核成功");
        }
        apply.setApplydate(stu.getApplydate());
        apply.setAuditStatus(1);
        apply.setAuditTime(new Date());
        apply.setAuditPerson(loginUser.getRealname());
        apply.setAuditRemark(logoutApply.getAuditRemark());
        Result<Integer> result = CancelAccountUtils.regCheck(stu);
        if (!result.isSuccess()) {
            return Result.error(result.getMessage());
        }
        int freeze = result.getResult();
        // 未开户的直接执行注销
        if (freeze == 0) {
            String s = CancelAccountUtils.doLogout(stu, apply, loginUser.getRealname());
            if (Objects.equals(s, "success")) {
                apply.setRemark("学员已注销");
            } else {
                apply.setRemark("学员推送监管失败");
            }
        } else if (freeze == 1) {
            // 开户但未冻结的先销户再注销
            Result<?> cancelRes = stuService.cancelAccount(stu.getStunum());
            apply.setRemark(cancelRes.getMessage());
            if (cancelRes.isSuccess()) {
                String s = CancelAccountUtils.doLogout(stu, apply, loginUser.getRealname());
                apply.setRemark("success".equals(s) ? "学员已注销" : "学员推送监管失败");
            }
        } else {
            // 违约金够的先扣违约金，不够违约金的全解冻
//            Result<BigDecimal> damageRes = CancelAccountUtils.damageCheck(stu.getStunum());
//            if (damageRes.isSuccess()) {
//                stuTransferService.trans(damageRes.getResult(), stu, 2);
//            } else {
//                stuService.unfreeze(stu.getStunum(), 2);
//            }
            stuService.unfreeze(stu.getStunum(), 2, null);
            apply.setRemark("注销流程中");
        }
        stuLogoutApplyService.updateById(apply);
        return Result.ok("审核成功");
    }


    /**
     * 手动推送监管
     */
    @AutoLog(value = "学员注销申请-重新推送监管")
    @ApiOperation(value = "学员注销申请-重新推送监管", notes = "学员注销申请-重新推送监管")
    @PostMapping(value = "/rePushJg")
    public Result<String> rePushJg(@RequestBody Map<String, String> param) {
        String id = param.get("id");
        StuLogoutApply stuLogoutApply = stuLogoutApplyService.getById(id);
        Studentinfo studentinfo = studentinfoService.getOneByStuNum(stuLogoutApply.getStunum());
        if (Objects.isNull(studentinfo)) {
            return Result.error("未找到该学员信息");
        }
        if (!Objects.equals(stuLogoutApply.getStatus(), 2)) {
            return Result.error("只有推送监管失败才能手动推送监管");
        }
        String s = CancelAccountUtils.doLogout(studentinfo, stuLogoutApply, "手动推送监管");
        stuLogoutApplyService.updateById(stuLogoutApply);
        if (Objects.equals("success", s)) {
            return Result.OK("推送监管端成功，学员注销");
        } else {
            return Result.error("推送监管失败");
        }
    }

    /**
     * 添加
     */
    @AutoLog(value = "学员注销申请-添加")
    @ApiOperation(value = "学员注销申请-添加", notes = "学员注销申请-添加")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody StuLogoutApply stuLogoutApply) {
        stuLogoutApplyService.save(stuLogoutApply);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     */
    @AutoLog(value = "学员注销申请-编辑")
    @ApiOperation(value = "学员注销申请-编辑", notes = "学员注销申请-编辑")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody StuLogoutApply stuLogoutApply) {
        stuLogoutApplyService.updateById(stuLogoutApply);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     */
    @AutoLog(value = "学员注销申请-通过id删除")
    @ApiOperation(value = "学员注销申请-通过id删除", notes = "学员注销申请-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id") String id) {
        stuLogoutApplyService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     */
    @AutoLog(value = "学员注销申请-批量删除")
    @ApiOperation(value = "学员注销申请-批量删除", notes = "学员注销申请-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids") String ids) {
        this.stuLogoutApplyService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     */
    @ApiOperation(value = "学员注销申请-通过id查询", notes = "学员注销申请-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<StuLogoutApply> queryById(@RequestParam(name = "id") String id) {
        StuLogoutApply stuLogoutApply = stuLogoutApplyService.getById(id);
        if (stuLogoutApply == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(stuLogoutApply);
    }

    /**
     * 接收监管发起注销信息
     */
    @PostMapping(value = "/logoutRec")
    public Result<?> logoutRec(@RequestBody String recString) {
        SM4Utils sm4 = new SM4Utils();
        BlackOrLogoutVo rec = JSONObject.parseObject(sm4.decryptData_ECB(recString), BlackOrLogoutVo.class);
        Studentinfo one = stuService.getOne(
                Wrappers.lambdaQuery(Studentinfo.class)
                        .eq(Studentinfo::getInscode, rec.getInscode())
                        .eq(Studentinfo::getIdcard, rec.getIdcard())
                        .eq(Studentinfo::getTraintype, rec.getTrainType())
        );
        if (Objects.isNull(one)) {
            return Result.error("该学员不存在");
        }
        StuTransfer stuTransfer = stuTransferService.getOne(
                Wrappers.<StuTransfer>lambdaQuery()
                        .eq(StuTransfer::getStunum, one.getStunum())
                        .eq(StuTransfer::getOldInscode, one.getInscode())
                        .orderByDesc(StuTransfer::getCrdate), false
        );
        if (stuTransfer != null) {
            if (stuTransfer.getInsAudit() == 2
                    || (stuTransfer.getInsAudit() == 1 && stuTransfer.getStatus() == 2)
                    || (stuTransfer.getInsAudit() == 1 && stuTransfer.getStatus() == 1 && stuTransfer.getFlow() == 4)) {

            } else {
                return Result.error("转校流程未完结，不可注销");
            }

        }

        // 执行注销操作
        Result<?> result = stuLogoutApplyService.logoutRec(one);
        if (!result.isSuccess()) {
            return result;
        }
        // 记录日志
        TMMr890chLog res = new TMMr890chLog();
        String realName = JkyCryptorUtils.decrypt(rec.getRealName(), cryptoKey);
        res.setLogType(5);
        res.setLogContent(one.getName() + one.getIdcard() + "学员注销通过");
        res.setUserid(rec.getUserName());
        res.setUsername(realName);
        res.setIp(rec.getIp());
        mr890chLogService.save(res);
        // 注销表保存记录
        StuLogoutApply apply = new StuLogoutApply();
        apply.setInscode(rec.getInscode());
        apply.setIdcard(rec.getIdcard());
        apply.setStunum(one.getStunum());
        apply.setStuname(one.getName());
        apply.setAuditStatus(1);
        apply.setAuditRemark("监管注销学员");
        apply.setStuReason("监管注销学员");
        apply.setAuditTime(new Date());
        apply.setAuditPerson(realName);
        apply.setRegistration(new Date());
        apply.setApplydate(one.getApplydate());
        apply.setInsname(one.getInsname());
        apply.setPushDate(new Date());
        apply.setStatus(1);
        stuLogoutApplyService.save(apply);
        return Result.ok(one);
    }
}
