package com.jky.boot.zlb.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.common.cas.entity.BaseZlbPersonInfo;
import com.jky.boot.common.sensitive.DesensitizedUtils;
import com.jky.boot.core.module.gzpt.entity.StudentContract;
import com.jky.boot.core.module.gzpt.entity.StudentStudyProof;
import com.jky.boot.core.module.gzpt.entity.Studentinfo;
import com.jky.boot.core.module.gzpt.entity.StudentinfoEnter;
import com.jky.boot.core.module.gzpt.service.IStudentContractService;
import com.jky.boot.core.module.gzpt.service.IStudentStudyProofService;
import com.jky.boot.core.module.gzpt.service.IStudentinfoService;
import com.jky.boot.core.module.gzpt.utils.Constant;
import com.jky.boot.core.module.sxgzpt.dto.CoachLabelDto;
import com.jky.boot.core.util.OssZWYUtils;
import com.jky.boot.core.util.ZlbStuUtils;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import com.jky.boot.zlb.entity.TMCoach;
import com.jky.boot.zlb.entity.TMStudentInfoEnter;
import com.jky.boot.zlb.service.ITMAuthrecordService;
import com.jky.boot.zlb.service.ITMCoachService;
import com.jky.boot.zlb.service.ITMStudentInfoEnterService;
import com.jky.crypto.annotation.DesensitizedAnno;
import com.jky.crypto.enums.DesensitizedTypeEnum;
import com.jky.crypto.utils.JkyDesensitizedUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;


@Api(tags = "学员科目一学习证明")
@RestController
@RequestMapping("/zlb/studentStudyProof")
@Slf4j
public class ZlbStudentStudyProofController extends JeecgController<StudentStudyProof, IStudentStudyProofService> {
    @Autowired
    private IStudentStudyProofService studentStudyProofService;
    @Autowired
    private IStudentinfoService studentinfoService;
    @Autowired
    private OssZWYUtils ossZWYUtils;
    @RequestMapping(value = "/getById", method = RequestMethod.GET)
    public Result<?> person(@RequestParam(name = "id") String id) {
        Studentinfo stu = ZlbStuUtils.getStuByZlbId(id);
        if (Objects.isNull(stu)||StringUtils.isBlank(stu.getStunum())) {
            return Result.error("学员信息不存在");
        }
        String stunum = stu.getStunum();

        StudentStudyProof studentStudyProof = studentStudyProofService.getOne(Wrappers.<StudentStudyProof>lambdaQuery()
                .eq(StudentStudyProof::getStunum, stunum).last("limit 1")
        );
        if(Objects.isNull(studentStudyProof)){
            return Result.error(3210,"未上传学习证明");
        }
        if (StringUtils.isNotBlank(studentStudyProof.getName())) {
            studentStudyProof.setName(DesensitizedUtils.chineseNameZlb(studentStudyProof.getName()));
        }
        if (StringUtils.isNotBlank(studentStudyProof.getIdcard())) {
            studentStudyProof.setIdcard(DesensitizedUtils.idCardNumZlb(studentStudyProof.getIdcard()));
        }
        //OssZWYUtils.subStr(studentInfoSaveDto.getHeadpicture());
        studentStudyProof.setStudyUrl(ossZWYUtils.getPhotoUrl(studentStudyProof.getStudyUrl()));
        return Result.ok(studentStudyProof);
    }
    @RequestMapping(value = "/getByStudentInfoId", method = RequestMethod.GET)
    public Result<?> getByStudentInfoId(@RequestParam(name = "id") String id) {
        // 先通过id查询正式学员信息
        Studentinfo studentinfo = studentinfoService.getById(id);
        if (Objects.isNull(studentinfo)||StringUtils.isBlank(studentinfo.getStunum())) {
            return Result.error("学员信息不存在");
        }
        String stunum = studentinfo.getStunum();
        StudentStudyProof studentStudyProof = studentStudyProofService.getOne(Wrappers.<StudentStudyProof>lambdaQuery()
                .eq(StudentStudyProof::getStunum, stunum).last("limit 1")
        );
        if(Objects.isNull(studentStudyProof)){
            return Result.error(3210,"未上传学习证明");
        }
        studentStudyProof.setStudyUrl(ossZWYUtils.getPhotoUrl(studentStudyProof.getStudyUrl()));
        return Result.ok(studentStudyProof);
    }
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public Result<?> save(@RequestBody StudentStudyProof studentStudyProof) {
        Studentinfo stu = ZlbStuUtils.getStuByZlbId(studentStudyProof.getId());
        if (Objects.isNull(stu)||StringUtils.isBlank(stu.getStunum())) {
            return Result.error("学员信息不存在");
        }
        long count = studentStudyProofService.count(Wrappers.<StudentStudyProof>lambdaQuery()
                .eq(StudentStudyProof::getStunum, stu.getStunum())
        );
        if (count > 0) {
            return Result.error("学习证明已存在");
        }
        if(!StringUtils.equalsIgnoreCase(stu.getIdcard(),studentStudyProof.getIdcard())){
            return Result.error("身份证不一致，上传失败");
        }
        studentStudyProof.setId(null);
        studentStudyProof.setStunum(stu.getStunum());
        studentStudyProof.setInscode(stu.getInscode());
        studentStudyProof.setStudyUrl(OssZWYUtils.subStr(studentStudyProof.getStudyUrl()));
        studentStudyProofService.save(studentStudyProof);
        return Result.ok();
    }

    /**
     * 通过id删除学习证明
     *
     * @param id 学习证明ID
     * @return 结果
     */
    @AutoLog(value = "学员科目一学习证明-通过id删除")
    @ApiOperation(value="学员科目一学习证明-通过id删除", notes="学员科目一学习证明-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name="id",required=true) String id) {
        studentStudyProofService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除学习证明
     *
     * @param ids 学习证明ID，多个ID用逗号分隔
     * @return 结果
     */
    @AutoLog(value = "学员科目一学习证明-批量删除")
    @ApiOperation(value="学员科目一学习证明-批量删除", notes="学员科目一学习证明-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        this.studentStudyProofService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 分页列表查询
     *
     * @param studentStudyProof 查询条件
     * @param pageNo 页码
     * @param pageSize 每页记录数
     * @param req 请求
     * @return 分页数据
     */
    @AutoLog(value = "学员科目一学习证明-分页列表查询")
    @ApiOperation(value="学员科目一学习证明-分页列表查询", notes="学员科目一学习证明-分页列表查询")
    @GetMapping(value = "/list")
    @DesensitizedAnno
    public Result<IPage<StudentStudyProof>> queryPageList(StudentStudyProof studentStudyProof,
                                                      @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                      @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                                      HttpServletRequest req) {
        // 获取当前用户所属驾校信息
        List<String> inscodeList = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(studentStudyProof.getInscode());
        QueryWrapper<StudentStudyProof> queryWrapper = QueryGenerator.initQueryWrapper(studentStudyProof, req.getParameterMap());
        // 添加驾校筛选条件
        queryWrapper.in("inscode", inscodeList);
        Page<StudentStudyProof> page = new Page<>(pageNo, pageSize);
        IPage<StudentStudyProof> pageList = studentStudyProofService.page(page, queryWrapper);

        // 处理学习证明URL，添加完整路径
        for (StudentStudyProof proof : pageList.getRecords()) {
            if (StringUtils.isNotBlank(proof.getStudyUrl())) {
                proof.setStudyUrl(ossZWYUtils.getPhotoUrl(proof.getStudyUrl()));
            }
        }

        return Result.OK(pageList);
    }
}
