package com.jky.boot.core.module.sxgzpt.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jky.boot.core.module.gzpt.entity.ClassRecordDetail;
import com.jky.boot.core.module.sxgzpt.entity.TrainRecordAbnormal;
import com.jky.boot.core.module.sxgzpt.mapper.TrainRecordAbnormalMapper;
import com.jky.boot.core.module.sxgzpt.service.IAbnormalService;
import com.jky.boot.core.module.sxgzpt.service.ITrainRecordAbnormalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 异常组管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-21
 */
@Service
public class TrainRecordAbnormalServiceImpl extends ServiceImpl<TrainRecordAbnormalMapper, TrainRecordAbnormal> implements ITrainRecordAbnormalService {
    @Autowired
    private TrainRecordAbnormalMapper trainRecordAbnormalMapper;
    //    @Autowired
//    private ITrainRecordAbnormalNoService trainRecordAbnormalNoService;
    @Autowired
    private IAbnormalService abnormalService;

    /**
     * 查询异常组管理列表
     *
     * @param trainRecordAbnormal 异常组管理
     * @return 异常组管理
     */
    @Override
    public List<TrainRecordAbnormal> selectTrainRecordAbnormalList(TrainRecordAbnormal trainRecordAbnormal) {
        return trainRecordAbnormalMapper.selectTrainRecordAbnormalList(trainRecordAbnormal);
    }

    @Override
    public List<ClassRecordDetail> getClassRecordDetailList(String groupId, Date crdate, String tablename) {
        return trainRecordAbnormalMapper.getClassRecordDetailList(groupId, crdate, tablename);
    }

    @Override
    public List<ClassRecordDetail> getClassRecordDetailList(String groupId, Date strattime, Date crdate, String tablename) {
        return trainRecordAbnormalMapper.getoldClassRecordDetailList(groupId, strattime, crdate, tablename);
    }

//    @Override
//    public List<TrainRecord> getTrainRecordList(String groupId, Date crdate, String tablename) {
//        return trainRecordAbnormalMapper.getTrainRecordList(groupId,crdate,tablename);
//    }

    @Override
    public List<ClassRecordDetail> getClassRecordDetailListByGroupIds(List<String> ids, Date starttime, Date maxCrdate) {
        return trainRecordAbnormalMapper.getClassRecordDetailListByGroupIds(ids, maxCrdate, starttime);
    }
}
