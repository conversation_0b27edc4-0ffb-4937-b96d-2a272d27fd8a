package com.jky.boot.core.module.gzpt.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jky.boot.core.module.gzpt.entity.TMMr890chLog;

import javax.servlet.http.HttpServletRequest;

/**
 * @Description: t_m_mr890ch_log
 * @Author: jeecg-boot
 * @Date: 2024-01-18
 * @Version: V1.0
 */
public interface ITMMr890chLogService extends IService<TMMr890chLog> {

    public void keeplog(Integer type ,String logContent, HttpServletRequest req);

    /**
     * 监管平台操作 存储敏感操作
     * @param type
     * @param logContent
     * @param ip
     * @param userid
     * @param username
     */
    public void jgKeepLog(Integer type, String logContent, String ip, String userid, String username);
}
