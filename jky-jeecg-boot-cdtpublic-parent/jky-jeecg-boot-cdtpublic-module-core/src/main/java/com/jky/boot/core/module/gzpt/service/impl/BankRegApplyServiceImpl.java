package com.jky.boot.core.module.gzpt.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jky.boot.base.service.ISchedulingApplyService;
import com.jky.boot.core.module.gzpt.dto.ApplyTimesDto;
import com.jky.boot.core.module.gzpt.entity.BankReg;
import com.jky.boot.core.module.gzpt.entity.BankRegApply;
import com.jky.boot.core.module.gzpt.entity.StudentContract;
import com.jky.boot.core.module.gzpt.entity.Studentinfo;
import com.jky.boot.core.module.gzpt.mapper.BankRegApplyMapper;
import com.jky.boot.core.module.gzpt.service.IBankRegApplyService;
import com.jky.boot.core.module.gzpt.service.IBankRegService;
import com.jky.boot.core.module.gzpt.service.IStudentContractService;
import com.jky.boot.core.module.gzpt.service.IStudentinfoService;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 学员多笔冻结申请
 */
@Service
@Slf4j
public class BankRegApplyServiceImpl extends ServiceImpl<BankRegApplyMapper, BankRegApply> implements IBankRegApplyService {

    @Autowired
    private IStudentinfoService studentinfoService;
    @Autowired
    private IBankRegService bankRegService;
    @Autowired
    private IStudentContractService studentContractService;
    @Autowired
    private ISchedulingApplyService schedulingApplyService;

    @Override
    public Result<?> saveDefaultApply(Studentinfo studentinfo) {
        //根据学员当前的状态，系统自动生成一条待确认的多笔冻结申请
        //多笔冻结步骤：
        //    1、9 额外多笔冻结先不考虑
        //    2、1 单笔冻结 无法进行多笔冻结操作
        //    3、当前开户类型为 21二笔冻结第一笔（课堂+模拟） 并且 余额为0 ，可以 冻结 22二笔冻结第二笔（科二+科三实操）
        //    4、当前开户类型为 31三笔冻结第一笔（课堂+模拟） 并且 余额为0 ，可以 冻结 32三笔冻结第二笔（科二实操）
        //    5、当前开户类型为 32三笔冻结第二笔（科二实操） 并且 余额为0 ，可以 冻结 33三笔冻结第三笔（科三实操）
        //    6、当前开户类型为 32三笔冻结第二笔（科二实操） 并且 余额小于 科二科三最低单价 ，发起退费申请，驾校审核通过退费后再执行第5步

        String stunum = studentinfo.getStunum();
        if (StringUtils.isBlank(stunum)) {
            return Result.error("学员编号未生成");
        }
        if (StringUtils.equalsIgnoreCase(studentinfo.getRegState(), "1")) {
            return Result.error("当前已为单笔冻结，无需再次冻结");
        }
        if (StringUtils.equalsIgnoreCase(studentinfo.getRegState(), "22")) {
            return Result.error("当前已为二笔冻结第二笔（科二+科三实操），无需再次冻结");
        }
        if (StringUtils.equalsIgnoreCase(studentinfo.getRegState(), "33")) {
            return Result.error("当前已为三笔冻结第三笔（科三实操），无需再次冻结");
        }
        List<String> regStateList = Arrays.asList("21", "31", "32");
        if (!regStateList.contains(studentinfo.getRegState())) {
            return Result.error("当前阶段" + studentinfo.getRegState() + "无需再次冻结");
        }
        BankRegApply lastApply = this.getOne(Wrappers.<BankRegApply>lambdaQuery()
                .eq(BankRegApply::getStunum, stunum)
                .orderByDesc(BankRegApply::getCreateTime).last("limit 1"));
        if (Objects.nonNull(lastApply)) {
            if (Objects.equals(0, lastApply.getStatus())) {
                return Result.error("存在未确认的" + (StringUtils.equalsIgnoreCase("1", lastApply.getApplyType()) ? "冻结" : "解冻") + "申请，请先确认");
            }
            if (Objects.equals(10, lastApply.getStatus())) {
                return Result.error("申请待驾校审核，请耐心等待");
            }
            if (Objects.equals(1, lastApply.getStatus()) || Objects.equals(2, lastApply.getStatus())) {
                return Result.error("申请已确认，请耐心等待");
            }
            if (Objects.equals(4, lastApply.getStatus()) && StringUtils.equalsIgnoreCase("2", lastApply.getApplyType())) {
                return Result.error("解冻申请失败，无法再次冻结");
            }
        }
        BankReg reg = bankRegService.getOneByStuNum(stunum);
        if (Objects.isNull(reg) || !Objects.equals(3, reg.getStatus())) {
            return Result.error("未进行冻结，请先冻结");
        }
        StudentContract contract = studentContractService.getOneByStunum(stunum);
        if (Objects.isNull(contract)) {
            return Result.error("合同不存在，无法冻结");
        }
        //补充reg表身份证缺失
        if (StringUtils.isBlank(reg.getIdcard()) && StringUtils.isNotBlank(studentinfo.getIdcard())) {
            reg.setIdcard(studentinfo.getIdcard());
        }
        BankRegApply apply = null;
        //开始创建冻结申请
        if (StringUtils.equalsIgnoreCase(studentinfo.getRegState(), "21")) {
            //当前开户类型为 21二笔冻结第一笔（课堂+模拟） 并且 余额为0 ，可以 冻结 22二笔冻结第二笔（科二+科三实操）
            if (reg.getRemainingAmount().compareTo(BigDecimal.ZERO) != 0) {
                return Result.error("冻结余额不为0，无法再次冻结");
            }
            //创建二笔冻结第二笔（科二+科三实操） 冻结申请
            apply = createApply("22", reg, contract);
            if (apply.getBalanceAmount().compareTo(BigDecimal.ZERO) == 0) {
                return Result.error("申请冻结金额为0，申请失败");
            }
        } else if (StringUtils.equalsIgnoreCase(studentinfo.getRegState(), "31")) {
            //当前开户类型为 31三笔冻结第一笔（课堂+模拟） 并且 余额为0 ，可以 冻结 32三笔冻结第二笔（科二实操）
            if (reg.getRemainingAmount().compareTo(BigDecimal.ZERO) != 0) {
                return Result.error("冻结余额不为0，无法再次冻结");
            }
            //创建三笔冻结第二笔（科二实操） 冻结申请
            apply = createApply("32", reg, contract);
            if (apply.getBalanceAmount().compareTo(BigDecimal.ZERO) == 0) {
                return Result.error("申请冻结金额为0，申请失败");
            }

            //String remark = "无法再次冻结，当前冻结余额：" + reg.getRemainingAmount() + " ;科二单价：" + contract.getSub2CostPer() +
            //        " ;科三单价：" + contract.getSub3CostPer() + "  亲，系统建议冻结余额最好能被 科二单价 和科三单价 减至0，最好不要有余数，不然要发起退费申请噢！";
            if (contract.getSub2CostPer().compareTo(contract.getSub3CostPer()) != 0) {
                String remark = "当前冻结资金为科目二学费总和，因科目二、科目三单学时学费可能存在不同，若同时学习会导致冻结账户最后剩余资金不足以支付任一学时的费用，此时需退费并重新冻结剩余所有学费，请学员谨慎选择";
                apply.setRemark(remark);
            }
        } else if (StringUtils.equalsIgnoreCase(studentinfo.getRegState(), "32")) {
            //当前开户类型为 32三笔冻结第二笔（科二实操） 并且 余额为0 ，可以 冻结 33三笔冻结第三笔（科三实操）
            if (reg.getRemainingAmount().compareTo(BigDecimal.ZERO) > 0) {
                //当前开户类型为 32三笔冻结第二笔（科二实操） 并且 余额小于 科二科三最低单价 ，发起退费申请，驾校审核通过退费后再执行第5步
                //科二 100 科三 80       冻结余额 70 80 90 100 110
                //科二 80  科三 100      冻结余额 70 80 90 100 110
                BigDecimal min = contract.getSub2CostPer().min(contract.getSub3CostPer());
                if (reg.getRemainingAmount().compareTo(min) >= 0) {
                    return Result.error("无法再次冻结，请按驾校要求学完相应课时后再进行冻结操作，当前冻结余额：" + reg.getRemainingAmount());
                } else {
                    //32三笔冻结第二笔（科二实操） 并且 余额小于 科二科三最低单价 ，发起退费申请
                    apply = createRefundApply("32", reg, reg.getRemainingAmount());
                    String remark = "当前冻结余额：" + reg.getRemainingAmount() + " 小于 科二科三最低单价，需要发起退费申请！";
                    apply.setRemark(remark);
                }
            } else if (reg.getRemainingAmount().compareTo(BigDecimal.ZERO) == 0) {
                //创建三笔冻结第三笔（科三实操） 冻结申请
                apply = createApply("33", reg, contract);
                if (apply.getBalanceAmount().compareTo(BigDecimal.ZERO) == 0) {
                    return Result.error("申请冻结金额为0，申请失败");
                }

            }
        }
        boolean save = this.save(apply);
        return save ? Result.OK("申请成功") : Result.error("操作失败");
    }

    @Override
    public Result<?> setApplyConfirm(String id, String stunum) {
        if (StringUtils.isBlank(stunum)) {
            return Result.error("学员编号未生成");
        }
        BankRegApply apply = this.getById(id);
        if (Objects.isNull(apply)) {
            return Result.error("申请不存在");
        }
        if (!StringUtils.equalsIgnoreCase(stunum, apply.getStunum())) {
            return Result.error("申请不存在!!!!!");
        }
        if (!Objects.equals(0, apply.getStatus())) {
            return Result.error("申请已确认，无需重复确认");
        }
        BankRegApply newApply = new BankRegApply();
        newApply.setId(id);
        //学员确认（驾校待审核）
        newApply.setStatus(10);
        boolean update = this.updateById(newApply);
        return update ? Result.OK("操作成功") : Result.error("操作失败");
    }

    @Override
    public IPage<BankRegApply> getApplyList(Page<BankRegApply> page, String stunum, String applyType) {
        LambdaQueryWrapper<BankRegApply> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BankRegApply::getStunum, stunum);
        queryWrapper.eq(StringUtils.isNotBlank(applyType), BankRegApply::getApplyType, applyType);
        queryWrapper.orderByDesc(BankRegApply::getCreateTime);
        IPage<BankRegApply> pageList = this.page(page, queryWrapper);
        return pageList;
    }

    @Override
    public Result<?> applyAudit(BankRegApply apply) {
        if (StringUtils.isBlank(apply.getId()) || StringUtils.isBlank(apply.getAuditStatus())) {
            return Result.error("必填参数为空");
        }
        if (StringUtils.equalsIgnoreCase(apply.getAuditStatus(), "2") || StringUtils.isBlank(apply.getAuditReason())) {
            return Result.error("审核不通过需要提供不通过原因");
        }
        //所拥有的驾校权限
        List<String> inscodeList = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(null);
        if (CollectionUtils.isEmpty(inscodeList)) {
            return Result.error("无权操作");
        }
        BankRegApply bankRegApply = this.getById(apply);
        if (Objects.isNull(bankRegApply)) {
            return Result.error("申请不存在");
        }
        if (!inscodeList.contains(bankRegApply.getInscode())) {
            return Result.error("无权操作");
        }

        if (!Objects.equals(apply.getStatus(), 10)) {
            return Result.error("只有待审核数据才能审核");
        }
        bankRegApply.setStatus(StringUtils.equalsIgnoreCase(apply.getAuditStatus(), "1") ? 1 : 12);
        bankRegApply.setAuditDate(new Date());
        bankRegApply.setAuditReason(apply.getAuditReason());
        boolean update = this.updateById(bankRegApply);
        return update ? Result.OK("审核成功") : Result.error("审核失败");
    }


    /**
     * 创建初始的冻结订单
     *
     * @param regState
     * @param reg
     * @param studentContract
     * @return
     */
    BankRegApply createApply(String regState, BankReg reg, StudentContract studentContract) {
        BankRegApply apply = new BankRegApply();
        apply.setStunum(reg.getStunum());
        apply.setInscode(reg.getInscode());
        apply.setInsname(reg.getInsname());
        apply.setCreateTime(new Date());
        apply.setStatus(0);
        apply.setBankcode(reg.getBankcode());
        apply.setBankname(reg.getBankname());
        apply.setStuname(reg.getStuname());
        apply.setIdcard(reg.getIdcard());
        apply.setRegState(regState);
        apply.setApplyType("1");
        //设置冻结金额
        setBalanceAmount(regState, studentContract, apply);
        return apply;
    }

    /**
     * 创建初始的退费订单
     *
     * @param regState
     * @param reg
     * @param balanceAmount
     * @return
     */
    BankRegApply createRefundApply(String regState, BankReg reg, BigDecimal balanceAmount) {
        BankRegApply apply = new BankRegApply();
        apply.setStunum(reg.getStunum());
        apply.setInscode(reg.getInscode());
        apply.setInsname(reg.getInsname());
        apply.setCreateTime(new Date());
        apply.setStatus(0);
        apply.setBankcode(reg.getBankcode());
        apply.setBankname(reg.getBankname());
        apply.setStuname(reg.getStuname());
        apply.setIdcard(reg.getIdcard());
        apply.setRegState(regState);
        apply.setApplyType("2");
        apply.setBalanceAmount(balanceAmount);
        return apply;
    }

    /**
     * @param regState        21二笔冻结第一笔（课堂+模拟）  22二笔冻结第二笔  31三笔冻结第一笔（课堂+模拟）  32三笔冻结第二笔（科二实操）   33三笔冻结第三笔（科三实操）
     * @param studentContract 合同
     * @param apply           申请
     * @return
     */
    void setBalanceAmount(String regState, StudentContract studentContract, BankRegApply apply) {
        //1 单笔冻结
        //21二笔冻结第一笔（课堂+模拟）  22二笔冻结第二笔（科二+科三实操）
        //31三笔冻结第一笔（课堂+模拟）  32三笔冻结第二笔（科二实操）   33三笔冻结第三笔（科三实操）
        //9 额外多笔冻结（与免单对应）
        BigDecimal balanceAmount = BigDecimal.ZERO;
        //if (StringUtils.equalsIgnoreCase(regState, "21") || StringUtils.equalsIgnoreCase(regState, "31")) {
        //    balanceAmount=balanceAmount.add(studentContract.getTotalTheoryCost()).add(studentContract.getTotalImiCost());
        //} else
        if (StringUtils.equalsIgnoreCase(regState, "22")) {
            apply.setSub2Cost(studentContract.getTotalSub2Cost());
            apply.setSub3Cost(studentContract.getTotalSub3Cost());
            balanceAmount = balanceAmount.add(studentContract.getTotalSub2Cost()).add(studentContract.getTotalSub3Cost());
        } else if (StringUtils.equalsIgnoreCase(regState, "32")) {
            apply.setSub2Cost(studentContract.getTotalSub2Cost());
            apply.setSub3Cost(BigDecimal.ZERO);
            balanceAmount = balanceAmount.add(studentContract.getTotalSub2Cost());
        } else if (StringUtils.equalsIgnoreCase(regState, "33")) {
            //可能存在部分科二尚未学完，故此处需要计算科二和科三的剩余冻结金额
            ApplyTimesDto applyTimes = schedulingApplyService.getStuApplyTimes(studentContract.getStunum());
            BigDecimal sub2De = studentContract.getTotalSub2Cost().subtract((studentContract.getSub2CostPer().multiply(BigDecimal.valueOf(applyTimes.getSub2OprApplyTimes()))));
            BigDecimal sub3De = studentContract.getTotalSub3Cost().subtract((studentContract.getSub3CostPer().multiply(BigDecimal.valueOf(applyTimes.getSub3OprApplyTimes()))));
            apply.setSub2Cost(sub2De);
            apply.setSub3Cost(sub3De);
            balanceAmount = balanceAmount.add(sub2De).add(sub3De);
        }
        apply.setBalanceAmount(balanceAmount);
    }
}
