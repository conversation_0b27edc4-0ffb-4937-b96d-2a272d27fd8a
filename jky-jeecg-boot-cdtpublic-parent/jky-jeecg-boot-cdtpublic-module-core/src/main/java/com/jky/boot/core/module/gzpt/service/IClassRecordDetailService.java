package com.jky.boot.core.module.gzpt.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.core.module.gzpt.entity.ClassRecordDetail;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jky.boot.core.module.gzpt.entity.TotalTime;
import com.jky.boot.zlb.vo.BrProVo;

import java.util.List;

/**
 * @Description: t_m_class_record_detail
 * @Author: jeecg-boot
 * @Date:   2023-04-06
 * @Version: V1.0
 */
public interface IClassRecordDetailService extends IService<ClassRecordDetail> {

    /**
     * 查询学时管理
     *
     * @param id 学时管理主键
     * @return 学时管理
     */
    ClassRecordDetail selectClassRecordDetailById(String id);

    /**
     * 查询学时管理列表
     *
     * @param classRecordDetail 学时管理
     * @return 学时管理集合
     */
    List<ClassRecordDetail> selectClassRecordDetailList(ClassRecordDetail classRecordDetail);

    /**
     * 新增学时管理
     *
     * @param classRecordDetail 学时管理
     * @return 结果
     */
    int insertClassRecordDetail(ClassRecordDetail classRecordDetail);

    /**
     * 修改学时管理
     *
     * @param classRecordDetail 学时管理
     * @return 结果
     */
    int updateClassRecordDetail(ClassRecordDetail classRecordDetail);

    /**
     * 批量删除学时管理
     *
     * @param ids 需要删除的学时管理主键集合
     * @return 结果
     */
    int deleteClassRecordDetailByIds(String[] ids);

    /**
     * 删除学时管理信息
     *
     * @param id 学时管理主键
     * @return 结果
     */
    int deleteClassRecordDetailById(String id);

    //生成报审
    String CrStageTrainningTime(TotalTime totalTime);

    String CrOrUpStageTrainningTime(TotalTime totalTime);

    //根据id查询
    List<ClassRecordDetail> findDetailByIds(String[] ids);

    int deleteClassRecordDetailByStunum(String stunum);

    boolean findClassRecord(ClassRecordDetail classRecordDetail);

    TotalTime getTotalTime(ClassRecordDetail classRecordDetail, int type, TotalTime totalTime);

    boolean isbaosen(TotalTime totalTime);

    List<ClassRecordDetail> selectkc(String subjcode,String id,String type);

    IPage<ClassRecordDetail> selectqb(Page<ClassRecordDetail> page, String subjcode,String id,String type);

    List<ClassRecordDetail> selectyc(String subjcode,String id,String type);

    List<ClassRecordDetail> selectmn(String subjcode,String id,String type);

    List<ClassRecordDetail> selectsc(String subjcode,String id,String type);

    /**
     * 给定大纲、科目、车型，查找学时分类
     */
    List<BrProVo> getBrType(Integer outlineType, String subCode, String carType);


    /**
     * 查询学员指定科目的学时进度
     */
    List<BrProVo> queryBrPro(String stuNum, String subCode);
}
