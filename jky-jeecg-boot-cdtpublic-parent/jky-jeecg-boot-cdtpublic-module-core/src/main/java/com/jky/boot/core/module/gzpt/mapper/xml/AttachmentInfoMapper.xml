<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.boot.core.module.gzpt.mapper.AttachmentInfoMapper">
  <resultMap id="BaseResultMap" type="com.jky.boot.core.module.gzpt.entity.AttachmentInfo">
    <!--@mbg.generated-->
    <!--@Table attachment_info-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="link_id" jdbcType="BIGINT" property="linkId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="sub_type" jdbcType="VARCHAR" property="subType" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_delete" jdbcType="VARCHAR" property="isDelete" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, link_id, `type`, sub_type, content, create_time, update_time, is_delete
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from attachment_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from attachment_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.jky.boot.core.module.gzpt.entity.AttachmentInfo">
    <!--@mbg.generated-->
    insert into attachment_info (id, link_id, `type`, 
      sub_type, content, create_time, 
      update_time, is_delete)
    values (#{id,jdbcType=BIGINT}, #{linkId,jdbcType=BIGINT}, #{type,jdbcType=VARCHAR}, 
      #{subType,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.jky.boot.core.module.gzpt.entity.AttachmentInfo">
    <!--@mbg.generated-->
    insert into attachment_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="linkId != null">
        link_id,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="subType != null">
        sub_type,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="linkId != null">
        #{linkId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="subType != null">
        #{subType,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.jky.boot.core.module.gzpt.entity.AttachmentInfo">
    <!--@mbg.generated-->
    update attachment_info
    <set>
      <if test="linkId != null">
        link_id = #{linkId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="subType != null">
        sub_type = #{subType,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jky.boot.core.module.gzpt.entity.AttachmentInfo">
    <!--@mbg.generated-->
    update attachment_info
    set link_id = #{linkId,jdbcType=BIGINT},
      `type` = #{type,jdbcType=VARCHAR},
      sub_type = #{subType,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_delete = #{isDelete,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>