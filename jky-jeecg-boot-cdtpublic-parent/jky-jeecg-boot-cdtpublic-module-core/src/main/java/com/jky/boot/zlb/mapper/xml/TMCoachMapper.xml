<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jky.boot.zlb.mapper.TMCoachMapper">
    <select id="driver" resultType="com.jky.boot.zlb.entity.TMCoach">
        select a.*,b.num from (
            select NAME, COACHNUM, DRIPERMITTED, mark, penalize, age, teachpermitted,
                   extract(year from NOW()::date) - extract(year from hiredate::date) as year, photourl
            from t_m_coach where INSCODE =  #{inscode} and EMPLOYSTATUS = '0' and AUDITSTATE = 1 and `status` = 1
        )a left join (
            select COACHNUM ,count(0) num from t_m_studentinfo
            where INSCODE = #{inscode} group by COACHNUM
        )b
        on a.COACHNUM = b.COACHNUM
    </select>

    <select id="coach" resultType="com.jky.boot.zlb.entity.TMCoach">
        select * from t_m_coach
        where name = #{name} and IDCARD = #{idcard} and EMPLOYSTATUS = 0 and AUDITSTATE = 1 and STOP_TRAIN = 0 and STATUS = 1
    </select>


    <select id="selectcoach" resultType="com.jky.boot.zlb.entity.TMCoach">
        SELECT NAME,COACHNUM FROM t_m_coach
        where INSCODE = #{inscode} and EMPLOYSTATUS = '0' and AUDITSTATE = 1 and `status` = 1 and TEACHPERMITTED Like concat('%', #{teachpermitted}, '%')
    </select>

</mapper>