package com.jky.boot.core.module.gzpt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: 驾校评价统计
 * @Author: jeecg-boot
 * @Date: 2023-11-30
 * @Version: V1.0
 */
@Data
@TableName("t_m_eva_sum_ins")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "t_m_eva_sum_ins对象", description = "驾校评价统计")
public class EvaSumIns implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private Integer id;
    /**
     * 驾校编号
     */
    @Excel(name = "驾校编号", width = 15)
    @ApiModelProperty(value = "驾校编号")
    private String inscode;
    /**
     * 驾校名称
     */
    @Excel(name = "驾校名称", width = 15)
    @ApiModelProperty(value = "驾校名称")
    private String insname;
    /**
     * 课后评价
     */
    @Excel(name = "课后评价", width = 15)
    @ApiModelProperty(value = "课后评价")
    private Double classEva;
    /**
     * 考后评价
     */
    @Excel(name = "考后评价", width = 15)
    @ApiModelProperty(value = "考后评价")
    private Double examEva;
    /**
     * 总体评分
     */
    @Excel(name = "总体评分", width = 15)
    @ApiModelProperty(value = "总体评分")
    private Double overall;
}
