package com.jky.boot.zlb.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.common.utils.StringUtils;
import com.jky.boot.core.module.gzpt.entity.Carinfo;
import com.jky.boot.core.module.gzpt.entity.ClassRecordDetail;
import com.jky.boot.zlb.dto.CoachStudentTransferDto;
import com.jky.boot.zlb.dto.StudentSignInOut;
import com.jky.boot.zlb.dto.TrainBindCoachDto;
import com.jky.boot.zlb.dto.TrainBindStudentDto;
import com.jky.boot.zlb.entity.ApiTrainTime;
import com.jky.boot.zlb.service.ITrainingService;
import com.jky.boot.zlb.vo.ClDtlProVo;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 培训服务主要实现教练带教过程中的服务信息化，包括实人认证、绑定教练车、关联学员、教练签到签退、特殊学员签到签退、强制签退等功能
 */
@RestController
@RequestMapping("/zlb/training")
@Slf4j
public class TrainingController {

    @Autowired
    private ITrainingService trainingService;

    /**
     * 绑定/解绑教练车
     */
    @RequestMapping(value = "/bindCoachCar", method = RequestMethod.POST)
    public Result<?> bindCoachCar(@RequestBody TrainBindCoachDto bindCoachDto) {
        if (StringUtils.isBlank(bindCoachDto.getLicnum()) || null == bindCoachDto.getId()) {
            return Result.error("请输入浙里办id和车牌号");
        }
        return trainingService.bindCoachCar(bindCoachDto.getLicnum(), bindCoachDto.getId());
    }


    /**
     * 获取未被绑定的该驾校车辆
     */
    @RequestMapping(value = "/getNotBindCarList", method = RequestMethod.GET)
    public Result<?> getCoachCarList(@RequestParam(name = "id") String id,
                                     @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                     @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        if (id == null) {
            return Result.error("请输入浙里办id");
        }
        Page<Carinfo> page = new Page<>(pageNo, pageSize);
        return trainingService.getNotBindCarList(page, id);
    }

    /**
     * 查看绑定教练车列表、绑定教练车详情
     */
    @RequestMapping(value = "/getBindedCarList", method = RequestMethod.GET)
    public Result<?> getBindedCarList(@RequestParam(name = "id", required = false) String id) {
        if (id == null) {
            return Result.error("请输入浙里办id");
        }
        return trainingService.getBindedCarList(id);
    }

    /**
     * 关联学员
     */
    @RequestMapping(value = "/bindStudents", method = RequestMethod.POST)
    public Result<?> bindStudents(@RequestBody TrainBindStudentDto trainBindStudent) {
        if (StringUtils.isBlank(trainBindStudent.getIdcard()) || null == trainBindStudent.getId()) {
            return Result.error("请输入学员信息和车牌号");
        }
        return trainingService.bindStudents(trainBindStudent.getIdcard(), trainBindStudent.getId());
    }

    /**
     * 查看、关联学员列表、关联学员详情
     */
    @RequestMapping(value = "/getBindedStudentList", method = RequestMethod.GET)
    public Result<?> getBindedStudentList(@RequestParam(name = "id", required = false) String id) {
        if (id == null) {
            return Result.error("请输入浙里办id");
        }
        return trainingService.getBindedStudentList(id);
    }

    /**
     * 教练签到
     */
    @RequestMapping(value = "/signInCoach", method = RequestMethod.GET)
    public Result<?> signInCoach(@RequestParam(name = "id", required = false) String id) {
        if (id == null) {
            return Result.error("请输入浙里办id");
        }
        return trainingService.signInCoach(id);
    }

    /**
     * 教练签退
     */
    @RequestMapping(value = "/signOutCoach", method = RequestMethod.GET)
    public Result<?> signOutCoach(@RequestParam(name = "id", required = false) String id) {
        if (id == null) {
            return Result.error("请输入浙里办id");
        }
        return trainingService.signOutCoach(id);
    }

    /**
     * 教练签到签退
     */
    @RequestMapping(value = "/signInOutCoach", method = RequestMethod.GET)
    public Result<?> signInOutCoach(@RequestParam(name = "id", required = false) String id) {
        if (id == null) {
            return Result.error("请输入浙里办id");
        }
        return trainingService.signInOutCoach(id);
    }

//    /**
//     * 教练签到签退
//     */
//    @RequestMapping(value = "/signInOutCoach", method = RequestMethod.GET)
//    public Result<?> signInOutCoach( @RequestParam(name="id", required = false) Long id) {
//        if (id == null) {
//            return Result.error("请输入浙里办id");
//        }
//        return trainingService.signInOutCoach(id);
//    }

    /**
     * 教练帮学生签到
     */
    @RequestMapping(value = "/signInStudent", method = RequestMethod.GET)
    public Result<?> signInStudent(@RequestParam(name = "idcard", required = false) String idcard,
                                   @RequestParam(name = "id", required = false) String id) {
        if (StringUtils.isBlank(idcard) || id == null) {
            return Result.error("请输入学员信息和车牌号");
        }
        return trainingService.signInStudent(idcard, id);
    }

    /**
     * 教练帮学生签退
     */
    @RequestMapping(value = "/signOutStudent", method = RequestMethod.GET)
    public Result<?> signOutStudent(@RequestParam(name = "idcard", required = false) String idcard,
                                    @RequestParam(name = "id", required = false) String id) {
        if (StringUtils.isBlank(idcard) || id == null) {
            return Result.error("请输入学员信息和车牌号");
        }
        return trainingService.signOutStudent(idcard, id);
    }

    /**
     * 特殊学员签到签退
     */
    @RequestMapping(value = "/signInOutStudent", method = RequestMethod.POST)
    public Result<?> signInOutStudent(@RequestBody StudentSignInOut studentSignInOut) {
        if (StringUtils.isBlank(studentSignInOut.getIdcard()) || null == studentSignInOut.getId()) {
            return Result.error("请输入学员信息和车牌号");
        }
        return trainingService.signInOutStudent(studentSignInOut.getIdcard(), studentSignInOut.getId());
    }

    /**
     * 学员自己签到
     */
    @RequestMapping(value = "/signInStudentBySelf", method = RequestMethod.GET)
    public Result<?> signInStudentBySelf(@RequestParam(name = "id") String id,
                                         @RequestParam(name = "longitude", required = false) String longitude,
                                         @RequestParam(name = "latitude", required = false) String latitude) {
        if (StringUtils.isBlank(longitude) || StringUtils.isBlank(latitude) || id == null) {
            return Result.error("请输入浙里办id和经纬度");
        }
        return trainingService.signInStudentBySelf(id, longitude, latitude);
    }

    /**
     * 学员自己签退
     */
    @RequestMapping(value = "/signOutStudentBySelf", method = RequestMethod.GET)
    public Result<?> signOutStudentBySelf(@RequestParam(name = "id") String id,
                                          @RequestParam(name = "longitude", required = false) String longitude,
                                          @RequestParam(name = "latitude", required = false) String latitude) {
        if (StringUtils.isBlank(longitude) || StringUtils.isBlank(latitude) || id == null) {
            return Result.error("请输入浙里办id和经纬度");
        }
        return trainingService.signOutStudentBySelf(id, longitude, latitude);
    }


    /**
     * 查询该学员打卡记录，默认当月，可传时间区间
     */
    @RequestMapping(value = "/getSignRecordList", method = RequestMethod.GET)
    public Result<?> getSignRecordList(@RequestParam(name = "id") String id,
                                       @RequestParam(name = "beginDate", required = false) String beginDate,
                                       @RequestParam(name = "endDate", required = false) String endDate) {
        return trainingService.getSignRecordList(id, beginDate, endDate);
    }

    /**
     * 查询该学员当天是否已经打卡的状态
     */
    @RequestMapping(value = "/getStudentSignStatus", method = RequestMethod.GET)
    public Result<?> getStudentSignStatus(@RequestParam(name = "id") String id) {
        return trainingService.getStudentSignStatus(id);
    }


    /**
     * 教练员个人详情
     */
    @RequestMapping(value = "/getCoachInfo", method = RequestMethod.GET)
    public Result<?> getCoachInfo(@RequestParam(name = "id", required = false) String id) {
        if (StringUtils.isBlank(id)) {
            return Result.error("请输入浙里办id");
        }
        return trainingService.getCoachInfo(id);
    }


    /**
     * 继续教育
     */
    @RequestMapping(value = "/getCoachEdu", method = RequestMethod.GET)
    public Result<?> getCoachEdu(@RequestParam(name = "id") String id,
                                 @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                 @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        if (id == null) {
            return Result.error("请输入浙里办id");
        }
        Page<ApiTrainTime> page = new Page<>(pageNo, pageSize);
        return trainingService.getCoachEdu(page, id);
    }


    /**
     * 教练员转校记录查询
     */
    @RequestMapping(value = "/getCoachTransfer", method = RequestMethod.GET)
    public Result<?> getCoachTransfer(@RequestParam(name = "id") String id) {
        if (id == null) {
            return Result.error("请输入浙里办id");
        }
        return trainingService.getCoachTransfer(id);
    }


    /**
     * 教练员转校
     */
    @RequestMapping(value = "/coachTransfer", method = RequestMethod.POST)
    public Result<?> coachTransfer(@RequestBody CoachStudentTransferDto coachStudentTransferDto) {
        if (StringUtils.isBlank(coachStudentTransferDto.getInsName()) || null == coachStudentTransferDto.getId()) {
            return Result.error("请输入驾校名称和浙里办id");
        }
        return trainingService.coachTransfer(coachStudentTransferDto.getId(), coachStudentTransferDto.getInsName());
    }

    /**
     * 学员转校记录查询
     */
    @RequestMapping(value = "/getStudentTransfer", method = RequestMethod.GET)
    public Result<?> getCoachTransfer(@RequestParam(name = "id") String id, @RequestParam String idcard) {
        if (id == null || StringUtils.isBlank(idcard)) {
            return Result.error("请输入浙里办id和学员信息");
        }
        return trainingService.getStudentTransfer(id, idcard);
    }


    /**
     * 学员转校
     */
    @RequestMapping(value = "/studentTransfer", method = RequestMethod.POST)
    public Result<?> studentTransfer(@RequestBody CoachStudentTransferDto coachStudentTransferDto) {
        if (StringUtils.isBlank(coachStudentTransferDto.getInsName()) || StringUtils.isBlank(coachStudentTransferDto.getIdcard())
                || null == coachStudentTransferDto.getId()) {
            return Result.error("请输入驾校名称和浙里办id");
        }
        return trainingService.studentTransfer(coachStudentTransferDto.getId(), coachStudentTransferDto.getIdcard(), coachStudentTransferDto.getInsName());
    }

    @ApiOperation(value = "学员学时进度", notes = "学员学时进度")
    @GetMapping(value = "/stu/classRecordDetail/brPro")
    public Result<?> brPro(ClassRecordDetail clRecDtl) {
        String subjcode = clRecDtl.getSubjcode();
        String stunum = clRecDtl.getStunum();
        String trainCarType = clRecDtl.getTrainCarType();
        ClDtlProVo res = trainingService.brPro(subjcode, stunum, trainCarType);
        return Result.ok(res);
    }
}
