package com.jky.boot.core.module.gzpt.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jky.boot.core.module.gzpt.entity.BankRegApply;
import com.jky.boot.core.module.gzpt.service.IBankRegApplyService;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import com.jky.crypto.annotation.DesensitizedAnno;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @Description: 学员多笔冻结申请
 * @Author: jeecg-boot
 * @Date: 2023-04-06
 * @Version: V1.0
 */
@Api(tags = "学员多笔冻结申请")
@RestController
@RequestMapping("/gzpt/bankRegApply")
@Slf4j
public class BankRegApplyController extends JeecgController<BankRegApply, IBankRegApplyService> {
    @Autowired
    private IBankRegApplyService bankRegApplyService;

    /**
     * 分页列表查询
     */
    @GetMapping(value = "/list")
    @DesensitizedAnno
    public Result<IPage<BankRegApply>> queryPageList(BankRegApply apply,
                                                     @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                     @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                     HttpServletRequest req) {
        List<String> inscode = JkSecurityUtils.getOrgcodeBeMixedChoiceRoleBydeptId(apply.getInscode());
        apply.setInscode(null);
        QueryWrapper<BankRegApply> queryWrapper = QueryGenerator.initQueryWrapper(apply, req.getParameterMap());
        //申请类型 1 冻结  2 解冻
        queryWrapper.eq("apply_type", 2);
        queryWrapper.in("inscode", inscode);
        queryWrapper.orderByDesc("create_time");
        Page<BankRegApply> page = new Page<>(pageNo, pageSize);
        IPage<BankRegApply> pageList = bankRegApplyService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 审核
     *
     * @param apply
     * @return
     */
    @AutoLog(value = "学员多笔冻结申请-审核")
    @ApiOperation(value = "学员多笔冻结申请-审核", notes = "学员多笔冻结申请-审核")
    @RequiresPermissions("gzpt:bankRegApply:audit")
    @PostMapping(value = "/audit")
    public Result<?> audit(@RequestBody BankRegApply apply) {
        return bankRegApplyService.applyAudit(apply);
    }

}
