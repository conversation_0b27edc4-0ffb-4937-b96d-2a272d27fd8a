package com.jky.boot.annotation.aspect;

import com.jky.boot.annotation.RedissonLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.jeecg.boot.starter.lock.client.RedissonLockClient;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

/**
 * 分布式锁切面
 *
 * <AUTHOR>
 * @version 2024-06-24 10:24
 */

@Slf4j
@Aspect
@Component
public class RedissonLockAspect {
    private final RedissonLockClient redissonLock;

    public RedissonLockAspect(RedissonLockClient redissonLock) {
        this.redissonLock = redissonLock;
    }

    /**
     * SpEL表达式解析
     */
    private final SpelExpressionParser spelExpressionParser = new SpelExpressionParser();

    /**
     * 用于获取方法参数名字
     */
    private final DefaultParameterNameDiscoverer nameDiscoverer = new DefaultParameterNameDiscoverer();

    @Pointcut("@annotation(com.jky.boot.annotation.RedissonLock)")
    public void redissonLock() {
    }

    @Around("redissonLock() && @annotation(annotation)")
    public Object aroundRedisson(ProceedingJoinPoint pjp, RedissonLock annotation) throws Throwable {
        String key = null;
        try {
            key = getKey(pjp, annotation);
            if (!redissonLock.tryLock(key, annotation.waitTime(), annotation.lockTime())) {
                throw new RuntimeException("请勿重复提交");
            }
            return pjp.proceed();
        } finally {
            // 解锁
            if (!StringUtils.isBlank(key)) {
                redissonLock.unlock(key);
            }
        }
    }

    private String getKey(ProceedingJoinPoint pjp, RedissonLock annotation) {
        String ori = annotation.key();
        if (StringUtils.isBlank(ori)) {
            throw new RuntimeException("Lock key cannot be blank");
        }
        String key = ori;
        if (key.contains("#")) {
            // 处理 spEL 表达式
            key = getValBySpEL(ori, (MethodSignature) pjp.getSignature(), pjp.getArgs());
        }
        String prefix = annotation.keyPrefix();
        return StringUtils.isBlank(prefix) ? key : prefix + key;
    }


    /**
     * 解析 spEL 表达式
     *
     * @param spEL            spEL表达式
     * @param methodSignature 方法签名
     * @param args            实际参数
     * @return 表达式的值
     */
    private String getValBySpEL(String spEL, MethodSignature methodSignature, Object[] args) {
        // 获取方法形参名数组
        String[] paramNames = nameDiscoverer.getParameterNames(methodSignature.getMethod());
        if (paramNames == null || paramNames.length < 1) {
            throw new RuntimeException("Lock key cannot be empty");
        }
        Expression expression = spelExpressionParser.parseExpression(spEL);
        // spring的表达式上下文对象
        EvaluationContext context = new StandardEvaluationContext();
        // 给上下文赋值（形参，实参）
        for (int i = 0; i < args.length; i++) {
            context.setVariable(paramNames[i], args[i]);
        }
        Object value = expression.getValue(context);
        if (value == null) {
            throw new RuntimeException("The parameter value cannot be null");
        }
        return value.toString();
    }
}
