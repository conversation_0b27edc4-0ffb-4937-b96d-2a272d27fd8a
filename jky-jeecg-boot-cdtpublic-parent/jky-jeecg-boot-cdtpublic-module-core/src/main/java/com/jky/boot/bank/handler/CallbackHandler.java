package com.jky.boot.bank.handler;

import com.alibaba.fastjson.JSONObject;
import com.alipay.api.msg.MsgHandler;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jky.boot.bank.vo.FundTransferVo;
import com.jky.boot.bank.vo.OrderStatusVo;
import com.jky.boot.base.dao.MsgReplyAlipay;
import com.jky.boot.base.service.IMsgReplyAlipayService;
import com.jky.boot.core.module.gzpt.entity.BankOrder;
import com.jky.boot.core.module.gzpt.entity.BankReg;
import com.jky.boot.core.module.gzpt.service.IBankOrderService;
import com.jky.boot.core.module.gzpt.service.IBankRegService;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.boot.starter.lock.client.RedissonLockClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.Objects;

/**
 * 划转消息回调处理
 *
 * <AUTHOR>
 * @version 2024-12-12 15:37
 */
@Slf4j
@Component
public class CallbackHandler implements MsgHandler {


    @Autowired
    private IBankRegService bankRegService;
    @Autowired
    private RedissonLockClient redissonLock;
    @Autowired
    private IBankOrderService bankOrderService;
    @Autowired
    private IMsgReplyAlipayService msgReplyAlipayService;

    private static final String FUND_TRANSFER_MSG = "alipay.ebpp.industry.supervision.fundtransfer.notify";
    private static final String ORDER_STATUS = "alipay.ebpp.industry.supervision.orderstatus.notify";

    @Override
    public void onMessage(String msgApi, String msgId, String bizContent) {
        // 消息重复推送校验
        long count = msgReplyAlipayService.count(
                Wrappers.<MsgReplyAlipay>lambdaQuery()
                        .eq(MsgReplyAlipay::getMsgId, msgId)
        );
        if (count > 0) {
            return;
        }
        // 保存消息
        String regId = saveLog(msgApi, msgId, bizContent);
        // 处理消息
        String key = "alipay:regId:" + regId;
        try {
            if (redissonLock.tryLock(key, 3, 6)) {
                if (msgApi.equals(FUND_TRANSFER_MSG)) {
                    FundTransferVo msg = JSONObject.parseObject(bizContent, FundTransferVo.class);
                    fundTransferCallback(msg);
                } else if (msgApi.equals(ORDER_STATUS)) {
                    OrderStatusVo msg = JSONObject.parseObject(bizContent, OrderStatusVo.class);
                    orderStatusCallback(msg);
                }
            }
        } catch (Exception e) {
            log.error("接收消息失败：", e);
        } finally {
            redissonLock.unlock(key);
        }
    }


    /**
     * 划转消息处理
     *
     * @param msg 划转消息
     */
    private void fundTransferCallback(FundTransferVo msg) {
        log.info("FundTransferVo:{}", msg);
        // 仅开户、未冻结的 order_release，直接跳过,这里先注释，未冻结不会走到这里
//        if ("ORDER_RELEASE".equals(msg.getTransferScene()) && msg.getAmount() == 0) {
//            return;
//        }
        // 金额转换
        BigDecimal amount = BigDecimal.valueOf(msg.getAmount()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
        // 转入冻结金额
        if ("TRANSFER_IN".equals(msg.getTransferScene())) {
            BankReg reg = bankRegService.getById(msg.getRelationOutOrderNo());
            BigDecimal cur = Objects.isNull(reg.getRemainingAmount()) ? BigDecimal.valueOf(0) : reg.getRemainingAmount();
            reg.setRemainingAmount(cur.add(amount));
            bankRegService.updateById(reg);
        }
        // 划转操作
        if ("TRANSFER_OUT".equals(msg.getTransferScene())) {
            BankReg reg = bankRegService.getById(msg.getRelationOutOrderNo());
            BankOrder order = bankOrderService.getById(msg.getOutFlowId());
            if ("SUCCESS".equals(msg.getTransferStatus())) {
                order.setStatus(1L);
                order.setSerialno(msg.getOperateNo());
                order.setRemark("交易完成");
                order.setBalanceAmount(reg.getRemainingAmount().subtract(amount));
                order.setBankdate(new Date());
                reg.setRemainingAmount(order.getBalanceAmount());
            } else {
                order.setStatus(2L);
                order.setRemark("交易失败");
            }
            bankRegService.updateById(reg);
            bankOrderService.updateById(order);
        }
        // 解冻操作
        if ("ORDER_RELEASE".equals(msg.getTransferScene())) {
            BankReg reg = bankRegService.getById(msg.getRelationOutOrderNo());
            BankOrder order = bankOrderService.getById(msg.getOutFlowId());
            if ("SUCCESS".equals(msg.getTransferStatus())) {
                order.setStatus(1L);
                order.setSerialno(msg.getOperateNo());
                order.setRemark("交易完成");
                order.setBankdate(new Date());
                order.setBalanceAmount(BigDecimal.ZERO);
                reg.setRemainingAmount(BigDecimal.ZERO);
            } else {
                order.setStatus(2L);
                order.setRemark("交易失败");
            }
            bankRegService.updateById(reg);
            bankOrderService.updateById(order);
        }
    }

    /**
     * 订单消息处理
     *
     * 订单状态
     * 枚举值
     * 初始化: INIT
     * 待转入: WAIT_FOR_TRANSFERRING
     * 转入中: TRANSFERRING
     * 已冻结: FROZEN
     * 释放中: RELEASING
     * 已释放: RELEASE
     * 关闭释放中: RELEASING_FOR_CLOSING
     * 已关闭: CLOSED
     * @param msg 订单消息
     */
    private void orderStatusCallback(OrderStatusVo msg) {
        log.info("OrderStatusVo:{}", msg);
        BankReg reg = bankRegService.getById(msg.getOutOrderNo());
        // release-冻结后释放，closed-仅开户后关闭账户
        if ("RELEASE".equals(msg.getOrderStatus()) || "CLOSED".equals(msg.getOrderStatus())) {
            reg.setStatus(0);
        }
        // transferring-转入金额少于应冻结金额
        if ("TRANSFERRING".equals(msg.getOrderStatus())) {
            reg.setStatus(4);
        }
        // frozen-转入金额大于等于应冻结金额，冻结成功后再充钱不会发送消息了
        if ("FROZEN".equals(msg.getOrderStatus())) {
            reg.setStatus(3).setBankdate(new Date());
        }
        bankRegService.updateById(reg);
    }

    /**
     * 保存消息, 并返回 reg 的 id，当作锁的参数
     */
    private String saveLog(String msgApi, String msgId, String bizContent) {
        String regId;
        // 处理消息
        if (msgApi.equals(FUND_TRANSFER_MSG)) {
            FundTransferVo msg = JSONObject.parseObject(bizContent, FundTransferVo.class);
            saveFundTransferLog(msgId, msg);
            regId = msg.getRelationOutOrderNo();
        } else {
            OrderStatusVo msg = JSONObject.parseObject(bizContent, OrderStatusVo.class);
            saveOrderStatusLog(msgId, msg);
            regId = msg.getOutOrderNo();
        }
        return regId;
    }

    /**
     * 划转消息保存
     */
    private void saveFundTransferLog(String msgId, FundTransferVo msgVo) {
        MsgReplyAlipay alipayMsg = new MsgReplyAlipay();
        alipayMsg.setMsgApi("fundtransfer").setMsgId(msgId).setBizContent(JSONObject.toJSONString(msgVo));
        // 填充学员编号
        BankReg reg = bankRegService.getById(msgVo.getRelationOutOrderNo());
        alipayMsg.setStunum(reg.getStunum());
        msgReplyAlipayService.save(alipayMsg);
    }

    /**
     * 订单消息保存
     */
    private void saveOrderStatusLog(String msgId, OrderStatusVo msgVo) {
        MsgReplyAlipay alipayMsg = new MsgReplyAlipay();
        alipayMsg.setMsgApi("orderstatus").setMsgId(msgId).setBizContent(JSONObject.toJSONString(msgVo));
        // 填充学员编号
        BankReg reg = bankRegService.getById(msgVo.getOutOrderNo());
        alipayMsg.setStunum(reg.getStunum());
        msgReplyAlipayService.save(alipayMsg);
    }
}
