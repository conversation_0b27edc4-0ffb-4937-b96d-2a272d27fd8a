package com.jky.boot.core.module.gzpt.service.impl;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jky.boot.common.utils.CommonResponse;
import com.jky.boot.common.utils.EnvControl;
import com.jky.boot.common.utils.KeepHttpUtilQG;
import com.jky.boot.core.module.gzpt.dto.BankTransDto;
import com.jky.boot.core.module.gzpt.entity.*;
import com.jky.boot.core.module.gzpt.mapper.*;
import com.jky.boot.core.module.gzpt.service.IInstitutionService;
import com.jky.boot.core.module.gzpt.service.IStuTransferService;
import com.jky.boot.core.module.gzpt.service.IStudentinfoService;
import com.jky.boot.core.module.gzpt.utils.CancelAccountUtils;
import com.jky.boot.core.module.gzpt.utils.PushBankUtil;
import com.jky.boot.core.module.gzpt.utils.UrlAddressUtils;
import com.jky.boot.system.module.manage.util.JkSecurityUtils;
import com.jky.boot.zlb.entity.TMSupervise;
import com.jky.boot.zlb.mapper.TMSuperviseMapper;
import com.jky.boot.zlb.service.ITMSuperviseService;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 学员转校
 *
 * <AUTHOR>
 * @version 2023-12-18
 */
@Service
public class StuTransferServiceImpl extends ServiceImpl<StuTransferMapper, StuTransfer> implements IStuTransferService {
    @Autowired
    private BankRegMapper bankRegMapper;
    @Autowired
    private BankOrderMapper bankOrderMapper;
    @Autowired
    private InstitutionMapper institutionMapper;
    @Autowired
    private StudentinfoMapper studentinfoMapper;
    @Autowired
    private TMSuperviseMapper superviseMapper;

//    @Override
//    @SneakyThrows
//    public Result<?> trans(BigDecimal transMoney, Studentinfo stu, Integer type) {
//        BankReg one = bankRegMapper.selectOne(
//                Wrappers.lambdaQuery(BankReg.class)
//                        .eq(BankReg::getStunum, stu.getStunum())
//        );
////        String orderNo = DateIdUtil.getSeq();
//        // 保存 bankOrder
//        BankOrder bankOrder = genBankOrder(transMoney, one, stu, type);
//        bankOrder.setTransFlag(type);
//        bankOrderMapper.insert(bankOrder);
//        BankTransDto res = PushBankUtil.genTransMsg(bankOrder);
//        JSONObject resJson = JSONObject.parseObject(JSONObject.toJSONString(res));
//        String resp = PushBankUtil.pushFundTransfer(3, resJson, stu.getBankcode());
//        CommonResponse response = JSONObject.parseObject(resp, CommonResponse.class);
//        if (response.getErrorcode() != 0) {
//            return Result.error(response.getMessage());
//        }
//        return Result.ok("ok");
//    }

    @Override
    public Result<?> transferApply(StuTransfer transfer) {
        String stuNum = transfer.getStunum();
        String insCode = JkSecurityUtils.getOrgCodeByUnknown(transfer.getInscode());
        if (StringUtils.isBlank(stuNum)) {
            return Result.error("未推送全国！");
        }
        Studentinfo stu = studentinfoMapper.selectOne(Wrappers.<Studentinfo>lambdaQuery().eq(Studentinfo::getStunum,stuNum).last("limit 1"));
        if (Objects.isNull(stu)) {
            return Result.error("该学员不存在！");
        }
        // 转入驾校不可为当前驾校
        Institution newIns = institutionMapper.selectOne(Wrappers.<Institution>lambdaQuery().eq(Institution::getInscode, insCode).last("limit 1"));
        if (stu.getInscode().equals(newIns.getInscode())) {
            return Result.error("转入转出驾校一致，请重新选择！");
        }
        // 转入驾校不可为重点关注驾校
        TMSupervise one1 = superviseMapper.selectOne(Wrappers.<TMSupervise>lambdaQuery().eq(TMSupervise::getObjnum, insCode).eq(TMSupervise::getState, 1).orderByDesc(TMSupervise::getExpiretime).last("limit 1"));
        if (Objects.nonNull(one1)) {
            if (!(one1.getState() == 0 || new Date().getTime() > one1.getExpiretime().getTime())) {
                return Result.error("该驾校在重点监管名单中，不允许转入");
            }
        }
        // 开户校验
        Result<Integer> regRes = CancelAccountUtils.regCheck(stu);
        if (!regRes.isSuccess()) {
            return regRes;
        }
        // 是否能转校（注销互斥 + 重复提交 + 预约记录划钱）
        Result<?> result = CancelAccountUtils.canTransOrLogout(stu.getStunum());
        if (!result.isSuccess()) {
            return result;
        }
        // 监管备案校验
        if (EnvControl.JG_SWITCH) {
            JSONObject json = new JSONObject();
            json.put("idCard", stu.getIdcard());
            json.put("trainType", stu.getTraintype());
            CommonResponse commonResponse = KeepHttpUtilQG.sendHttpClientPostSJ(UrlAddressUtils.jgptUrl + "/sx/studentCheck", json.toString());
            int errorCode = commonResponse.getErrorcode();
            if (errorCode == 1) {
                return Result.error("监管未备案学员，不支持转校业务");
            }
        }
        // 转出驾校倒闭的，转出审核自动通过
        List<Institution> bankruptIns = institutionMapper.selectList(
                Wrappers.<Institution>lambdaQuery()
                        .eq(Institution::getIsBankruptcy, 1)
        );
        List<String> insCodeSet = bankruptIns.stream().map(Institution::getInscode).collect(Collectors.toList());
        StuTransfer res = genVo(stu, newIns);
        if (insCodeSet.contains(res.getOldInscode())) {
            res.setInsAudit(1);
            res.setInsReason("倒闭驾校自动审核");
            res.setInsAuditDate(new Date());
        }
        res.setFreeze(regRes.getResult());
        res.setStuReason(transfer.getStuReason());
        this.save(res);
        return Result.ok("提交申请成功！");
    }

    private static BankOrder genBankOrder(BigDecimal transMoney, BankReg bankReg, Studentinfo stu, Integer type) {
        BankOrder bankOrder = new BankOrder();
//        bankOrder.setId(orderNo);
        bankOrder.setStunum(bankReg.getStunum());
        bankOrder.setInscode(bankReg.getInscode());
        bankOrder.setInsname(bankReg.getInsname());
        bankOrder.setCreatedate(new Date());
        bankOrder.setStatus(0L);
        bankOrder.setBankcode(bankReg.getBankcode());
        bankOrder.setBankname(bankReg.getBankname());
        bankOrder.setStuname(bankReg.getStuname());
        bankOrder.setTransferAmount(transMoney);
        bankOrder.setIdcard(stu.getIdcard());
//        if (type == 3) {
//            bankOrder.setOrdertype(7L);
//        }
//        if (type == 2) {
//            bankOrder.setOrdertype(6L);
//        }
//        if (type == 1) {
//            bankOrder.setOrdertype(5L);
//        }
        return bankOrder;
    }

    /**
     * 组装类
     */
    private StuTransfer genVo(Studentinfo stu, Institution newIns) {
        StuTransfer res = new StuTransfer();
        res.setName(stu.getName());
        res.setIdcard(stu.getIdcard());
        res.setOldInscode(stu.getInscode());
        res.setOldInsname(stu.getInsname());
        res.setStunum(stu.getStunum());
        res.setInscode(newIns.getInscode());
        res.setInsname(newIns.getName());
        return res;
    }
}
