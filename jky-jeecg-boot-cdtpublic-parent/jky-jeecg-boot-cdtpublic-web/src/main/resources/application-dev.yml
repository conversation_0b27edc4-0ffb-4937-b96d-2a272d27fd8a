server:
  port: 8428
  tomcat:
    max-swallow-size: -1
  error:
    include-exception: true
    include-stacktrace: ALWAYS
    include-message: ALWAYS
  servlet:
    context-path: /quzgzpt
  compression:
    enabled: true
    min-response-size: 1024
    mime-types: application/javascript,application/json,application/xml,text/html,text/xml,text/plain,text/css,image/*

management:
  endpoints:
    enabled-by-default: false

spring:
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
  mail:
    host: smtp.163.com
    username: jee<PERSON><PERSON>@163.com
    password: ??
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
  ## quartz定时任务,采用数据库方式
  quartz:
    job-store-type: jdbc
    initialize-schema: embedded
    #定时任务启动开关，true-开  false-关
    auto-startup: false
    #延迟1秒启动定时任务
    startup-delay: 1s
    #启动时更新己存在的Job
    overwrite-existing-jobs: true
    properties:
      org:
        quartz:
          scheduler:
            instanceName: MyScheduler
            instanceId: AUTO
          jobStore:
            class: org.springframework.scheduling.quartz.LocalDataSourceJobStore
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
            tablePrefix: QRTZ_
            isClustered: true
            misfireThreshold: 12000
            clusterCheckinInterval: 15000
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 10
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true
  #json 时间戳统一转换
  jackson:
    date-format:   yyyy-MM-dd HH:mm:ss
    time-zone:   GMT+8
  jpa:
    open-in-view: false
  aop:
    proxy-target-class: true
  #配置freemarker
  freemarker:
    # 设置模板后缀名
    suffix: .ftl
    # 设置文档类型
    content-type: text/html
    # 设置页面编码格式
    charset: UTF-8
    # 设置页面缓存
    cache: false
    prefer-file-system-access: false
    # 设置ftl文件路径
    template-loader-path:
      - classpath:/templates
  # 设置静态文件路径，js,css等
  mvc:
    static-path-pattern: /**
    #Spring Boot 2.6+后映射匹配的默认策略已从AntPathMatcher更改为PathPatternParser,需要手动指定为ant-path-matcher
    pathmatch:
      matching-strategy: ant_path_matcher
  resource:
    static-locations: classpath:/static/,classpath:/public/
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
        allow:
      web-stat-filter:
        enabled: true
    dynamic:
      druid: # 全局druid参数，绝大部分值和默认保持一致。(现已支持的参数如下,不清楚含义不要乱设置)
        # 连接池的配置信息
        # 初始化大小，最小，最大
        initial-size: 5
        min-idle: 5
        maxActive: 20
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        # 打开PSCache，并且指定每个连接上PSCache的大小
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
        filters: stat,wall,slf4j
        # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      datasource:
        master:
          driver-class-name: org.postgresql.Driver
          #          url: ***************************************************************************************************************************************************************************
          #          username: cdt_jp
          #          password: cdt_jp_0614
          url: ********************************************************************,SYS_CATALOG
          username: cdtmanage
          password: test_cdtmanage01-18-10
          #driver-class-name: com.mysql.cj.jdbc.Driver
  #redis 配置
  redis:
    database: 6
    host: ************
    port: 6379
    password: fsry
#mybatis plus 设置
mybatis-plus:
  mapper-locations: classpath*:org/jeecg/modules/**/xml/*Mapper.xml,classpath*:com/jky/boot/**/xml/*Mapper.xml
  global-config:
    # 关闭MP3.0自带的banner
    banner: false
    db-config:
      #主键类型  0:"数据库ID自增",1:"该类型为未设置主键类型", 2:"用户输入ID",3:"全局唯一ID (数字类型唯一ID)", 4:"全局唯一ID UUID",5:"字符串全局唯一ID (idWorker 的字符串表示)";
      id-type: ASSIGN_ID
      # 默认数据库表下划线命名
      table-underline: true
  configuration:
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 返回类型为Map,显示null对应的字段
    call-setters-on-nulls: true
#jeecg专用配置
minidao:
  base-package: org.jeecg.modules.jmreport.*
jeecg:
  # 是否启用安全模式
  safeMode: false
  # 签名密钥串(前后端要一致，正式发布请自行修改)
  signatureSecret: dd05f1c54d63749eda95f9fa6d49v442a
  # 签名拦截接口
  signUrls: /sys/dict/getDictItems/*,/sys/dict/loadDict/*,/sys/dict/loadDictOrderByValue/*,/sys/dict/loadDictItem/*,/sys/dict/loadTreeData,/sys/api/**
  #local、minio、alioss
  uploadType: local
  # 前端访问地址
  domainUrl:
    pc: http://localhost:3100
    app: http://localhost:8051
  path:
    #文件上传根目录 设置
    #    upload: /opt/projects/cdtpublic/upFiles
    #    upload: /opt/uploadPath
    upload: E:/opt/uploadPath
    #webapp文件路径
    webapp: /opt/webapp
  #    webapp: D://opt/webapp
  shiro:
    excludeUrls: /alipay/**,/sys/jky/cas/client/**,/pub/jky/**,/gzpt/classRecordDetail/add,/gzpt/stageTrainningTime/saveAuditstate,/sys/cas/**,/MR890/**,/zlb/login/**,/zlb/tmc/tMCoach/**,/zlb/studentEnter/**,/zlb/applyOnline/**,/gzpt/studentinfo/chackStudent
  #阿里云oss存储和大鱼短信秘钥配置
  oss:
    #    endpoint: oss-cn-hangzhou.aliyuncs.com
    #    accessKey: LTAI4GDArYcxPrYXqbVMjXKc
    #    secretKey: ******************************
    #    bucketName: jkyqstes
    #    accessKey: ydLAvUbqnW4b02FL
    #    secretKey: ******************************
    #    endpoint: oss-cn-shaoxing-sxdx-d01-a.ops.dxdtsxzwy.com
    #    bucketName: sx-jposs-166
    endpoint: oss-cn-hangzhou.aliyuncs.com
    accessKey: LTAI4GDArYcxPrYXqbVMjXKc
    secretKey: ******************************
    bucketName: jkyqstes
    staticDomain: https://static.jeecg.com
    proxyUrl: http://sxjp.jtj.sx.gov.cn:18086/sx-jposs-166
    expirationTime: 3600000
    upload: quzgzpt
    prod: false
  # ElasticSearch 6设置
  elasticsearch:
    cluster-name: jeecg-ES
    cluster-nodes: 127.0.0.1:9200
    check-enabled: false
  # 在线预览文件服务器地址配置
  file-view-domain: 127.0.0.1:8012
  # minio文件上传
  minio:
    minio_url: http://minio.jeecg.com
    minio_name: ??
    minio_pass: ??
    bucketName: otatest
  #大屏报表参数设置
  jmreport:
    mode: dev
    #数据字典是否进行saas数据隔离，自己看自己的字典
    saas: false
    #是否需要校验token
    is_verify_token: true
    #必须校验方法
    verify_methods: remove,delete,save,add,update
  #xxl-job配置
  xxljob:
    enabled: false
    adminAddresses: http://127.0.0.1:9080/xxl-job-admin
    appname: ${spring.application.name}
    accessToken: ''
    address: 127.0.0.1:30007
    ip: 127.0.0.1
    port: 30007
    logPath: logs/jeecg/job/jobhandler/
    logRetentionDays: 30
  #分布式锁配置
  redisson:
    address: ************:6379
    password: 'fsry'
    type: STANDALONE
    enabled: true
    database: 7
#cas单点登录
cas:
  auth:
    appKey: ''
    appSecret: ''
  prefixUrl: http://ywsn.jtyst.zj.gov.cn:7003/auth/oauth/check_token
  userInfo: http://ywsn.jtyst.zj.gov.cn:7003/admin/user/info
  logout: http://ywsn.jtyst.zj.gov.cn:7003/auth/token/logout
  deptDetail: http://ywsn.jtyst.zj.gov.cn:7003/admin/api/dept/getDeptById   #根据组织id获取组织信息
  orgDetail: http://ywsn.jtyst.zj.gov.cn:7003/admin/api/org/getSysOrgById   #根据机构id获取机构信息
  currentAndNextAndMember: http://ywsn.jtyst.zj.gov.cn:7003/admin/api/dept/getCurrentAndNextAndMember   #根据组织id获取本组织信息、直接下属组织列表以及用户列表
#Mybatis输出sql日志
logging:
  level:
    org.jeecg.modules.system.mapper: debug
#swagger
knife4j:
  #开启增强配置
  enable: true
  #开启生产环境屏蔽
  production: false
  basic:
    enable: false
    username: jeecg
    password: jeecg1314
#第三方登录
justauth:
  enabled: true
  type:
    GITHUB:
      client-id: ??
      client-secret: ??
      redirect-uri: http://sso.test.com:8080/jeecg-boot/sys/thirdLogin/github/callback
    WECHAT_ENTERPRISE:
      client-id: ??
      client-secret: ??
      redirect-uri: http://sso.test.com:8080/jeecg-boot/sys/thirdLogin/wechat_enterprise/callback
      agent-id: ??
    DINGTALK:
      client-id: ??
      client-secret: ??
      redirect-uri: http://sso.test.com:8080/jeecg-boot/sys/thirdLogin/dingtalk/callback
    WECHAT_OPEN:
      client-id: ??
      client-secret: ??
      redirect-uri: http://sso.test.com:8080/jeecg-boot/sys/thirdLogin/wechat_open/callback
  cache:
    type: default
    prefix: 'demo::'
    timeout: 1h
#第三方APP对接
third-app:
  enabled: false
  type:
    #企业微信
    WECHAT_ENTERPRISE:
      enabled: false
      #CORP_ID
      client-id: ??
      #SECRET
      client-secret: ??
      #自建应用id
      agent-id: ??
      #自建应用秘钥（新版企微需要配置）
      # agent-app-secret: ??
    #钉钉
    DINGTALK:
      enabled: false
      # appKey
      client-id: ??
      # appSecret
      client-secret: ??
      agent-id: ??



# 中国银行地址
CITICurl: http://IP:PORT/jxbank/msgnotify?v=1&ts=1212&user=
# 农商地址
RCBurl: http://**************:38080/rcb/msgnotify?v=1&ts=1212&user=

# 宁波银行地址
bankurl: http://IP:PORT/banksdk/msgnotify?v=1&ts=1212&user=
#建行测试
#JHbankurl: http://************:13307/ccbbank/msgsend?v=1&ts=1212&user=
JHbankurl: http://IP:PORT/ccbbank/msgsend?v=1&ts=1212&user=

JHbankurl2: http://IP:PORT/ccbbank
# 推送工行地址
ICBCbankurl: http://IP:PORT/icbbank/msgnotify?v=1&ts=1212&user=

#农行
ABCbankurl: http://IP:PORT/abcmsgnotify?v=1&ts=1212&user=



#服务器ip+端口号
jhgzurl: http://**************:8428/quzgzpt

#appUrl: http://zlb.aptstu.com/zjx
appUrl: https://IP:PORT/prod-api/app


cityCoode: 3308
cityName: 衢州

jgurl: http://IP:PORT/jxjgptjk/gzpt/querysubject

cret: /templates/pfx/hangzhouzhizheng.pfx
passcret: Zhizheng22



citySeal: /inscode_sx/


alipay:
  serverUrl: https://openapi.alipay.com/gateway.do
  appId: ****************
  privateKey: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCwwIVmHBq5uPm7r1Rshn5cFT8JNitBQuQDf/Jfz7UrvMBTigmVrLMl2jPkdEijfNsoJDiWQAtNra6oKHU4YM+pZzXoF0ULxmHgPNrPdSVjeJxsXsiHs978smOe1kxfOqSUmv0iZH/s91XNbCiw7pvpug0WbBC6a1IzDpgo901oYQN1zxKX5gsEtnEwXNzamvwLBXqqHgu9BhbPAmh5giobc6Si0keEZe6X54aIPW2RGwA1Eczyv4yPHVy1670BptaVmSgDOWlCV/2FvrrBJfNoq5oYTyHOEvfDhIaxfa7E92xxiKQcM6MYIKLE/eYvWS3FIythvlkzCk7/ba9fBOERAgMBAAECggEAUp87/FZI2+YuvuAfe7eG266Gz9yFbbVB9qzZdJYYX0CVOre88T1QZPJR7Ym55spXe7UqynlXj1EP4sU+vkLq74oydbJi8TUjzXlE41MkThI0NKg93sljBAazcK1F+AHmqxykFW6bKf8YGbVkdAil0yTIN2H+k9XVD87Tq9qmUZK48yrplqpoScaYKZamtldyqMmkihXXZ7cfOvKCDtntqPF7go456SATowzb88vQmydLudhkq9AMXVZIoRga42wPptVHTSuulJFdYrfztGd6Pc/eofTPMj1cdiw6BqXB0XDElLXVOy7kEbaPpX6pW8iJoZtoVkhjgUMeNEFIcCYUUQKBgQDXhRElFZqthSubpk73IyPAkPeQUBqsWgJzZ92P7z8TmgcZF4IWLNDZCB6rzeE4pC/0vjRSHifrpNFYJSjzb6WIi+dnjLPnRiT2bMZD8RmJ87Wz9YJWHaq1ab65c/1QIHhog6ucayoRZASntszyftv3DnnYOvLrmJeyBdYgfb8PjQKBgQDR813yk5V8xDu9pYpMIAU3g4t1yAXRI3eie62Mj2jlh4osXSVjF/CQd6N+H+bDArIljU9ByZ1AQL0yrFkHOmvoUvBy/I0DkQ2mUhvzUAJlQEwXkv10OuLCSrmswn8JgV57xf/pDoDY2o1VPwYVo1H5VYrZatOrXEU9+cWUZmUklQKBgQCkgUWc8OioGkY3ALK1oXTgv8a3uO0jMXE9dcKA4zVaWO+iD3eURZEEmFYQ/Y00Ft8kCSsWoz4FeEDy7ypa5pWLrH5tY9DOb5Q3JMARjC7aRdQkEPnzkaGD57tVA1wPlTlTTW3ob0QlEhsbiUTWtE5iPgJzMkvJ2SexExNmqZT04QKBgCLUNKmhQhNImoCDjcXDKhpjgCcx34UqVRZnXgsLX2N0cL+bVDB0MDxHHKA3uphvQg5outU3FRcrpjdZ0Asa7fvEho8MxnfpAFKvHGPoyfzyBa2DfPhjLGr+6n52ZYy2IW53ElUfStCusPOLWP8N8PNj9XzL9AFhdTsqzFQqCG1hAoGBAIS99doa3UwRvs0Pa6rrQL48P/WxGLTq0pxPQI89jh/il7ARvV74jAYQP3dGnQYbvX6Ke7HyICQRnKDHarffstWgA/qStPvoMzozXt5yaTDbSw3Fj+82ph8HuuFSyHPPM6DiqqSojt3jYFFQa6vDjhupOwq83cc3izYGoPABssFR
  publicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsxyMsupDgZ3nnebkEr6giYPokV/FKd8bpWMJf4pLgRwaKcFx1xx9Wh2WXQy29BiVafEPY+pY/D6RHeFsZIsmPoWB3jggapXN0Kh8Zy7TdndNdkjv7Za2iBwYlhIL0akBvtSPHLYMnkd7hZ2gj+QzP0MU6y1iHYhDPpuvWzGPVqP6yajk4Cfn/onjKMuuoorUjYQa6SsyDlEnm/ndU7XRL2NDpOi5pTBMHxc0EMiwk/8RvMOsuyX2FeNCp0UK787jKsRT4nIQS+DyC+eiuVGUHfr2N852GDX42oHpBy7GHe0QvL37iKNEdzENo5SiVLJG1Bl+zEYfETSd5qOG4UKEcQIDAQAB

#金华-平安银行
PABurl: http://**************:38080/pabank/msgnotify?v=1&ts=1212&user=

# 嘉兴
#zlb:
#  appId: **********
#  secretKey: BCDSGS_51d2629f2f97323a4a91a0999788e435
#  accessKey: BCDSGA_8cb97f8bcca1356e89ae2272a53a69a9

# 绍兴
zlb:
  appId: **********
  secretKey: BCDSGS_91ffc224fc6afff9ca528e4900daa51c
  accessKey: BCDSGA_ea29c74f9289b5fbc2cbfe57b0940153

white:
  ipList:
jky:
  crypto-mybatis:
    #必须配置（请用jasypt加密秘钥）
    #    default-key: jky@************ #测试
    default-key: Jkyjppt@********  #生产
    enabled: true

jpdata:
  appId: AP1117985329
  appSecurity: tRmMKQr1q3vVtV98KPkfa8xFog0nfGIV
  publicKey: MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE0njAX7bs9gNJIho3NO0CP7iuKfy9PgQrS1rcgj7ji1we/DeQ6XcX++QGoVJ0o5At13HpWFF2k4l4wSdQd0c/0g==
  privateKey: MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgY3sdzB0d9qcgKj/kc7aQk8uzB8PJ+ZBQ028Z+TJV4KigCgYIKoEcz1UBgi2hRANCAASv6ZPqbDDcTzcCRFHZacNEGhqZruhlsn+pQDgQChgx1knXqe6eSrRTKS8PdtfEpgeh/AkaL5iga+y0NdQQplKg
  baseUrl: http://************:8600
  aesKey: yxc@jky2023


RSA:
  publicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAq7VNbRgSZyN71mJiYTfxjeTTRazpFt0LgzxjqK9Wb/oM6pUMA9YDnUIf+/4kEtc2gHz2c1V+h+PdWgBdkHwWXhHeks+8kTklFVz1eB2iCFjv9DkCUI9xrM642ZWqRXtb6R9Zr2zkddbePnQOsHrp88YKsX76Sr+0OZC4mTXJ0sLhDudOmA6JJvgZQfrqSvDXEmwuEQTKHxE846vZNYMvTIwvPrVjNL4J+h3yMVx92MCGKcewR5QXsI/57cMAo/rDPJQLP0W0WsQ08BiZjnWYaXFQ4LF4fDJIujNvNPTWC3KHY2fy+jr9CPjXFwthniB8KJ6F5aPHN/d+V7SvBNQQJwIDAQAB
  privateKey: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCrtU1tGBJnI3vWYmJhN/GN5NNFrOkW3QuDPGOor1Zv+gzqlQwD1gOdQh/7/iQS1zaAfPZzVX6H491aAF2QfBZeEd6Sz7yROSUVXPV4HaIIWO/0OQJQj3GszrjZlapFe1vpH1mvbOR11t4+dA6weunzxgqxfvpKv7Q5kLiZNcnSwuEO506YDokm+BlB+upK8NcSbC4RBMofETzjq9k1gy9MjC8+tWM0vgn6HfIxXH3YwIYpx7BHlBewj/ntwwCj+sM8lAs/RbRaxDTwGJmOdZhpcVDgsXh8Mki6M2809NYLcodjZ/L6Ov0I+NcXC2GeIHwonoXlo8c3935XtK8E1BAnAgMBAAECggEAExpBeeCSs4y8Cza40S4DHpEVfAvnhpai4D/IqoyPJjgAVdytEIQvtMAVhNhaNQpiUtsUI/Kf+Ag+qPOfdSKI7oA4bcbUqKdd28mSuYE6dIlIcLgi7Ml4kQwSfopq/f3iVMYbGSwZIo77/KC2vaV+/5LJv8NrnwNCzqCuGMVmJZ8/+CNpm+fDT0mJz+5OM2WoU66ttBLTVDR4RrueznMG6sHpO/xrSY2E770VcJCJGZEgEiOyehAZXYxLEPhNwqHfIhlVLX0fX/Gsg5gUEtGMPwcvf6o/8P2M/GT92KTBwa/85pLeJS7BEPcFaJenBo0rp9qV3PTp8jGe4IyuB+fNgQKBgQDVeulcAtX6bbEeFBs6yI9cBqWiwnoLq5q7YtJ80snpPAaP8GMJGmxPx3X8JkZOCMwmG3QBIcnnVJxBKxt1EKOZ0T9ep6KHgMkM0T/LL2M/6qvQ48DkbCedt2d12RFJsopjo2XpWVFlNEp5U1lssoG0a+I8Pgd+4y9hOWVaDUno5wKBgQDN6H4dTYJ10+W30X2UIe8Go3VRK59OKZhEOeRL/GnxfOIJatg5PTomcD3P2qIedqhM+yD/CQxTD+B1ehm8+uXmQF9on0f+pF0aPe9lCiZJcneagLAggFjT4gnMJdSlk7peS/i+K+xtq+DPDHHSJw6+knzQx+vQa7CWG402rhd2wQKBgFxAtlGVxCRpfK3T4ISBoToSgUeZpSYf7UjnBrD8YlbRIsDXD+UiLwY22uyxGZalRJt/J51JnbfDH5cl6sKnAxv4idr8tEa8R6WrU1cZzlkTr1Yg5f2QI3qLhEEwIcFoT1eJiAPUHlGOzFQryawoY9a0/FouMA0MXjA0nnDQ50ynAoGBAIPuDjgqPIfQcCSaFpM+Ns+TFg0fHSBQqF42lZ9WKG9xK8cK7WWnZEXSrtgp5qwMLQO9+ENQgdQKnS/IPwuF2p3v6vG29MMkfv7cZjHgEe4HaLjW8YKxmbiQ4OQr7Rb2E3htxv/aVs9iTFV9TMRxABJ8+3ouTlqpVWioWI+Ldj9BAoGAG4LdpOSGOiKGJuvIjxJRAy4ikGyL+UIQfa1RvIVaL1F8JjAqnn4qp691k3QJxau+/FDOU41sW0P1XuwJQs/E8JEHDpwpd3cSUZR+wP3N1owFUsKaqGoKXwm17qkj7Och5KKwK4LQCr7Aaa5I4aL/8pucNTWzoqnCVxky05Qyx0I=


SM4Key: DC4BA668063AAB2F
envControl:
  ossSwitch: false
  jsSwitch: false
stuSkip: ****************
#高新是1比9,东航是2比8
separateAccountInst: '{"****************": "0.1", "****************": "0.2"}'
