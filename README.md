关于请求 使用 @/utils/request.js
源码

/\*\*

- 通用封装请求方法
- @param {请求路径} url
- @param {请求配置} config
- config 配置枚举
-     data	[Object/String/ArrayBuffer]	请求参数
-     method	[String=GET]	请求方法
-     header	[Object]	请求头
-     timeout	[Number=60000]	超时时间(ms)
-     dataType	[String=json]	请求数据类型
-     responseType	[String=text]	返回数据类型
-     withCredentials	[Boolean=false]	 跨域请求是否带cookies
-     success	[Function]	请求成功回调
-     fail	[Function]	请求失败回调
-     complete	[Function]	请求结束回调
  \*/
  import {
  ACCESS_TOKEN
  } from "@/common/util/constants"
  export default function(url, config, flag) {
  const {
  header = {}, ...rest
  } = config;
  let headerData = {
  'content-type': 'application/json',
  ...header,
  }
  if (uni.getStorageSync(ACCESS_TOKEN)) {
  headerData['X-Access-Token'] = uni.getStorageSync(ACCESS_TOKEN)
  }
  return new Promise((resolve, reject) => {
  uni.request({
  url,
  header: headerData,
  withCredentials: true,
  ...config,
  success: (getData) => {
  const {
  statusCode
  } = getData;
  const {
  success,
  result,
  message,
  data
  } = getData.data || {};
  if (statusCode === 500) {
  uni.showToast({
  title: '服务器连接失败，请稍后再试。。。',
  icon: 'error',
  success() {
  reject(message);
  }
  })
  } else if (statusCode === 200 && success) {
  if (data) {
  resolve(data)
  } else {
  resolve(result)
  }
  } else {
  if (flag == 1) {
  resolve(getData.data)
  } else {
  uni.showToast({
  title: message,
  icon: 'error',
  duration: 4000,
  success() {
  reject(message)
  }
  });
  }
  }
  },
  fail: (error) => {
  reject(error);
  }
  })
  })
  }

使用文档
import request from '@/utils/request';
import { USER_INFO } from "@/common/util/constants";
try {
const res = await request(this.$api + `/zlb/bankRegApply/add`, {
method: "POST",
data: {
id: uni.getStorageSync(USER_INFO),
},
});
console.log(res);
uni.showToast({
title: res,
icon: "success",
});
this.pageNo = 1;
this.applyList = [];
this.fetchApplyList();
} catch (error) {
uni.showToast({
title: error,
icon: "none",
duration: 3000,
});
}

注意 当 f12 返回

{
"success": true,
"message": "申请成功",
"code": 200,
"result": "申请成功",
"timestamp": *************
}

res 的结果为 result 的值

关于添加新页面创建路由 需要 pages.json 和 zlb-front-quz/common/router/modules/routes.js 中都修改

项目组件库文档
不清楚可以查看
E:\核心\交科科技\clineRooCode\组件库文档 ai 用\uView2.0
