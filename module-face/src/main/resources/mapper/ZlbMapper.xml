<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zz3s.face.mapper.ZlbStuKmpassMapper">

    <resultMap id="BaseResultMap" type="com.zz3s.face.entity.ZlbStuKmpass">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="stunum" column="inscode" jdbcType="VARCHAR"/>
            <result property="subject" column="subject" jdbcType="VARCHAR"/>
            <result property="crdate" column="crdate" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,stunum,subject,crdate
    </sql>
</mapper>
