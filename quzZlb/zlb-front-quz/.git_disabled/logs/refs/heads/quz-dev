0000000000000000000000000000000000000000 c0c88e47e6fec0b01877bed58668d79cb88f9180 52417 <<EMAIL>> 1735557769 +0800	branch: Created from origin/quz-dev
c0c88e47e6fec0b01877bed58668d79cb88f9180 3e5134cb0526e87b547d0e0d5740cf16e1860d07 52417 <<EMAIL>> 1735558125 +0800	commit: 添加转校功能
3e5134cb0526e87b547d0e0d5740cf16e1860d07 d186c4986fe9fd7fac33ef93495b15bf8e4629b1 52417 <<EMAIL>> 1736754259 +0800	commit: 添加支付宝跳转功能
d186c4986fe9fd7fac33ef93495b15bf8e4629b1 e8ada01c32a7c2a5ea18055afea6ac47298f5b36 52417 <<EMAIL>> 1736755970 +0800	commit: 去除pdf中的向上的箭头按钮
e8ada01c32a7c2a5ea18055afea6ac47298f5b36 32069455797cb42a87fef1aeb4a66da44fdb15f3 52417 <<EMAIL>> 1737011903 +0800	commit: 添加默认经纬度 驾校简介样式格式化
32069455797cb42a87fef1aeb4a66da44fdb15f3 1e7707c2a961418d2212adb965b94dc9f62ceec0 52417 <<EMAIL>> 1737530678 +0800	commit: 添加支付宝支付跳转 教练员端代码优化
1e7707c2a961418d2212adb965b94dc9f62ceec0 f896eec80385076b5d80ae269ee070d606f87023 52417 <<EMAIL>> 1737620588 +0800	commit: 添加教练端预约排班
f896eec80385076b5d80ae269ee070d606f87023 3c765cc58cdfd4693d8b977bcabf003abdb08d57 52417 <<EMAIL>> 1737707703 +0800	commit: 添加教练端预约排班
3c765cc58cdfd4693d8b977bcabf003abdb08d57 6ad756adc51127ce83313fc9c6ba9ba8cd60acdb 52417 <<EMAIL>> 1737708224 +0800	commit: 添加教练端预约排班0.5
6ad756adc51127ce83313fc9c6ba9ba8cd60acdb 7a054d364401d56f4d15f7c5d8440568080a8b80 52417 <<EMAIL>> 1737708467 +0800	commit: 添加教练端预约排班0.6
7a054d364401d56f4d15f7c5d8440568080a8b80 418170335f9affb0d4fe1449b68d396f1e7852e1 52417 <<EMAIL>> 1737708969 +0800	commit: 添加教练端预约排班0.7
418170335f9affb0d4fe1449b68d396f1e7852e1 5e121a49bc7fe3f6a2d5be054e681534b9274d94 52417 <<EMAIL>> 1737708987 +0800	rebase (finish): refs/heads/quz-dev onto 1e7707c2a961418d2212adb965b94dc9f62ceec0
5e121a49bc7fe3f6a2d5be054e681534b9274d94 aea5c3535917f5dd2fb4087d6c3f3b43523b2b83 52417 <<EMAIL>> 1737709023 +0800	commit (merge): Merge remote-tracking branch 'origin/quz-dev' into quz-dev
aea5c3535917f5dd2fb4087d6c3f3b43523b2b83 5ea2d35ea4c6fa23e098ee88599bfa6c3ebdbb0f 52417 <<EMAIL>> 1737855347 +0800	commit: 添加教练端预约排班0.8
5ea2d35ea4c6fa23e098ee88599bfa6c3ebdbb0f d5c696efc5cd4d79a8793d517d75f304cbea1e49 52417 <<EMAIL>> 1738740984 +0800	commit: 添加教练端预约排班0.9
d5c696efc5cd4d79a8793d517d75f304cbea1e49 6cb679a6d39f7bb910d000ec1693ad38a1e34c15 52417 <<EMAIL>> 1738829519 +0800	commit: 排版预期0.1版开发
6cb679a6d39f7bb910d000ec1693ad38a1e34c15 464eef0da67191f4adb96ce8bedc0614818c01c4 52417 <<EMAIL>> 1738829533 +0800	rebase (finish): refs/heads/quz-dev onto aea5c3535917f5dd2fb4087d6c3f3b43523b2b83
464eef0da67191f4adb96ce8bedc0614818c01c4 87ee3f35fd475a582a2b062dfd896281e231faaa 52417 <<EMAIL>> 1738894091 +0800	commit: 排版预期0.2版开发
87ee3f35fd475a582a2b062dfd896281e231faaa 3f9eb623e50e049782cbd30ebc4e8b280bf87b8e 52417 <<EMAIL>> 1738894808 +0800	commit: 排版预期0.3版开发
3f9eb623e50e049782cbd30ebc4e8b280bf87b8e 65d723c30b92741042f8a2b8b41a2d278cf472d4 52417 <<EMAIL>> 1738899907 +0800	commit: 排版预期0.4版开发
65d723c30b92741042f8a2b8b41a2d278cf472d4 613fd87952a9630b65db1b65f55cfce771f053fb 52417 <<EMAIL>> 1738907756 +0800	commit: 排版0.5
613fd87952a9630b65db1b65f55cfce771f053fb 9affd5961f7c0f05aacfaec2bb22ab4fda2cf03b 52417 <<EMAIL>> 1738908177 +0800	commit: 排版0.5
9affd5961f7c0f05aacfaec2bb22ab4fda2cf03b 93f2647e685ce4fc11f52af4f8b61b810b220cb5 52417 <<EMAIL>> 1738908901 +0800	commit: 排版0.5
93f2647e685ce4fc11f52af4f8b61b810b220cb5 73ba965d410bca89918a02d3d544efe3e4e20513 52417 <<EMAIL>> 1738917652 +0800	commit: 排版0.6
73ba965d410bca89918a02d3d544efe3e4e20513 b7ab0679590c1f4c587480c5a626c70d16f54761 52417 <<EMAIL>> 1738921452 +0800	commit: 排版0.6
b7ab0679590c1f4c587480c5a626c70d16f54761 aeae54212254c350cacf2c64d134aacdc9e9ecfe 52417 <<EMAIL>> 1738921962 +0800	commit: 排版0.6
aeae54212254c350cacf2c64d134aacdc9e9ecfe 10a6498af10b287071c786ac448a34e3503600fe 52417 <<EMAIL>> 1738921997 +0800	rebase (finish): refs/heads/quz-dev onto aea5c3535917f5dd2fb4087d6c3f3b43523b2b83
10a6498af10b287071c786ac448a34e3503600fe d84e44677bbe1bd5b3f58219a7f9281cac968f1e 52417 <<EMAIL>> 1739173977 +0800	commit: 预约培训】有多个教练员发布计划，这个时候浙里办上只有一条数据，没有办法区分。
d84e44677bbe1bd5b3f58219a7f9281cac968f1e 614bcb04117e966a85f602d42ce2bb2469066b34 52417 <<EMAIL>> 1739236311 +0800	commit: a
614bcb04117e966a85f602d42ce2bb2469066b34 22b2dd39119f3df1d6ebed5d5ab149a2468fdb7f 52417 <<EMAIL>> 1739237139 +0800	commit: a
22b2dd39119f3df1d6ebed5d5ab149a2468fdb7f ce962579c1f61abb2e1d3ec830391a3f43456a1e 52417 <<EMAIL>> 1739240860 +0800	commit: bug fix
ce962579c1f61abb2e1d3ec830391a3f43456a1e 0fa73226306c45b7b650918e6c6907c180b797ee 52417 <<EMAIL>> 1739329684 +0800	commit: bug fix 【个人中心-预约记录】这种超时没有取消的，状态能不能改下。已培训什么的，就不是取消预约。
0fa73226306c45b7b650918e6c6907c180b797ee 2b252007e6748b8155c1b253439799537f2d5280 52417 <<EMAIL>> 1739349905 +0800	commit: bug fix 【个人中心-预约记录】增加下课程类型，无法区分预约的是什么类型的课程（实车、模式、课堂）
2b252007e6748b8155c1b253439799537f2d5280 edd21f0bb4e880db44cff8694f2befd5cde6da72 52417 <<EMAIL>> 1739408090 +0800	commit: bug fix 已预约、可预约、不可预约建议做下颜色区分
edd21f0bb4e880db44cff8694f2befd5cde6da72 e4d2e168e3157c46837812201bd36fb795209e5e 52417 <<EMAIL>> 1739408139 +0800	rebase (finish): refs/heads/quz-dev onto d84e44677bbe1bd5b3f58219a7f9281cac968f1e
e4d2e168e3157c46837812201bd36fb795209e5e ee3c591214f65ed6f63c450545ecea93be70b0ee 52417 <<EMAIL>> 1739408147 +0800	rebase (finish): refs/heads/quz-dev onto 614bcb04117e966a85f602d42ce2bb2469066b34
ee3c591214f65ed6f63c450545ecea93be70b0ee 64eed8f553e64f6a35e381d8af924f2344f0e8e9 52417 <<EMAIL>> 1739408187 +0800	merge origin/quz-dev: Merge made by the 'ort' strategy.
64eed8f553e64f6a35e381d8af924f2344f0e8e9 a9159130645eb7c1008c7a9e2b484eee48f6e756 52417 <<EMAIL>> 1739428783 +0800	commit: bug fix 【预约排班】教练员可为超时排班添加学员（过了排班时间，不能预约学员）
a9159130645eb7c1008c7a9e2b484eee48f6e756 64d1f0b6f789ff3d17c7eba1ba4c72352d47bd9b 52417 <<EMAIL>> 1739779770 +0800	commit: yuyue
64d1f0b6f789ff3d17c7eba1ba4c72352d47bd9b 664280c339fc4ee8e7472f36174ec70d361aa68b 52417 <<EMAIL>> 1739780345 +0800	commit: yuyue
664280c339fc4ee8e7472f36174ec70d361aa68b 9eb63ac23137d4c29127a42df24134445f072d09 52417 <<EMAIL>> 1739780365 +0800	rebase (finish): refs/heads/quz-dev onto a9159130645eb7c1008c7a9e2b484eee48f6e756
9eb63ac23137d4c29127a42df24134445f072d09 3bac27e5fe4d1da95bb8c8fbbbb1b339da0e83da 52417 <<EMAIL>> 1740471181 +0800	commit: 模拟课堂添加教室 学时确认添加修改
3bac27e5fe4d1da95bb8c8fbbbb1b339da0e83da 6695b2c6dd0ddbf7218f8840e189e17e94fadf94 52417 <<EMAIL>> 1740472340 +0800	commit: 结业考核预约删除错误调用
6695b2c6dd0ddbf7218f8840e189e17e94fadf94 b6e1ef3bd1499622039504cd6d3c41b6fcb2c0ee 52417 <<EMAIL>> 1740556399 +0800	commit: 学时确认人脸识别
b6e1ef3bd1499622039504cd6d3c41b6fcb2c0ee 17c9c6528001829ed7640f56f599f7e38e8a9079 52417 <<EMAIL>> 1740967077 +0800	commit: 答题bug修复
17c9c6528001829ed7640f56f599f7e38e8a9079 b5cd7b614c7853c84659613eb2004d68bd00278a 52417 <<EMAIL>> 1740968211 +0800	commit: 【在线理论学习】答题时间结束后，还能继续答题
b5cd7b614c7853c84659613eb2004d68bd00278a 0c889a998a0ac090f73643edcfe86c0baee4d4a0 52417 <<EMAIL>> 1741854675 +0800	commit: 修改 encrypt 引入问题
0c889a998a0ac090f73643edcfe86c0baee4d4a0 12006f497c66e923862c1822144d92f731e5d2c0 52417 <<EMAIL>> 1741917359 +0800	commit: 添加单位元
12006f497c66e923862c1822144d92f731e5d2c0 602bf6e47360b56da8080bae708aa76b0531cf32 52417 <<EMAIL>> 1743045925 +0800	commit: 添加uniapp新版本兼容
602bf6e47360b56da8080bae708aa76b0531cf32 f27622350b5cc23e5ecc96b87b190b310fa80d0c 52417 <<EMAIL>> 1743057893 +0800	commit: 添加uniapp新版本兼容
f27622350b5cc23e5ecc96b87b190b310fa80d0c f08f3af1fbe62a34fc716b767b8bc83cf5173de2 52417 <<EMAIL>> 1743060186 +0800	commit: 添加uniapp新版本兼容
f08f3af1fbe62a34fc716b767b8bc83cf5173de2 4ad433f1bf7f626e2503ec90426017f2ec2f0fe6 52417 <<EMAIL>> 1743060686 +0800	commit: 添加uniapp新版本兼容
4ad433f1bf7f626e2503ec90426017f2ec2f0fe6 56378d0f7422dc1c00d11b3e08cf02d9c81ea155 52417 <<EMAIL>> 1743062531 +0800	commit: 添加uniapp新版本兼容
56378d0f7422dc1c00d11b3e08cf02d9c81ea155 27b1bf09ce73b7af134cf7964d901be6ca65d74c 52417 <<EMAIL>> 1743064870 +0800	commit: 添加uniapp新版本兼容
27b1bf09ce73b7af134cf7964d901be6ca65d74c b100c9c420a2480a37177424ab1332b3ad49a0ab 52417 <<EMAIL>> 1743064973 +0800	rebase (finish): refs/heads/quz-dev onto 602bf6e47360b56da8080bae708aa76b0531cf32
b100c9c420a2480a37177424ab1332b3ad49a0ab 33b4a323d5eb92ae425f84a1d14bf253754bf82f 52417 <<EMAIL>> 1743074483 +0800	commit: 添加图标
33b4a323d5eb92ae425f84a1d14bf253754bf82f cb42fde4e9435c8c65b2f70338a17ccc31b779fb 52417 <<EMAIL>> 1743074498 +0800	commit: 添加图标
cb42fde4e9435c8c65b2f70338a17ccc31b779fb 4f478ab34d3da3dda0d226d27d9f8feefd571e62 52417 <<EMAIL>> 1743124567 +0800	commit: 添加uniapp新版本兼容
4f478ab34d3da3dda0d226d27d9f8feefd571e62 682994b2dab388ddc563234ca471382740034a9d 52417 <<EMAIL>> 1743125706 +0800	commit: 添加uniapp新版本兼容
682994b2dab388ddc563234ca471382740034a9d 20d1a6a9c90d96cc19eac8ee95d2692af1989f3d 52417 <<EMAIL>> 1743126166 +0800	commit: 添加uniapp新版本兼容
20d1a6a9c90d96cc19eac8ee95d2692af1989f3d a2b910c40ba53643fb2b9ac9e6659d18a66c94ae 52417 <<EMAIL>> 1743127903 +0800	commit: 添加uniapp新版本兼容
a2b910c40ba53643fb2b9ac9e6659d18a66c94ae 62856741cc549d5cc0111759d05afebe326ddbb0 52417 <<EMAIL>> 1743127930 +0800	rebase (finish): refs/heads/quz-dev onto b100c9c420a2480a37177424ab1332b3ad49a0ab
62856741cc549d5cc0111759d05afebe326ddbb0 4b47b6d61ac14a4032d8456018012ff117f2d937 52417 <<EMAIL>> 1743132592 +0800	commit: 添加uniapp新版本兼容
4b47b6d61ac14a4032d8456018012ff117f2d937 7c3422aef73ddf3f397398b9838ecabc422a6759 52417 <<EMAIL>> 1743142745 +0800	commit: 1
7c3422aef73ddf3f397398b9838ecabc422a6759 ebe47e703670777f073021efc84adfffff8901ce 52417 <<EMAIL>> 1743142985 +0800	rebase (finish): refs/heads/quz-dev onto b100c9c420a2480a37177424ab1332b3ad49a0ab
ebe47e703670777f073021efc84adfffff8901ce e5e26590b44ff262a20bd40f76b4d9fc95eb1623 52417 <<EMAIL>> 1743149046 +0800	commit: 添加uniapp新版本兼容
e5e26590b44ff262a20bd40f76b4d9fc95eb1623 9bb4f157db80653a8a7a0d195b12f78397112abc 52417 <<EMAIL>> 1743150099 +0800	commit: 添加uniapp新版本兼容
9bb4f157db80653a8a7a0d195b12f78397112abc dae712d9aeb243848c3dd1047a593918fac6e036 52417 <<EMAIL>> 1743574871 +0800	commit: 教练
dae712d9aeb243848c3dd1047a593918fac6e036 0bf0888a3c1e3a591caaf6045d94a781ebb92cb5 52417 <<EMAIL>> 1743575500 +0800	commit: 教练
0bf0888a3c1e3a591caaf6045d94a781ebb92cb5 cc1b395ef45861092e675d1da7806968f177a487 52417 <<EMAIL>> 1743580039 +0800	commit: 优化教练选择功能，重构相关方法，修复教练筛选逻辑
cc1b395ef45861092e675d1da7806968f177a487 2daff938f5150294209a46a73ad35757b3428712 52417 <<EMAIL>> 1743580056 +0800	rebase (finish): refs/heads/quz-dev onto 9bb4f157db80653a8a7a0d195b12f78397112abc
2daff938f5150294209a46a73ad35757b3428712 fd6bcb556a5fec330f52dd014680491601ac63d9 52417 <<EMAIL>> 1743580061 +0800	revert: Revert "优化教练选择功能，重构相关方法，修复教练筛选逻辑"
fd6bcb556a5fec330f52dd014680491601ac63d9 4aeed4a1ce9a7af5cdfdc96bf0834bbc5d802542 52417 <<EMAIL>> 1743580064 +0800	revert: Revert "添加uniapp新版本兼容"
4aeed4a1ce9a7af5cdfdc96bf0834bbc5d802542 9aa6b6cdf8493a8375bec6c3d2bed0933269654b 52417 <<EMAIL>> 1743580067 +0800	revert: Revert "添加uniapp新版本兼容"
9aa6b6cdf8493a8375bec6c3d2bed0933269654b 2daff938f5150294209a46a73ad35757b3428712 52417 <<EMAIL>> 1743580164 +0800	rebase (finish): refs/heads/quz-dev onto 2daff938f5150294209a46a73ad35757b3428712
2daff938f5150294209a46a73ad35757b3428712 fd2f9431e03aa6c927b82a45379fc9dbda4bf0b6 52417 <<EMAIL>> 1744075271 +0800	commit: 修改标题文本，从“在线理论学习”更改为“在线理论练习”
fd2f9431e03aa6c927b82a45379fc9dbda4bf0b6 e0bcd7779289620b5bbf74efe4d2e40b66025c65 52417 <<EMAIL>> 1744077450 +0800	commit: 修改地区名称，从“高新区”更改为“柯城区”
e0bcd7779289620b5bbf74efe4d2e40b66025c65 7b85862a3ec23a28f1f5a4a4f3a4b16af7ce4be6 52417 <<EMAIL>> 1744186818 +0800	commit: 优化学员筛选逻辑，支持C1排班学员的选择，移除转校功能的注释
7b85862a3ec23a28f1f5a4a4f3a4b16af7ce4be6 38ae745b477d7bf37a17d7b7d3d91e97aaa3852f 52417 <<EMAIL>> 1744253298 +0800	commit: 添加教室筛选功能，支持根据教室名称过滤预约课程列表，优化教室列表获取逻辑
38ae745b477d7bf37a17d7b7d3d91e97aaa3852f df2ca0ae6e4d0b339e339766349439721279e0ff 52417 <<EMAIL>> 1744268456 +0800	commit: 优化教练筛选和教室筛选逻辑，调整代码格式
df2ca0ae6e4d0b339e339766349439721279e0ff 3a9cc10f0e4a69cfa4be0e7106253b4ff27e65c4 52417 <<EMAIL>> 1744343274 +0800	commit: 修改图标顺序 代码重构
3a9cc10f0e4a69cfa4be0e7106253b4ff27e65c4 7fe4c3ed9aeca1219171383c85468b62bb7daa67 52417 <<EMAIL>> 1744858986 +0800	commit: 修改为 在线理论练习
7fe4c3ed9aeca1219171383c85468b62bb7daa67 f7d97c4701fae81832cb5826959d8c410037fe8f 52417 <<EMAIL>> 1746775822 +0800	commit: 修复教练排班 错误驾校信息bug
f7d97c4701fae81832cb5826959d8c410037fe8f 5922919e400491c830abded1ad70ef3fa0b2cd09 52417 <<EMAIL>> 1746776169 +0800	commit: 手机浏览器展示此页面 用户签名的时候 手指拖动 可以下拉页面导致 签名困难的问题 安卓没问题 苹果浏览器有这个问题
5922919e400491c830abded1ad70ef3fa0b2cd09 c82493bd60ad3c855831e3a02a47ec35c5fe33f9 52417 <<EMAIL>> 1747036640 +0800	commit: 衢江、柯城区在衢学车的下拉列表两个主城区统一放市辖区
c82493bd60ad3c855831e3a02a47ec35c5fe33f9 62a4e04264beb28cf1a03bb44a39c0a5ceff7a0d 52417 <<EMAIL>> 1747124536 +0800	commit: 衢江、柯城区在衢学车的下拉列表两个主城区统一放市辖区
62a4e04264beb28cf1a03bb44a39c0a5ceff7a0d c0378da4a9c3d41976dd76ca51bd53b012d273f9 52417 <<EMAIL>> 1747210837 +0800	commit: 公式价格修改
c0378da4a9c3d41976dd76ca51bd53b012d273f9 1460b17bc008be697187130c07c56ab2f3dc7365 52417 <<EMAIL>> 1748244728 +0800	commit: 课程时长默认1
1460b17bc008be697187130c07c56ab2f3dc7365 aeff1168852eb66b68fe64ac71f32e22ab248278 52417 <<EMAIL>> 1748324720 +0800	commit: 驾校名称显示逻辑修改
aeff1168852eb66b68fe64ac71f32e22ab248278 fea4298a73da1ad5e8721f9f170368620aa12ec7 52417 <<EMAIL>> 1748398545 +0800	commit: 多笔冻结代码修改
fea4298a73da1ad5e8721f9f170368620aa12ec7 84bf21aefbca10c041792edf925137571ca2154f 52417 <<EMAIL>> 1748398664 +0800	commit: 多笔冻结代码修改
84bf21aefbca10c041792edf925137571ca2154f 20ab579ed21836a8c6c19c4bc2a5b229a98b7788 52417 <<EMAIL>> 1748418524 +0800	commit: 1
20ab579ed21836a8c6c19c4bc2a5b229a98b7788 08cc5ddfd54bd86d6102770b238f36c2467b8e42 52417 <<EMAIL>> 1748419586 +0800	commit: 1
08cc5ddfd54bd86d6102770b238f36c2467b8e42 603fbc42d547d8fdfc85df2ae49a7a14741a3df9 52417 <<EMAIL>> 1748479804 +0800	commit: 推送银行二次确认
603fbc42d547d8fdfc85df2ae49a7a14741a3df9 9366473b1d125fac4291eb0a5b08ecea32432dd4 52417 <<EMAIL>> 1748486791 +0800	commit: 推送银行二次确认
9366473b1d125fac4291eb0a5b08ecea32432dd4 59b0da5c08c3286bd0db80fba633aed8b86753b4 52417 <<EMAIL>> 1748487663 +0800	commit: 推送银行二次确认
59b0da5c08c3286bd0db80fba633aed8b86753b4 b53054ebb38352fb04b63a6547337884abd00b9e 52417 <<EMAIL>> 1748487724 +0800	rebase (finish): refs/heads/quz-dev onto aeff1168852eb66b68fe64ac71f32e22ab248278
b53054ebb38352fb04b63a6547337884abd00b9e 71fa993e69a7a22016cfdeb17ccfcf54a4c91f72 52417 <<EMAIL>> 1748490106 +0800	commit: 推送银行二次确认
71fa993e69a7a22016cfdeb17ccfcf54a4c91f72 80b8fe92f7c261f8e892472bf1de6a995d6125c1 52417 <<EMAIL>> 1748493388 +0800	commit: 推送银行二次确认
