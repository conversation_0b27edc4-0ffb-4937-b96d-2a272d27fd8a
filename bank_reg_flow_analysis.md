# 驾培资金多笔冻结业务流程分析

## 1. 引言

本文档旨在分析衢州驾培项目中，学员资金多笔冻结业务的核心流程、关键表结构、主要服务以及它们之间的交互关系。重点关注 `/zlb/bankRegApply/add` 接口及其后续处理逻辑。

## 2. 核心接口: `/zlb/bankRegApply/add`

此接口是学员或系统自动发起后续阶段资金冻结申请的入口。

*   **路径**: `/zlb/bankRegApply/add` (POST请求)
*   **控制器**: [`ZlbBankRegApplyController.java`](quzPublic/public/jky-jeecg-boot-cdtpublic-parent/jky-jeecg-boot-cdtpublic-module-core/src/main/java/com/jky/boot/core/module/reservation/controller/ZlbBankRegApplyController.java)
*   **接收参数**: `BankRegApply rec` (请求体中包含学员ID等信息)
*   **主要职责**:
    1.  校验学员信息。
    2.  调用 `bankRegApplyService.saveDefaultApply(studentinfo)` 方法，其中 `studentinfo` 是通过学员ID查询到的学员对象。

## 3. 关键方法分析

### 3.1. `BankRegApplyServiceImpl.saveDefaultApply(Studentinfo studentinfo)`

这是生成待确认的多笔冻结申请的核心。

*   **前置校验**:
    *   学员编号是否存在。
    *   学员当前冻结阶段 (`studentinfo.getRegState()`) 是否允许再次冻结（例如，不能是最终冻结阶段或单笔全额冻结）。
    *   系统中是否存在该学员尚未处理完成的 `BankRegApply` 申请。
    *   获取学员当前的银行冻结主记录 `BankReg bankReg = bankRegService.getOneByStuNum(stunum)`。此方法获取的是该学员**创建时间最早**的一条 `BankReg` 记录。
    *   获取学员合同 `StudentContract contract`。
    *   `bankReg` 记录必须存在且其状态 (`status`) 必须为 `3` (表示银行冻结成功)。
*   **根据学员当前冻结阶段和银行冻结余额创建申请**:
    *   **若 `studentinfo.getRegState() == "21"` (二笔冻结第一笔完成)**:
        *   **条件**: `bankReg.getRemainingAmount() == 0` (第一笔冻结资金已用完)。
        *   **操作**: 调用 `createApply("22", bankReg, contract)` 创建目标为 "22" (二笔冻结第二笔：科二+科三) 的 `BankRegApply` 申请。
    *   **若 `studentinfo.getRegState() == "31"` (三笔冻结第一笔完成)**:
        *   **条件**: `bankReg.getRemainingAmount() == 0`。
        *   **操作**: 调用 `createApply("32", bankReg, contract)` 创建目标为 "32" (三笔冻结第二笔：科二) 的 `BankRegApply` 申请。
    *   **若 `studentinfo.getRegState() == "32"` (三笔冻结第二笔完成)**:
        *   若 `bankReg.getRemainingAmount() > 0` 但不足以支付最便宜的后续课程：创建退费类型的 `BankRegApply` (调用 `createRefundApply`)。
        *   若 `bankReg.getRemainingAmount() == 0`: 调用 `createApply("33", bankReg, contract)` 创建目标为 "33" (三笔冻结第三笔：科三及剩余科二) 的 `BankRegApply` 申请。
*   **结果**: 将新创建的 `BankRegApply` 对象（状态为 `0` - 初始）保存到数据库。

### 3.2. `BankRegApplyServiceImpl.createApply(String targetRegState, BankReg regFromStunum, StudentContract contract)`

此方法用于初始化一个新的 `BankRegApply` 冻结申请单对象。

*   设置申请单的学员基本信息、驾校信息、银行信息。
*   设置申请状态为 `0` (初始，待学员确认)。
*   设置申请类型为 `"1"` (冻结)。
*   设置本次申请的目标冻结阶段 `apply.setRegState(targetRegState)`。
*   **核心**: 调用 `setBalanceAmount(targetRegState, contract, apply)` 方法来计算并设置本次申请单需要冻结的具体金额。

### 3.3. `BankRegApplyServiceImpl.setBalanceAmount(String targetRegState, StudentContract contract, BankRegApply apply)`

此方法是计算 `BankRegApply` 申请单中具体冻结金额的核心。

*   **输入**:
    *   `targetRegState`: 本次申请期望达到的下一个资金冻结阶段。
    *   `StudentContract contract`: 学员的合同，包含各科目费用标准。
    *   `BankRegApply apply`: 需要填充金额的申请单对象。
*   **输出**: 修改 `apply` 对象的 `balanceAmount` (总申请冻结金额), `sub2Cost` (科二金额), `sub3Cost` (科三金额)。
*   **计算逻辑**:
    *   **目标 "22" (二笔第二笔: 科二+科三)**:
        *   `apply.sub2Cost = contract.getTotalSub2Cost()`
        *   `apply.sub3Cost = contract.getTotalSub3Cost()`
        *   `apply.balanceAmount = contract.getTotalSub2Cost() + contract.getTotalSub3Cost()`
    *   **目标 "32" (三笔第二笔: 科二)**:
        *   `apply.sub2Cost = contract.getTotalSub2Cost()`
        *   `apply.sub3Cost = BigDecimal.ZERO`
        *   `apply.balanceAmount = contract.getTotalSub2Cost()`
    *   **目标 "33" (三笔第三笔: 科三及剩余科二)**:
        *   通过 `schedulingApplyService.getStuApplyTimes(contract.getStunum())` 获取学员已完成的科二、科三学时。
        *   计算科二剩余应冻结: `sub2De = contract.getTotalSub2Cost() - (contract.getSub2CostPer() * 已上科二学时)`。
        *   计算科三剩余应冻结: `sub3De = contract.getTotalSub3Cost() - (contract.getSub3CostPer() * 已上科三学时)`。
        *   `apply.sub2Cost = sub2De`
        *   `apply.sub3Cost = sub3De`
        *   `apply.balanceAmount = sub2De + sub3De`

## 4. 核心表结构与作用

### 4.1. `t_m_student_contract` ([`StudentContract.java`](quzPublic/public/jky-jeecg-boot-cdtpublic-parent/jky-jeecg-boot-cdtpublic-module-core/src/main/java/com/jky/boot/core/module/gzpt/entity/StudentContract.java))

*   **作用**: 学员合同表，存储学员与驾校签订的合同信息。核心字段包括各培训科目的总费用 (`total...Cost`)、单价 (`...CostPer`) 以及当前阶段各科目的可用余额 (`theoryCost`, `imiCost`, `sub2Cost`, `sub3Cost`)。
*   **关键**: 费用标准是计算各阶段应冻结金额的基准。科目余额会根据学员的资金冻结阶段 (`regState`) 动态调整，反映当前阶段的费用构成。
*   **关联**: 通过 `stunum` (学员编号) 关联学员信息。

### 4.2. `t_m_bank_reg` ([`BankReg.java`](quzPublic/public/jky-jeecg-boot-cdtpublic-parent/jky-jeecg-boot-cdtpublic-module-core/src/main/java/com/jky/boot/core/module/gzpt/entity/BankReg.java))

*   **作用**: 学员银行资金冻结主记录表，代表学员在银行的实际资金池状态。每条记录通常对应一个特定的冻结阶段或一次主要的冻结操作。
*   **核心字段**: `stunum`, `inscode`, `regState` (当前资金池对应的冻结阶段), `balanceAmount` (此资金池的总冻结金额), `remainingAmount` (当前剩余可用金额), `status` (银行处理状态：0-初始, 1-开户成功/待银行确认冻结, 3-冻结成功, 4-冻结中, 5-冻结失败等), `serialno` (银行侧流水号), `blockid` (银行冻结编号)。
*   **生命周期**:
    *   首次推送银行时创建第一条记录。
    *   当进入新的冻结阶段时，若旧阶段的 `BankReg` 记录余额为0，`BankRegApplyTaskServiceImpl` 会将其备份到 `t_m_bank_reg_detail_del` 并从原表删除，然后为新阶段创建一条全新的 `BankReg` 记录。
    *   状态和余额主要由银行回调 (`CallbackHandler`) 更新。

### 4.3. `t_m_bank_reg_apply` ([`BankRegApply.java`](quzPublic/public/jky-jeecg-boot-cdtpublic-parent/jky-jeecg-boot-cdtpublic-module-core/src/main/java/com/jky/boot/core/module/gzpt/entity/BankRegApply.java))

*   **作用**: 学员多笔冻结/解冻申请表，是驱动资金操作和阶段转换的核心事务表。
*   **核心字段**: `stunum`, `applyType` (1-冻结, 2-解冻), `regState` (本次申请的目标冻结阶段), `balanceAmount` (本次申请操作的总金额), `sub2Cost` (科二金额), `sub3Cost` (科三金额), `status` (申请自身处理状态：0-初始, 1-驾校审核通过, 2-银行处理中, 3-处理成功, 4-处理失败, 10-学员确认/待驾校审核, 12-驾校审核不通过), `orderId` (关联的 `t_m_bank_order.id`)。
*   **流程**: 记录学员发起的每一次资金操作意愿，后续经学员确认、驾校审核、系统任务处理（创建 `BankOrder` 推送银行）、银行回调等步骤完成整个流程。

## 5. 关键服务与定时任务

### 5.1. `BankRegApplyTaskServiceImpl.java`

*   **职责**: 异步处理 `BankRegApply` 申请。通过定时任务扫描处于特定状态的申请记录。
*   **`startApplyProcess1(BankRegApply apply)` (处理状态为1-驾校审核通过的申请)**:
    1.  创建 `BankOrder` 对象，包含本次操作的金额、类型等。
    2.  调用 `PushBankUtil.pushFundTransfer()` 将 `BankOrder` 信息推送给银行。
    3.  若推送成功:
        *   更新 `BankRegApply.status` 为 `2` (处理中)，记录 `BankOrder.id`。
        *   **关键的 `BankReg` 操作 (仅冻结申请)**:
            *   查询学员名下所有 `remainingAmount == 0` 的 `BankReg` 记录。
            *   如果存在此类记录，则将其备份到 `t_m_bank_reg_detail_del` 表，并从 `t_m_bank_reg` 表中删除。
            *   **创建一条新的 `BankReg` 记录**: `balanceAmount` 为 `apply.getBalanceAmount()`, `remainingAmount` 初始为 `BigDecimal.ZERO`, `regState` 为 `apply.getRegState()`, `status` 为 `1` (开户成功/等待银行回调确认冻结)。
    4.  若推送失败，则删除已创建的 `BankOrder`。
*   **`startApplyProcess2(BankRegApply apply)` (处理状态为2-处理中或4-处理失败的申请，依赖银行回调结果)**:
    1.  根据 `apply.getOrderId()` 获取关联的 `BankOrder`。
    2.  若 `BankOrder.status == 1L` (银行处理成功):
        *   更新 `BankRegApply.status` 为 `3` (处理成功)。
        *   **如果是冻结申请**: 根据 `apply.getRegState()` 和 `apply.getBalanceAmount()` 更新 `Studentinfo.regState` 和 `Studentinfo.frozenAmount`。调用 `studentContractService.setContract()` 更新合同科目余额。
    3.  若 `BankOrder.status == 2L` (银行处理失败): 更新 `BankRegApply.status` 为 `4` (处理失败)。
    4.  若 `BankOrder` 不存在: 更新 `BankRegApply.status` 为 `4` (处理失败，备注"订单不存在")。

### 5.2. `CallbackHandler.java`

*   **职责**: 处理银行（通过支付宝渠道）的回调通知，是数据同步和状态推进的关键。
*   **`fundTransferCallback(FundTransferVo msg)` (资金划转结果)**:
    *   `TRANSFER_IN`: 增加对应 `BankReg.remainingAmount`。
    *   `TRANSFER_OUT`: 成功则扣减 `BankReg.remainingAmount`，更新 `BankOrder.status` 为成功；失败则只更新 `BankOrder.status` 为失败。
    *   `ORDER_RELEASE` (全额解冻): 成功则将 `BankReg.remainingAmount` 置零，更新 `BankOrder.status` 为成功；失败则只更新 `BankOrder.status` 为失败。
*   **`orderStatusCallback(OrderStatusVo msg)` (订单状态变更)**:
    *   `RELEASE` / `CLOSED`: 更新对应 `BankReg.status` 为 `0` (初始/已解冻)。
    *   `TRANSFERRING` (转入中): 更新对应 `BankReg.status` 为 `4` (冻结中)。
    *   `FROZEN` (已冻结): 更新对应 `BankReg.status` 为 `3` (冻结成功)，记录银行操作时间。

## 6. 整体资金与状态流转概要

1.  **学员报名与首次冻结**:
    *   `StudentinfoController.add()`: 根据配置设置学员初始 `regState` (如 "31") 和第一笔应冻结金额。
    *   `StudentinfoController.PushStuToBank()`: 学员签合同后，驾校操作推送银行。
        *   创建第一条 `BankReg` 记录 (`status=0` 或 `1`, `balanceAmount`=首笔金额, `regState`=初始阶段)。
        *   调用 `StudentContractServiceImpl.setContract()` 初始化合同科目余额。
    *   银行回调 (`CallbackHandler`):
        *   `orderStatusCallback` (收到 `FROZEN`): 更新 `BankReg.status = 3`。
        *   `fundTransferCallback` (收到资金操作成功): 更新 `BankReg.remainingAmount` 为实际冻结金额。

2.  **后续多笔冻结**:
    *   学员上课消费，通过银行划拨回调 (`CallbackHandler.fundTransferCallback("TRANSFER_OUT")`) 扣减 `BankReg.remainingAmount`。
    *   当某阶段 `BankReg.remainingAmount == 0` 时，学员通过 `/zlb/bankRegApply/add` (内部调用 `BankRegApplyServiceImpl.saveDefaultApply()`) 发起下一阶段冻结申请，生成 `BankRegApply` 记录 (`status=0`)。
    *   学员在浙里办确认 (`ZlbBankRegApplyController.zlbConfirm()`), `BankRegApply.status` 变为 `10`。
    *   驾校PC端审核 (`BankRegApplyController.audit()`), 若通过，`BankRegApply.status` 变为 `1`。
    *   `BankRegApplyTaskServiceImpl.startApplyProcess1()` (定时任务处理 `status=1` 的申请):
        *   创建 `BankOrder` 并推送银行。
        *   成功后，`BankRegApply.status` 变为 `2`。
        *   **关键**: 查找并删除学员名下余额为0的旧 `BankReg` 记录 (备份到 `t_m_bank_reg_detail_del`)。然后**创建一条新的 `BankReg` 记录**，代表新阶段的资金池 (`balanceAmount`=本次申请金额, `remainingAmount`=0, `status=1`, `regState`=目标新阶段)。
    *   银行回调 (`CallbackHandler`):
        *   `orderStatusCallback` (收到 `FROZEN`): 更新新创建的 `BankReg.status = 3`。
        *   `fundTransferCallback` (收到资金操作成功): 更新新创建的 `BankReg.remainingAmount` (应等于其 `balanceAmount`)。
        *   同时更新关联的 `BankOrder.status = 1`。
    *   `BankRegApplyTaskServiceImpl.startApplyProcess2()` (定时任务处理 `status=2` 且对应 `BankOrder.status=1` 的申请):
        *   更新 `BankRegApply.status = 3` (处理成功)。
        *   更新 `Studentinfo.regState` 为新的冻结阶段，`Studentinfo.frozenAmount` 为本次冻结金额。
        *   调用 `StudentContractServiceImpl.setContract()`，根据新的 `regState` 调整 `StudentContract` 的科目余额。
    *   重复此流程，直至所有冻结阶段完成。

## 7. 重要逻辑点总结

*   **`BankReg` 记录管理**: 系统倾向于为每个新的有效冻结阶段创建新的 `BankReg` 记录，并将已用完的旧记录归档。因此，一个学员在不同时间点可能有多条 `BankReg` 历史记录，但通常只有一条是当前业务逻辑关注的“活跃”记录。
*   **`BankRegServiceImpl.getOneByStuNum()`**: 此方法获取的是学员创建时间最早的 `BankReg` 记录。在多笔冻结流程中，其他服务在获取当前银行状态时，可能需要更精确的查询（如结合 `status` 或取最新记录）来配合业务。
*   **金额计算**: 应冻结金额在创建 `BankRegApply` 时由 `setBalanceAmount` 根据合同和目标阶段预先计算。
*   **状态同步**: 银行回调是更新实际资金状态 (`BankReg`) 和银行订单状态 (`BankOrder`) 的主要途径。系统任务 (`BankRegApplyTaskServiceImpl`) 则根据这些状态来推进申请 (`BankRegApply`) 的内部业务流程，并最终更新学员信息 (`Studentinfo`) 和合同 (`StudentContract`)。
*   **数据一致性**: 依赖分布式锁 (`@RedissonLock`)保证并发操作的正确性，并通过多表状态同步确保流程的完整性。