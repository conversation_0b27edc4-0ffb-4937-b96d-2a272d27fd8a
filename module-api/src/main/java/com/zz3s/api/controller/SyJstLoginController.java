//package com.zz3s.api.controller;
//
//import com.baomidou.mybatisplus.core.toolkit.Wrappers;
//import com.zz3s.api.util.SpringContextUtils;
//import com.zz3s.api.util.SyJstAESUtils;
//import com.zz3s.common.constant.ZlbConstant;
//import com.zz3s.common.entities.CommonResponse;
//import com.zz3s.common.entities.ErrorCode;
//import com.zz3s.common.utils.RedisUtil;
//import com.zz3s.face.entity.BaseZlbPersonInfo;
//import com.zz3s.face.entity.Coach;
//import com.zz3s.face.entity.Institution;
//import com.zz3s.face.entity.Studentinfo;
//import com.zz3s.face.service.*;
//import lombok.extern.slf4j.Slf4j;
//import net.sf.json.JSONObject;
//import org.apache.catalina.connector.RequestFacade;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Controller;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestMethod;
//import org.springframework.web.bind.annotation.ResponseBody;
//
//import javax.servlet.http.HttpServletRequest;
//import java.lang.reflect.InvocationTargetException;
//import java.lang.reflect.Method;
//import java.util.Date;
//import java.util.Map;
//import java.util.Objects;
//import java.util.UUID;
//
///**
// * TODO 墨涵 驾所通 对接
// *
// * <AUTHOR>
// * @version 1.0
// * @date 2024/6/13 10:04
// */
//@Controller
//@RequestMapping("/jst")
//@Slf4j
//public class SyJstLoginController extends CommonController {
//    @Autowired
//    private RedisUtil redisUtil;
//
//    @Autowired
//    public OutrangeLogService outrangeLogService;
//    @Autowired
//    private StudentService studentService;
//    @Autowired
//    private ICoachService coachService;
//    @Autowired
//    private InstitutionService institutionService;
//    @Autowired
//    private IBaseZlbPersonInfoService zlbPersonInfoService;
////    @Value("${jky.crypto-mybatis.default-key}")
////    String key;
//
//
//    /**
//     * 驾所通统一接口
//     *
//     * @param request
//     * @param data
//     * @return
//     */
//    @RequestMapping(value = "/pushData", method = RequestMethod.POST)
//    @ResponseBody
//    public Map pushData(HttpServletRequest request, @RequestBody String data) {
//
//        String decrypt = null;
//        try {
//            decrypt = SyJstAESUtils.decrypt(data);
//        } catch (Exception e) {
//            e.printStackTrace();
//            return callBack(ErrorCode.ACCESS_FAILURE, "解密失败", null);
//        }
//        if (StringUtils.isBlank(decrypt)) {
//            return this.callBack(ErrorCode.ACCESS_FAILURE, "解密失败!", null);
//        }
//        JSONObject jo = JSONObject.fromObject(decrypt);
//        if (Objects.isNull(jo)) {
//            return this.callBack(ErrorCode.ACCESS_FAILURE, "解密失败!!", null);
//        }
//        if (!jo.has("methodName")) {
//            //0:学员 1:教练 2:考核员
//            return this.callBack(ErrorCode.ACCESS_FAILURE, "缺少参数：方法", null);
//        }
//        if (!jo.has("bizParam")) {
//            return this.callBack(ErrorCode.ACCESS_FAILURE, "缺少参数：请求体", null);
//        }
//        if (!jo.has("ts")) {
//            return this.callBack(ErrorCode.ACCESS_FAILURE, "缺少参数：时间", null);
//        }
//        String methodName = jo.getString("methodName");
//        String bizParamString = jo.getString("bizParam");
//        JSONObject bizParam = null;
//        try {
//            bizParam = JSONObject.fromObject(bizParamString);
//        } catch (Exception e) {
//            e.printStackTrace();
//            return this.callBack(ErrorCode.ACCESS_FAILURE, "请求体参数异常", null);
//        }
//        long ts = jo.getLong("ts");
//        long currTime = System.currentTimeMillis();
//        //60秒
//        if (currTime - ts > 60000) {
////            return this.callBack(ErrorCode.ACCESS_FAILURE, "该参数已过期!", null);
//        }
//
//
//        String instanceName = null;
//        switch (methodName) {
//            case "jstLogin"://学员签到接口
//                methodName = "jstLogin1";
//                instanceName = "syJstLoginController";
//                break;
//            case "passed"://学员签到接口
//                methodName = "passed1";
//                instanceName = "studentController";
//                bizParam.put("clientId", "syjstjkyjiapei");
//                break;
//            case "studentfinish"://学员签退接口
//                methodName = "studentFinish1";
//                instanceName = "studentController";
//                bizParam.put("clientId", "syjstjkyjiapei");
//                break;
//            case "coachpassed"://教练员签到接口
//                methodName = "coachPassed1";
//                instanceName = "coachController";
//                bizParam.put("clientId", "syjstjkyjiapei");
//                break;
//            case "coachfinish"://教练员签退接口
//                methodName = "coachFinish1";
//                instanceName = "coachController";
//                bizParam.put("clientId", "syjstjkyjiapei");
//                break;
//        }
//        if (StringUtils.isBlank(instanceName)) {
//            return callBack(ErrorCode.ACCESS_FAILURE, "接口名不对", null);
//        }
//        try {
//            Object bean = SpringContextUtils.getBean(instanceName);
//            Method method = bean.getClass().getMethod(methodName, RequestFacade.class, String.class);
//            return (Map) method.invoke(bean, null, bizParam.toString());
//        } catch (NoSuchMethodException e) {
//            e.printStackTrace();
//        } catch (IllegalAccessException e) {
//            e.printStackTrace();
//        } catch (InvocationTargetException e) {
//            e.printStackTrace();
//        }
//        return callBack(ErrorCode.ACCESS_FAILURE, "数据异常", null);
//    }
//
//
//    public Map jstLogin1(RequestFacade request, String data) {
//        JSONObject jo = JSONObject.fromObject(data);
//        if (!jo.has("roletype")) {
//            //0:学员 1:教练 2:考核员
//            return this.callBack(ErrorCode.ACCESS_FAILURE, "缺少roletype参数", null);
//        }
//        if (!jo.has("coachStuNum")) {
//            return this.callBack(ErrorCode.ACCESS_FAILURE, "缺少coachStuNum参数", null);
//        }
//        String roletype = jo.getString("roletype");
//        String coachStuNum = jo.getString("coachStuNum");
//        String redisKey = ZlbConstant.YXC_REDIS_ZLB_ID_BY_JST + roletype + coachStuNum;
//
//        if (redisUtil.hasKey(redisKey)) {
//            String certId = (String) redisUtil.get(redisKey);
//            boolean hasKey = redisUtil.hasKey(ZlbConstant.YXC_REDIS_ZLB_ID_BY_UUID + certId);
//            if(hasKey){
//                JSONObject object = new JSONObject();
//                object.put("certId", certId);
//                return this.callBack(ErrorCode.ACCESS_SUCCESS, ErrorCode.ACCESS_SUCCESS_MSG, object);
//            }
//        }
//
//
//        String name = null;
//        String phone = null;
//        String idcard = null;
//        if (StringUtils.equalsIgnoreCase("0", roletype)) {
//            Studentinfo one = studentService.getOne(Wrappers.<Studentinfo>lambdaQuery()
//                    .eq(Studentinfo::getStunum, coachStuNum).last("limit 1"));
//            if (Objects.isNull(one)) {
//                return this.callBack(ErrorCode.ACCESS_FAILURE, "企业端学员信息不存在", null);
//            }
//            if (StringUtils.isBlank(one.getDistrict()) || !StringUtils.equalsIgnoreCase(one.getDistrict(), "330604")) {
//                return this.callBack(ErrorCode.ACCESS_FAILURE, "请确认是否为上虞学员", null);
//            }
//            //获取学员信息
//            Object studentInfo = redisUtil.hget("stunums",coachStuNum);
//            JSONObject stuJsonData = null;
//            if (studentInfo == null) {
//                CommonResponse res = sendHttpClient("/stuinfoqueryByStunum-" + coachStuNum, "3306", "3306", "GET", "获取学员信息");
//                if (res.getErrorcode() != 0) {
//                    return this.callBack(res.getErrorcode(), res.getMessage(), res.getData());
//                }
//                Object stuData = res.getData();
//                stuJsonData = JSONObject.fromObject(stuData);
//                String currstunum = stuJsonData.getString("stunum");
//                //保存到redis  timetodo
//                redisUtil.hset("stunums", currstunum, stuJsonData.toString(), 60 * 60 * 24 * 5);
//            }else {
//                stuJsonData = JSONObject.fromObject(studentInfo.toString());
//            }
//            idcard = stuJsonData.getString("idcard");
//            name = stuJsonData.getString("name");
//            phone = stuJsonData.getString("phone");
//            //学员行政区划
//            String district = stuJsonData.getString("district");
//            if (!StringUtils.equalsIgnoreCase(district, "330604")) {
//                return this.callBack(ErrorCode.ACCESS_FAILURE, "请确认是否为上虞学员！！", null);
//            }
////            name = one.getName();
////            phone = one.getPhone();
////            idcard = one.getIdcard();
////            try {
////                name=JkyCryptorUtils.decrypt(name, key);
////                phone=JkyCryptorUtils.decrypt(phone, key);
////                idcard=JkyCryptorUtils.decrypt(idcard, key);
////            } catch (Exception e) {
////                e.printStackTrace();
////            }
////
////            log.info("上虞驾所通："+name+"  "+phone+"  "+idcard+"  ");
//        } else if (StringUtils.equalsIgnoreCase("1", roletype)) {
//            Coach one = coachService.getOne(Wrappers.<Coach>lambdaQuery()
//                    .eq(Coach::getCoachnum, coachStuNum).last("limit 1"));
//            if (Objects.isNull(one)) {
//                return this.callBack(ErrorCode.ACCESS_FAILURE, "企业端教练员信息不存在", null);
//            }
//            if (StringUtils.equalsIgnoreCase("1", one.getEmploystatus()) ||
//                    Objects.equals(0L, one.getAuditstate()) ||
//                    Objects.equals(1L, one.getStopTrain()) ||
//                    Objects.equals(0L, one.getStatus())) {
//                return this.callBack(ErrorCode.ACCESS_FAILURE, "企业端教练员状态异常（离职/审核不通过/停训/解除备案）", null);
//            }
//            Institution institution = institutionService.getOne(Wrappers.<Institution>lambdaQuery().eq(Institution::getInscode, one.getInscode()).last("limit 1"));
//            if (Objects.isNull(institution)) {
//                return this.callBack(ErrorCode.ACCESS_FAILURE, "企业端驾校不存在", null);
//            }
//            if (StringUtils.isBlank(institution.getDistrict()) || !StringUtils.equalsIgnoreCase(institution.getDistrict(), "330604")) {
//                return this.callBack(ErrorCode.ACCESS_FAILURE, "请确认是否为上虞教练员", null);
//            }
//            name = one.getName();
//            phone = one.getMobile();
//            idcard = one.getIdcard();
//        } else {
//            return this.callBack(ErrorCode.ACCESS_FAILURE, "企业端学员/教练员身份证异常", null);
//        }
//        if (StringUtils.isBlank(idcard)) {
//            return this.callBack(ErrorCode.ACCESS_FAILURE, "仅支持学员/教练员", null);
//        }
//
//
//        BaseZlbPersonInfo zlbPersonInfo = zlbPersonInfoService.getOne(Wrappers.<BaseZlbPersonInfo>lambdaQuery().eq(BaseZlbPersonInfo::getIdnum, idcard).last("limit 1"));
//        if (Objects.isNull(zlbPersonInfo)) {
//            zlbPersonInfo = new BaseZlbPersonInfo();
//            zlbPersonInfo.setCreateTime(new Date());
//            zlbPersonInfo.setUsername(name);
//            zlbPersonInfo.setMobile(phone);
//            zlbPersonInfo.setIdnum(idcard);
//            zlbPersonInfoService.save(zlbPersonInfo);
//        }
//
//        Long id = zlbPersonInfo.getId();
//        String certId = UUID.randomUUID().toString();
//        //设置登录的临时凭证
//        redisUtil.set(ZlbConstant.YXC_REDIS_ZLB_ID_BY_UUID + certId, id, 3600 * 12);
//        //设置zlbId与登录的临时凭证的关联
//        redisUtil.set(ZlbConstant.YXC_REDIS_UUID_BY_ZLB_ID + id, certId, 3600 * 12);
//        //设置zlbId与登录的临时凭证的关联
//        redisUtil.set(ZlbConstant.YXC_REDIS_ZLB_ID_BY_JST + roletype + coachStuNum, certId, 3600 * 12);
//        if (StringUtils.equalsIgnoreCase("0", roletype)) {
//            //设置zlbId与学员编号的关联
//            redisUtil.set(ZlbConstant.YXC_REDIS_STU_NUM_BY_ZLB_ID + id, coachStuNum, 3600 * 12);
//        } else {
//            //设置zlbId与教练员编号的关联
//            redisUtil.set(ZlbConstant.YXC_REDIS_COACH_NUM_BY_ZLB_ID + id, coachStuNum, 3600 * 12);
//        }
//
//        redisUtil.set(redisKey, certId, 3600 * 12);
//        JSONObject object = new JSONObject();
//        object.put("certId", certId);
//        return this.callBack(ErrorCode.ACCESS_SUCCESS, ErrorCode.ACCESS_SUCCESS_MSG, object);
//    }
//
//
//    public static void main(String[] args) {
//        if (!org.apache.commons.lang.StringUtils.equalsIgnoreCase(null, "syjstjkyjiapei")) {
//            System.out.println(1);
//        }
//    }
//}
