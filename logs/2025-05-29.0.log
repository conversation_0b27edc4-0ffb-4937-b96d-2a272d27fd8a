2025-05-29 08:50:21.633 [background-preinit] INFO  org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-05-29 08:50:21.712 [main] INFO  com.jky.boot.JkyCdtpublicApplication:55 - Starting JkyCdtpublicApplication using Java 1.8.0_452 on LAPTOP-8C2H06KS with PID 16764 (E:\核心\交科科技\驾培\衢州驾培\pcPublicAndZlb\quzPublic\public\jky-jeecg-boot-cdtpublic-parent\jky-jeecg-boot-cdtpublic-web\target\classes started by 52417 in E:\核心\交科科技\驾培\衢州驾培\pcPublicAndZlb)
2025-05-29 08:50:21.715 [main] INFO  com.jky.boot.JkyCdtpublicApplication:638 - The following 1 profile is active: "dev"
2025-05-29 08:50:24.836 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-29 08:50:24.841 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-29 08:50:24.971 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:201 - Finished Spring Data repository scanning in 106 ms. Found 0 Redis repository interfaces.
2025-05-29 08:50:25.428 [main] INFO  o.springframework.cloud.context.scope.GenericScope:283 - BeanFactory id=5478f4db-0ff6-3e0e-9d63-dcd55379166c
2025-05-29 08:50:25.580 [main] INFO  c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor:50 - Post-processing PropertySource instances
2025-05-29 08:50:25.654 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-05-29 08:50:25.657 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource shiro [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-05-29 08:50:25.659 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-05-29 08:50:25.659 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-05-29 08:50:25.659 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-05-29 08:50:25.659 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-05-29 08:50:25.659 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-05-29 08:50:25.659 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-05-29 08:50:25.659 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-05-29 08:50:25.659 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-05-29 08:50:25.659 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-05-29 08:50:25.710 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 08:50:25.711 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 08:50:25.712 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$445/671396159] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 08:50:25.715 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 08:50:25.727 [main] INFO  c.u.j.filter.DefaultLazyPropertyFilter:31 - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-05-29 08:50:25.734 [main] INFO  c.u.j.resolver.DefaultLazyPropertyResolver:31 - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-05-29 08:50:25.736 [main] INFO  c.u.j.detector.DefaultLazyPropertyDetector:30 - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-05-29 08:50:25.743 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 08:50:25.750 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 08:50:26.784 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration' of type [org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 08:50:26.786 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusMetricsExportAutoConfiguration' of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusMetricsExportAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 08:50:26.790 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'management.metrics.export.prometheus-org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusProperties' of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 08:50:26.794 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'prometheusConfig' of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusPropertiesConfigAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 08:50:26.796 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'collectorRegistry' of type [io.prometheus.client.CollectorRegistry] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 08:50:26.798 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration' of type [org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 08:50:26.799 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'micrometerClock' of type [io.micrometer.core.instrument.Clock$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 08:50:26.831 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'prometheusMeterRegistry' of type [io.micrometer.prometheus.PrometheusMeterRegistry] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 08:50:26.836 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'micrometerOptions' of type [io.lettuce.core.metrics.MicrometerOptions] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 08:50:26.837 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'lettuceMetrics' of type [org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration$$Lambda$493/531204642] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 08:50:26.973 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 08:50:27.132 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 08:50:27.138 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jeecgBaseConfig' of type [org.jeecg.config.JeecgBaseConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 08:50:27.140 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'shiroConfig' of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$75b20d86] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 08:50:27.633 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 08:50:27.640 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$8004365e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 08:50:27.648 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 08:50:27.691 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'redisConfig' of type [org.jeecg.common.modules.redis.config.RedisConfig$$EnhancerBySpringCGLIB$$93141db6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 08:50:27.746 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'shiroRealm' of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 08:50:27.833 [main] INFO  org.jeecg.config.shiro.ShiroConfig:226 - ===============(1)创建缓存管理器RedisCacheManager
2025-05-29 08:50:27.836 [main] INFO  org.jeecg.config.shiro.ShiroConfig:244 - ===============(2)创建RedisManager,连接Redis..
2025-05-29 08:50:27.841 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 08:50:27.850 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 08:50:27.879 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 08:50:27.913 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$298ea33d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 08:50:27.917 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 08:50:28.554 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8428 (http)
2025-05-29 08:50:28.572 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:168 - Initializing ProtocolHandler ["http-nio-8428"]
2025-05-29 08:50:28.575 [main] INFO  org.apache.catalina.core.StandardService:168 - Starting service [Tomcat]
2025-05-29 08:50:28.575 [main] INFO  org.apache.catalina.core.StandardEngine:168 - Starting Servlet engine: [Apache Tomcat/9.0.104]
2025-05-29 08:50:28.618 [main] WARN  org.apache.catalina.webresources.DirResourceSet:168 - Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8428.8695679680768981244] which is part of the web application [/quzgzpt]
2025-05-29 08:50:28.796 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/quzgzpt]:168 - Initializing Spring embedded WebApplicationContext
2025-05-29 08:50:28.796 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:292 - Root WebApplicationContext: initialization completed in 6887 ms
2025-05-29 08:50:29.176 [main] INFO  org.apache.catalina.core.StandardContext:168 - Suspicious URL pattern: [/sys/api/**] in context [/quzgzpt], see sections 12.1 and 12.2 of the Servlet specification
2025-05-29 08:50:30.365 [main] INFO  com.alibaba.druid.pool.DruidDataSource:994 - {dataSource-1,master} inited
2025-05-29 08:50:30.368 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:132 - dynamic-datasource - load a datasource named [master] success
2025-05-29 08:50:30.368 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:237 - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-05-29 08:50:35.062 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector:56 - class com.jky.boot.system.module.manage.entity.JkySysDictData ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-05-29 08:50:36.873 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector:56 - class com.jky.boot.core.module.gzpt.entity.InstitutionBinding ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-05-29 08:50:38.915 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector:56 - class com.jky.boot.zlb.entity.TMCoach ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-05-29 08:50:39.337 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector:56 - class com.jky.boot.zlb.entity.ZzTransferInscode ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-05-29 08:50:41.226 [main] INFO  org.jeecg.common.modules.redis.config.RedisConfig:56 -  --- redis config init --- 
2025-05-29 08:50:43.771 [main] INFO  o.j.b.s.l.c.s.i.StandaloneRedissonConfigStrategyImpl:33 - 初始化Redisson单机配置,连接地址:************:6379
2025-05-29 08:50:44.216 [main] INFO  org.redisson.Version:41 - Redisson 3.16.1
2025-05-29 08:50:44.934 [redisson-netty-4-32] INFO  o.r.connection.pool.MasterPubSubConnectionPool:166 - 1 connections initialized for ************/************:6379
2025-05-29 08:50:44.966 [redisson-netty-4-21] INFO  org.redisson.connection.pool.MasterConnectionPool:166 - 24 connections initialized for ************/************:6379
2025-05-29 08:50:45.132 [main] INFO  o.j.boot.starter.lock.config.RedissonConfiguration:32 - RedissonManager初始化完成,当前连接方式:STANDALONE,连接地址:************:6379
2025-05-29 08:51:01.007 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector:56 - class com.jky.boot.zlb.entity.TDCity ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-05-29 08:51:03.722 [main] INFO  org.quartz.impl.StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-05-29 08:51:03.735 [main] INFO  org.quartz.simpl.SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-05-29 08:51:03.808 [main] INFO  org.quartz.core.SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-05-29 08:51:03.808 [main] INFO  org.quartz.core.QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-05-29 08:51:03.832 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-05-29 08:51:03.842 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-05-29 08:51:03.845 [main] INFO  org.quartz.core.QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId 'LAPTOP-8C2H06KS1748479863726'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-05-29 08:51:03.845 [main] INFO  org.quartz.impl.StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-05-29 08:51:03.845 [main] INFO  org.quartz.impl.StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-05-29 08:51:03.846 [main] INFO  org.quartz.core.QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@5dcbdcb9
2025-05-29 08:51:08.536 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 0 endpoint(s) beneath base path '/actuator'
2025-05-29 08:51:08.875 [main] INFO  org.jeecg.config.init.CodeGenerateDbConfig:46 -  Init CodeGenerate Config [ Get Db Config From application.yml ] 
2025-05-29 08:51:13.828 [main] INFO  org.springframework.cloud.commons.util.InetUtils:170 - Cannot determine local hostname
2025-05-29 08:51:14.461 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:168 - Starting ProtocolHandler ["http-nio-8428"]
2025-05-29 08:51:14.498 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8428 (http) with context path '/quzgzpt'
2025-05-29 08:51:16.772 [main] INFO  org.springframework.cloud.commons.util.InetUtils:170 - Cannot determine local hostname
2025-05-29 08:51:18.853 [main] INFO  com.jky.boot.JkyCdtpublicApplication:61 - Started JkyCdtpublicApplication in 60.945 seconds (JVM running for 63.311)
2025-05-29 08:51:18.884 [main] INFO  com.jky.boot.JkyCdtpublicApplication:40 - 
----------------------------------------------------------
	Cdtpublic JKY Boot is running! Access URLs:
	Local: 		http://localhost:8428/quzgzpt/
	External: 	http://**************:8428/quzgzpt/
	Swagger文档: 	http://**************:8428/quzgzpt/doc.html
----------------------------------------------------------
2025-05-29 08:51:37.439 [http-nio-8428-exec-4] INFO  o.a.c.c.C.[Tomcat].[localhost].[/quzgzpt]:168 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-29 08:51:37.439 [http-nio-8428-exec-4] INFO  org.springframework.web.servlet.DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-05-29 08:51:37.442 [http-nio-8428-exec-4] INFO  org.springframework.web.servlet.DispatcherServlet:547 - Completed initialization in 3 ms
2025-05-29 08:51:38.624 [http-nio-8428-exec-2] INFO  o.jeecg.modules.cas.controller.ZlbLoginController:232 - gotoUrl============================&token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NDg0ODM0OTgsInVzZXJuYW1lIjoiemxiTG9naW4ifQ.GdlI_JzX_FLEERQzaCKko0YBiG0vBxIhXmsK71EX8hY&id=777
2025-05-29 08:51:40.579 [http-nio-8428-exec-1] INFO  o.j.c.desensitization.aspect.SensitiveDataAspect:76 - 加密操作，Aspect程序耗时：27ms
2025-05-29 08:51:44.055 [http-nio-8428-exec-9] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 08:51:44.059 [http-nio-8428-exec-10] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 08:53:00.580 [http-nio-8428-exec-2] INFO  o.j.boot.starter.lock.client.RedissonLockClient:56 - 获取锁成功,lockName=bankReg:apply:478f5ebe-cd2d-4ded-817b-2fe806b4cfa8
2025-05-29 09:04:22.199 [http-nio-8428-exec-2] ERROR o.j.boot.starter.lock.client.RedissonLockClient:121 - 解锁异常，lockName=bankReg:apply:478f5ebe-cd2d-4ded-817b-2fe806b4cfa8
java.lang.IllegalMonitorStateException: attempt to unlock lock, not locked by current thread by node id: 75e59d53-eb02-418c-9c86-58b112297c94 thread-id: 197
	at org.redisson.RedissonBaseLock.lambda$unlockAsync$1(RedissonBaseLock.java:314)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:187)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:105)
	at org.redisson.misc.RedissonPromise.trySuccess(RedissonPromise.java:82)
	at org.redisson.RedissonBaseLock.lambda$evalWriteAsync$0(RedissonBaseLock.java:226)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:187)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:105)
	at org.redisson.misc.RedissonPromise.trySuccess(RedissonPromise.java:82)
	at org.redisson.command.CommandBatchService.lambda$executeAsync$7(CommandBatchService.java:335)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:187)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:105)
	at org.redisson.misc.RedissonPromise.trySuccess(RedissonPromise.java:82)
	at org.redisson.command.RedisCommonBatchExecutor.handleResult(RedisCommonBatchExecutor.java:130)
	at org.redisson.command.RedisExecutor.checkAttemptPromise(RedisExecutor.java:449)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:169)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:187)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:105)
	at org.redisson.misc.RedissonPromise.trySuccess(RedissonPromise.java:82)
	at org.redisson.client.handler.CommandDecoder.decodeCommandBatch(CommandDecoder.java:301)
	at org.redisson.client.handler.CommandDecoder.decodeCommand(CommandDecoder.java:194)
	at org.redisson.client.handler.CommandDecoder.decode(CommandDecoder.java:122)
	at org.redisson.client.handler.CommandDecoder.decode(CommandDecoder.java:107)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:529)
	at io.netty.handler.codec.ReplayingDecoder.callDecode(ReplayingDecoder.java:366)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:04:22.207 [http-nio-8428-exec-2] ERROR c.j.b.c.exception.JkyCdtpublicExceptionHandler:142 - null
java.lang.NullPointerException: null
	at com.jky.boot.core.module.gzpt.service.impl.BankRegApplyServiceImpl.saveDefaultApply(BankRegApplyServiceImpl.java:116)
	at com.jky.boot.core.module.gzpt.service.impl.BankRegApplyServiceImpl$$FastClassBySpringCGLIB$$5f901cd5.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703)
	at com.jky.boot.core.module.gzpt.service.impl.BankRegApplyServiceImpl$$EnhancerBySpringCGLIB$$75b5cec9.saveDefaultApply(<generated>)
	at com.jky.boot.core.module.reservation.controller.ZlbBankRegApplyController.zlbAdd(ZlbBankRegApplyController.java:67)
	at com.jky.boot.core.module.reservation.controller.ZlbBankRegApplyController$$FastClassBySpringCGLIB$$55aad9fe.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102)
	at com.jky.boot.aop.CryptoAspect.around(CryptoAspect.java:66)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:62)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.jky.boot.annotation.aspect.RedissonLockAspect.aroundRedisson(RedissonLockAspect.java:58)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.jky.boot.core.module.reservation.controller.ZlbBankRegApplyController$$EnhancerBySpringCGLIB$$2ab7ebb7.zlbAdd(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:14:47.687 [http-nio-8428-exec-5] INFO  o.j.boot.starter.lock.client.RedissonLockClient:56 - 获取锁成功,lockName=bankReg:apply:478f5ebe-cd2d-4ded-817b-2fe806b4cfa8
2025-05-29 09:14:49.482 [http-nio-8428-exec-5] ERROR c.j.b.c.exception.JkyCdtpublicExceptionHandler:142 - null
java.lang.NullPointerException: null
	at java.math.BigDecimal.add(BigDecimal.java:1305)
	at com.jky.boot.core.module.gzpt.service.impl.BankRegApplyServiceImpl.setBalanceAmount(BankRegApplyServiceImpl.java:297)
	at com.jky.boot.core.module.gzpt.service.impl.BankRegApplyServiceImpl.createApply(BankRegApplyServiceImpl.java:246)
	at com.jky.boot.core.module.gzpt.service.impl.BankRegApplyServiceImpl.saveDefaultApply(BankRegApplyServiceImpl.java:120)
	at com.jky.boot.core.module.gzpt.service.impl.BankRegApplyServiceImpl$$FastClassBySpringCGLIB$$5f901cd5.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703)
	at com.jky.boot.core.module.gzpt.service.impl.BankRegApplyServiceImpl$$EnhancerBySpringCGLIB$$75b5cec9.saveDefaultApply(<generated>)
	at com.jky.boot.core.module.reservation.controller.ZlbBankRegApplyController.zlbAdd(ZlbBankRegApplyController.java:67)
	at com.jky.boot.core.module.reservation.controller.ZlbBankRegApplyController$$FastClassBySpringCGLIB$$55aad9fe.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102)
	at com.jky.boot.aop.CryptoAspect.around(CryptoAspect.java:66)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:62)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.jky.boot.annotation.aspect.RedissonLockAspect.aroundRedisson(RedissonLockAspect.java:58)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.jky.boot.core.module.reservation.controller.ZlbBankRegApplyController$$EnhancerBySpringCGLIB$$2ab7ebb7.zlbAdd(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:16:00.643 [http-nio-8428-exec-10] INFO  o.j.boot.starter.lock.client.RedissonLockClient:56 - 获取锁成功,lockName=bankReg:apply:478f5ebe-cd2d-4ded-817b-2fe806b4cfa8
2025-05-29 09:21:00.438 [http-nio-8428-exec-10] ERROR o.j.boot.starter.lock.client.RedissonLockClient:121 - 解锁异常，lockName=bankReg:apply:478f5ebe-cd2d-4ded-817b-2fe806b4cfa8
java.lang.IllegalMonitorStateException: attempt to unlock lock, not locked by current thread by node id: 75e59d53-eb02-418c-9c86-58b112297c94 thread-id: 205
	at org.redisson.RedissonBaseLock.lambda$unlockAsync$1(RedissonBaseLock.java:314)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:187)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:105)
	at org.redisson.misc.RedissonPromise.trySuccess(RedissonPromise.java:82)
	at org.redisson.RedissonBaseLock.lambda$evalWriteAsync$0(RedissonBaseLock.java:226)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:187)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:105)
	at org.redisson.misc.RedissonPromise.trySuccess(RedissonPromise.java:82)
	at org.redisson.command.CommandBatchService.lambda$executeAsync$7(CommandBatchService.java:335)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:187)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:105)
	at org.redisson.misc.RedissonPromise.trySuccess(RedissonPromise.java:82)
	at org.redisson.command.RedisCommonBatchExecutor.handleResult(RedisCommonBatchExecutor.java:130)
	at org.redisson.command.RedisExecutor.checkAttemptPromise(RedisExecutor.java:449)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:169)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:187)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:105)
	at org.redisson.misc.RedissonPromise.trySuccess(RedissonPromise.java:82)
	at org.redisson.client.handler.CommandDecoder.decodeCommandBatch(CommandDecoder.java:301)
	at org.redisson.client.handler.CommandDecoder.decodeCommand(CommandDecoder.java:194)
	at org.redisson.client.handler.CommandDecoder.decode(CommandDecoder.java:122)
	at org.redisson.client.handler.CommandDecoder.decode(CommandDecoder.java:107)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:529)
	at io.netty.handler.codec.ReplayingDecoder.callDecode(ReplayingDecoder.java:366)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:21:00.440 [http-nio-8428-exec-10] ERROR c.j.b.c.exception.JkyCdtpublicExceptionHandler:142 - null
java.lang.NullPointerException: null
	at java.math.BigDecimal.add(BigDecimal.java:1305)
	at com.jky.boot.core.module.gzpt.service.impl.BankRegApplyServiceImpl.setBalanceAmount(BankRegApplyServiceImpl.java:297)
	at com.jky.boot.core.module.gzpt.service.impl.BankRegApplyServiceImpl.createApply(BankRegApplyServiceImpl.java:246)
	at com.jky.boot.core.module.gzpt.service.impl.BankRegApplyServiceImpl.saveDefaultApply(BankRegApplyServiceImpl.java:120)
	at com.jky.boot.core.module.gzpt.service.impl.BankRegApplyServiceImpl$$FastClassBySpringCGLIB$$5f901cd5.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703)
	at com.jky.boot.core.module.gzpt.service.impl.BankRegApplyServiceImpl$$EnhancerBySpringCGLIB$$75b5cec9.saveDefaultApply(<generated>)
	at com.jky.boot.core.module.reservation.controller.ZlbBankRegApplyController.zlbAdd(ZlbBankRegApplyController.java:67)
	at com.jky.boot.core.module.reservation.controller.ZlbBankRegApplyController$$FastClassBySpringCGLIB$$55aad9fe.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102)
	at com.jky.boot.aop.CryptoAspect.around(CryptoAspect.java:66)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:62)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.jky.boot.annotation.aspect.RedissonLockAspect.aroundRedisson(RedissonLockAspect.java:58)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.jky.boot.core.module.reservation.controller.ZlbBankRegApplyController$$EnhancerBySpringCGLIB$$2ab7ebb7.zlbAdd(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:25:31.327 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x6c490ab5, L:/192.168.26.197:9125 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:25:31.328 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x40242977, L:/192.168.26.197:9126 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:25:31.328 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x26569e0f, L:/192.168.26.197:9128 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:25:31.329 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xab4777d6, L:/192.168.26.197:9120 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:25:31.329 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xa3305514, L:/192.168.26.197:9127 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:25:31.329 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x671c8fd4, L:/192.168.26.197:9121 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:25:31.329 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xe6bebe8c, L:/192.168.26.197:9118 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:25:31.329 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x62bacd03, L:/192.168.26.197:9122 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:25:31.330 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x154d86ea, L:/192.168.26.197:9119 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:25:31.331 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xcd486aaa, L:/192.168.26.197:9124 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:25:31.331 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xa91923cc, L:0.0.0.0/0.0.0.0:9123 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:25:31.332 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x161ec8a2, L:/192.168.26.197:9129 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:25:31.332 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x618bd869, L:/192.168.26.197:9130 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:25:31.332 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x24deb72c, L:/192.168.26.197:9131 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:25:31.332 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x1728e6ac, L:/192.168.26.197:9132 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:25:31.333 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xf4f49780, L:/192.168.26.197:9135 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:25:31.333 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xa19f55e8, L:/192.168.26.197:9134 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:25:31.333 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x70c19a16, L:/192.168.26.197:9136 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:25:31.333 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x25f9a981, L:/192.168.26.197:9138 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:25:31.334 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xb1f29e85, L:/192.168.26.197:9139 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:25:31.334 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xe0f593ec, L:/192.168.26.197:9140 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:25:31.334 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x95771ec4, L:/192.168.26.197:9133 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:25:31.334 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x5821be27, L:/192.168.26.197:9142 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:25:31.335 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x0d25c3ff, L:/192.168.26.197:9137 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:25:31.335 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x9b9edc16, L:/192.168.26.197:9141 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:32:14.732 [http-nio-8428-exec-4] INFO  o.j.boot.starter.lock.client.RedissonLockClient:56 - 获取锁成功,lockName=bankReg:apply:478f5ebe-cd2d-4ded-817b-2fe806b4cfa8
2025-05-29 09:51:05.964 [http-nio-8428-exec-4] ERROR o.j.boot.starter.lock.client.RedissonLockClient:121 - 解锁异常，lockName=bankReg:apply:478f5ebe-cd2d-4ded-817b-2fe806b4cfa8
java.lang.IllegalMonitorStateException: attempt to unlock lock, not locked by current thread by node id: 75e59d53-eb02-418c-9c86-58b112297c94 thread-id: 199
	at org.redisson.RedissonBaseLock.lambda$unlockAsync$1(RedissonBaseLock.java:314)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:187)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:105)
	at org.redisson.misc.RedissonPromise.trySuccess(RedissonPromise.java:82)
	at org.redisson.RedissonBaseLock.lambda$evalWriteAsync$0(RedissonBaseLock.java:226)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:187)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:105)
	at org.redisson.misc.RedissonPromise.trySuccess(RedissonPromise.java:82)
	at org.redisson.command.CommandBatchService.lambda$executeAsync$7(CommandBatchService.java:335)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:187)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:105)
	at org.redisson.misc.RedissonPromise.trySuccess(RedissonPromise.java:82)
	at org.redisson.command.RedisCommonBatchExecutor.handleResult(RedisCommonBatchExecutor.java:130)
	at org.redisson.command.RedisExecutor.checkAttemptPromise(RedisExecutor.java:449)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:169)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:187)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:105)
	at org.redisson.misc.RedissonPromise.trySuccess(RedissonPromise.java:82)
	at org.redisson.client.handler.CommandDecoder.decodeCommandBatch(CommandDecoder.java:301)
	at org.redisson.client.handler.CommandDecoder.decodeCommand(CommandDecoder.java:194)
	at org.redisson.client.handler.CommandDecoder.decode(CommandDecoder.java:122)
	at org.redisson.client.handler.CommandDecoder.decode(CommandDecoder.java:107)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:529)
	at io.netty.handler.codec.ReplayingDecoder.callDecode(ReplayingDecoder.java:366)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:51:05.965 [http-nio-8428-exec-4] ERROR c.j.b.c.exception.JkyCdtpublicExceptionHandler:142 - null
java.lang.NullPointerException: null
	at java.math.BigDecimal.add(BigDecimal.java:1305)
	at com.jky.boot.core.module.gzpt.service.impl.BankRegApplyServiceImpl.setBalanceAmount(BankRegApplyServiceImpl.java:297)
	at com.jky.boot.core.module.gzpt.service.impl.BankRegApplyServiceImpl.createApply(BankRegApplyServiceImpl.java:246)
	at com.jky.boot.core.module.gzpt.service.impl.BankRegApplyServiceImpl.saveDefaultApply(BankRegApplyServiceImpl.java:120)
	at com.jky.boot.core.module.gzpt.service.impl.BankRegApplyServiceImpl$$FastClassBySpringCGLIB$$5f901cd5.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703)
	at com.jky.boot.core.module.gzpt.service.impl.BankRegApplyServiceImpl$$EnhancerBySpringCGLIB$$75b5cec9.saveDefaultApply(<generated>)
	at com.jky.boot.core.module.reservation.controller.ZlbBankRegApplyController.zlbAdd(ZlbBankRegApplyController.java:67)
	at com.jky.boot.core.module.reservation.controller.ZlbBankRegApplyController$$FastClassBySpringCGLIB$$55aad9fe.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102)
	at com.jky.boot.aop.CryptoAspect.around(CryptoAspect.java:66)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:62)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.jky.boot.annotation.aspect.RedissonLockAspect.aroundRedisson(RedissonLockAspect.java:58)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.jky.boot.core.module.reservation.controller.ZlbBankRegApplyController$$EnhancerBySpringCGLIB$$2ab7ebb7.zlbAdd(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:51:20.270 [http-nio-8428-exec-5] INFO  o.j.boot.starter.lock.client.RedissonLockClient:56 - 获取锁成功,lockName=bankReg:apply:478f5ebe-cd2d-4ded-817b-2fe806b4cfa8
2025-05-29 10:08:26.235 [http-nio-8428-exec-5] ERROR o.j.boot.starter.lock.client.RedissonLockClient:121 - 解锁异常，lockName=bankReg:apply:478f5ebe-cd2d-4ded-817b-2fe806b4cfa8
java.lang.IllegalMonitorStateException: attempt to unlock lock, not locked by current thread by node id: 75e59d53-eb02-418c-9c86-58b112297c94 thread-id: 200
	at org.redisson.RedissonBaseLock.lambda$unlockAsync$1(RedissonBaseLock.java:314)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:187)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:105)
	at org.redisson.misc.RedissonPromise.trySuccess(RedissonPromise.java:82)
	at org.redisson.RedissonBaseLock.lambda$evalWriteAsync$0(RedissonBaseLock.java:226)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:187)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:105)
	at org.redisson.misc.RedissonPromise.trySuccess(RedissonPromise.java:82)
	at org.redisson.command.CommandBatchService.lambda$executeAsync$7(CommandBatchService.java:335)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:187)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:105)
	at org.redisson.misc.RedissonPromise.trySuccess(RedissonPromise.java:82)
	at org.redisson.command.RedisCommonBatchExecutor.handleResult(RedisCommonBatchExecutor.java:130)
	at org.redisson.command.RedisExecutor.checkAttemptPromise(RedisExecutor.java:449)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:169)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:187)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:105)
	at org.redisson.misc.RedissonPromise.trySuccess(RedissonPromise.java:82)
	at org.redisson.client.handler.CommandDecoder.decodeCommandBatch(CommandDecoder.java:301)
	at org.redisson.client.handler.CommandDecoder.decodeCommand(CommandDecoder.java:194)
	at org.redisson.client.handler.CommandDecoder.decode(CommandDecoder.java:122)
	at org.redisson.client.handler.CommandDecoder.decode(CommandDecoder.java:107)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:529)
	at io.netty.handler.codec.ReplayingDecoder.callDecode(ReplayingDecoder.java:366)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:08:26.236 [http-nio-8428-exec-5] ERROR c.j.b.c.exception.JkyCdtpublicExceptionHandler:142 - null
java.lang.NullPointerException: null
	at java.math.BigDecimal.add(BigDecimal.java:1305)
	at com.jky.boot.core.module.gzpt.service.impl.BankRegApplyServiceImpl.setBalanceAmount(BankRegApplyServiceImpl.java:297)
	at com.jky.boot.core.module.gzpt.service.impl.BankRegApplyServiceImpl.createApply(BankRegApplyServiceImpl.java:246)
	at com.jky.boot.core.module.gzpt.service.impl.BankRegApplyServiceImpl.saveDefaultApply(BankRegApplyServiceImpl.java:120)
	at com.jky.boot.core.module.gzpt.service.impl.BankRegApplyServiceImpl$$FastClassBySpringCGLIB$$5f901cd5.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703)
	at com.jky.boot.core.module.gzpt.service.impl.BankRegApplyServiceImpl$$EnhancerBySpringCGLIB$$75b5cec9.saveDefaultApply(<generated>)
	at com.jky.boot.core.module.reservation.controller.ZlbBankRegApplyController.zlbAdd(ZlbBankRegApplyController.java:67)
	at com.jky.boot.core.module.reservation.controller.ZlbBankRegApplyController$$FastClassBySpringCGLIB$$55aad9fe.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:102)
	at com.jky.boot.aop.CryptoAspect.around(CryptoAspect.java:66)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:62)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.jky.boot.annotation.aspect.RedissonLockAspect.aroundRedisson(RedissonLockAspect.java:58)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:634)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:624)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:72)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.jky.boot.core.module.reservation.controller.ZlbBankRegApplyController$$EnhancerBySpringCGLIB$$2ab7ebb7.zlbAdd(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:11:26.431 [http-nio-8428-exec-8] INFO  o.j.boot.starter.lock.client.RedissonLockClient:56 - 获取锁成功,lockName=bankReg:apply:478f5ebe-cd2d-4ded-817b-2fe806b4cfa8
2025-05-29 10:11:35.704 [http-nio-8428-exec-8] ERROR o.j.boot.starter.lock.client.RedissonLockClient:121 - 解锁异常，lockName=bankReg:apply:478f5ebe-cd2d-4ded-817b-2fe806b4cfa8
java.lang.IllegalMonitorStateException: attempt to unlock lock, not locked by current thread by node id: 75e59d53-eb02-418c-9c86-58b112297c94 thread-id: 203
	at org.redisson.RedissonBaseLock.lambda$unlockAsync$1(RedissonBaseLock.java:314)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:187)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:105)
	at org.redisson.misc.RedissonPromise.trySuccess(RedissonPromise.java:82)
	at org.redisson.RedissonBaseLock.lambda$evalWriteAsync$0(RedissonBaseLock.java:226)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:187)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:105)
	at org.redisson.misc.RedissonPromise.trySuccess(RedissonPromise.java:82)
	at org.redisson.command.CommandBatchService.lambda$executeAsync$7(CommandBatchService.java:335)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:187)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:105)
	at org.redisson.misc.RedissonPromise.trySuccess(RedissonPromise.java:82)
	at org.redisson.command.RedisCommonBatchExecutor.handleResult(RedisCommonBatchExecutor.java:130)
	at org.redisson.command.RedisExecutor.checkAttemptPromise(RedisExecutor.java:449)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:169)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:187)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:105)
	at org.redisson.misc.RedissonPromise.trySuccess(RedissonPromise.java:82)
	at org.redisson.client.handler.CommandDecoder.decodeCommandBatch(CommandDecoder.java:301)
	at org.redisson.client.handler.CommandDecoder.decodeCommand(CommandDecoder.java:194)
	at org.redisson.client.handler.CommandDecoder.decode(CommandDecoder.java:122)
	at org.redisson.client.handler.CommandDecoder.decode(CommandDecoder.java:107)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:529)
	at io.netty.handler.codec.ReplayingDecoder.callDecode(ReplayingDecoder.java:366)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:11:47.575 [http-nio-8428-exec-10] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 10:11:47.580 [http-nio-8428-exec-10] INFO  org.jeecg.common.aspect.DictAspect:306 - translateManyDict.dictCodes:bank_reg_state,bank_reg_apply_type,bank_reg_apply_status
2025-05-29 10:11:47.581 [http-nio-8428-exec-10] INFO  org.jeecg.common.aspect.DictAspect:307 - translateManyDict.values:32,1,0
2025-05-29 10:11:48.977 [http-nio-8428-exec-10] INFO  org.jeecg.common.aspect.DictAspect:309 - translateManyDict.result:{}
2025-05-29 10:16:00.747 [http-nio-8428-exec-3] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 10:16:00.773 [http-nio-8428-exec-3] INFO  org.jeecg.common.aspect.DictAspect:306 - translateManyDict.dictCodes:bank_reg_state,bank_reg_apply_type,bank_reg_apply_status
2025-05-29 10:16:00.773 [http-nio-8428-exec-3] INFO  org.jeecg.common.aspect.DictAspect:307 - translateManyDict.values:32,1,0
2025-05-29 10:16:00.791 [http-nio-8428-exec-3] INFO  org.jeecg.common.aspect.DictAspect:309 - translateManyDict.result:{}
2025-05-29 10:16:02.738 [http-nio-8428-exec-5] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 10:16:02.743 [http-nio-8428-exec-5] INFO  org.jeecg.common.aspect.DictAspect:306 - translateManyDict.dictCodes:bank_reg_state,bank_reg_apply_type,bank_reg_apply_status
2025-05-29 10:16:02.743 [http-nio-8428-exec-5] INFO  org.jeecg.common.aspect.DictAspect:307 - translateManyDict.values:32,1,0
2025-05-29 10:16:02.750 [http-nio-8428-exec-5] INFO  org.jeecg.common.aspect.DictAspect:309 - translateManyDict.result:{}
2025-05-29 10:16:26.136 [http-nio-8428-exec-8] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 10:16:26.140 [http-nio-8428-exec-8] INFO  org.jeecg.common.aspect.DictAspect:306 - translateManyDict.dictCodes:bank_reg_state,bank_reg_apply_type,bank_reg_apply_status
2025-05-29 10:16:26.141 [http-nio-8428-exec-8] INFO  org.jeecg.common.aspect.DictAspect:307 - translateManyDict.values:32,1,0
2025-05-29 10:16:26.147 [http-nio-8428-exec-8] INFO  org.jeecg.common.aspect.DictAspect:309 - translateManyDict.result:{}
2025-05-29 10:29:11.166 [http-nio-8428-exec-2] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 10:29:11.176 [http-nio-8428-exec-2] INFO  org.jeecg.common.aspect.DictAspect:306 - translateManyDict.dictCodes:bank_reg_state,bank_reg_apply_type,bank_reg_apply_status
2025-05-29 10:29:11.176 [http-nio-8428-exec-2] INFO  org.jeecg.common.aspect.DictAspect:307 - translateManyDict.values:32,1,0
2025-05-29 10:29:11.184 [http-nio-8428-exec-2] INFO  org.jeecg.common.aspect.DictAspect:309 - translateManyDict.result:{bank_reg_state=[DictModel(value=1, text=单笔冻结), DictModel(value=32, text=三笔冻结第二笔（科二实操）)]}
2025-05-29 10:45:22.294 [http-nio-8428-exec-5] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 10:45:22.312 [http-nio-8428-exec-5] INFO  org.jeecg.common.aspect.DictAspect:306 - translateManyDict.dictCodes:bank_reg_state,bank_reg_apply_type,bank_reg_apply_status
2025-05-29 10:45:22.312 [http-nio-8428-exec-5] INFO  org.jeecg.common.aspect.DictAspect:307 - translateManyDict.values:1,0
2025-05-29 10:45:22.319 [http-nio-8428-exec-5] INFO  org.jeecg.common.aspect.DictAspect:309 - translateManyDict.result:{bank_reg_state=[DictModel(value=1, text=单笔冻结)], bank_reg_apply_type=[DictModel(value=1, text=冻结)], bank_reg_apply_status=[DictModel(value=0, text=初始), DictModel(value=1, text=驾校审核通过)]}
2025-05-29 10:45:46.946 [http-nio-8428-exec-8] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 10:45:50.206 [http-nio-8428-exec-9] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 10:46:18.379 [http-nio-8428-exec-2] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 10:49:15.081 [http-nio-8428-exec-5] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 10:52:10.213 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x75c16828, L:/192.168.26.197:10534 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:52:10.213 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xf46c4454, L:/192.168.26.197:10527 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:52:10.214 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x416b5f9e, L:/192.168.26.197:10535 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:52:10.215 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x7f7c2e6a, L:/192.168.26.197:10536 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:52:10.215 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x1c906100, L:/192.168.26.197:10539 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:52:10.216 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xe2ea73c9, L:/192.168.26.197:10528 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:52:10.216 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x7ffbfe20, L:/192.168.26.197:10543 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:52:10.216 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xb7ae4eaf, L:/192.168.26.197:10540 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:52:10.216 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x7bcd1ba7, L:/192.168.26.197:10544 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:52:10.217 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xf4252b83, L:/192.168.26.197:10542 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:52:10.217 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xfad2536d, L:/192.168.26.197:10548 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:52:10.217 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x6016a3ba, L:/192.168.26.197:10549 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:52:10.217 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x30835db8, L:/192.168.26.197:10546 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:52:10.217 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x627a58d2, L:/192.168.26.197:10532 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:52:10.219 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xa5c5ef48, L:/192.168.26.197:10551 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:52:10.219 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xea6e7e48, L:/192.168.26.197:10547 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:52:10.219 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x06966e38, L:/192.168.26.197:10545 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:52:10.220 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x06733be7, L:/192.168.26.197:10531 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:52:10.220 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x3e39c9de, L:/192.168.26.197:10553 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:52:10.220 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x96e4621f, L:/192.168.26.197:10552 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:52:10.220 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xfaa3b5a5, L:/192.168.26.197:10554 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:52:10.220 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xbaf8ebe6, L:/192.168.26.197:10550 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:52:10.220 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xf97ed880, L:/192.168.26.197:10541 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:52:10.221 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x29152621, L:/192.168.26.197:10556 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:52:10.221 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x802deb73, L:/192.168.26.197:10555 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:52:30.102 [http-nio-8428-exec-9] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 10:52:39.069 [http-nio-8428-exec-2] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 10:56:25.461 [http-nio-8428-exec-5] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 10:56:27.040 [http-nio-8428-exec-6] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 11:00:39.445 [http-nio-8428-exec-2] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 11:02:05.504 [http-nio-8428-exec-5] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 11:02:19.870 [Thread-98] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder:108 - [HttpClientBeanHolder] Start destroying common HttpClient
2025-05-29 11:02:19.874 [Thread-98] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder:114 - [HttpClientBeanHolder] Destruction of the end
2025-05-29 11:02:21.385 [SpringApplicationShutdownHook] INFO  o.s.scheduling.quartz.SchedulerFactoryBean:847 - Shutting down Quartz Scheduler
2025-05-29 11:02:21.385 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler:666 - Scheduler MyScheduler_$_LAPTOP-8C2H06KS1748479863726 shutting down.
2025-05-29 11:02:21.385 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler:585 - Scheduler MyScheduler_$_LAPTOP-8C2H06KS1748479863726 paused.
2025-05-29 11:02:21.386 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler:740 - Scheduler MyScheduler_$_LAPTOP-8C2H06KS1748479863726 shutdown complete.
2025-05-29 11:02:21.441 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:217 - dynamic-datasource start closing ....
2025-05-29 11:02:21.444 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource:2029 - {dataSource-1} closing ...
2025-05-29 11:02:21.451 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource:2101 - {dataSource-1} closed
2025-05-29 11:02:21.451 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:221 - dynamic-datasource all closed success,bye
2025-05-29 11:02:33.360 [background-preinit] INFO  org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-05-29 11:02:33.391 [main] INFO  com.jky.boot.JkyCdtpublicApplication:55 - Starting JkyCdtpublicApplication using Java 1.8.0_452 on LAPTOP-8C2H06KS with PID 68360 (E:\核心\交科科技\驾培\衢州驾培\pcPublicAndZlb\quzPublic\public\jky-jeecg-boot-cdtpublic-parent\jky-jeecg-boot-cdtpublic-web\target\classes started by 52417 in E:\核心\交科科技\驾培\衢州驾培\pcPublicAndZlb)
2025-05-29 11:02:33.392 [main] INFO  com.jky.boot.JkyCdtpublicApplication:638 - The following 1 profile is active: "dev"
2025-05-29 11:02:35.862 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-29 11:02:35.865 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-29 11:02:36.002 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:201 - Finished Spring Data repository scanning in 118 ms. Found 0 Redis repository interfaces.
2025-05-29 11:02:36.464 [main] INFO  o.springframework.cloud.context.scope.GenericScope:283 - BeanFactory id=5478f4db-0ff6-3e0e-9d63-dcd55379166c
2025-05-29 11:02:36.704 [main] INFO  c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor:50 - Post-processing PropertySource instances
2025-05-29 11:02:36.798 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-05-29 11:02:36.800 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource shiro [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-05-29 11:02:36.801 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-05-29 11:02:36.801 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-05-29 11:02:36.802 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-05-29 11:02:36.802 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-05-29 11:02:36.802 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-05-29 11:02:36.802 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-05-29 11:02:36.802 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-05-29 11:02:36.802 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-05-29 11:02:36.803 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-05-29 11:02:36.864 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 11:02:36.867 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 11:02:36.868 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$445/180623266] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 11:02:36.872 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 11:02:36.885 [main] INFO  c.u.j.filter.DefaultLazyPropertyFilter:31 - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-05-29 11:02:36.892 [main] INFO  c.u.j.resolver.DefaultLazyPropertyResolver:31 - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-05-29 11:02:36.894 [main] INFO  c.u.j.detector.DefaultLazyPropertyDetector:30 - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-05-29 11:02:36.901 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 11:02:36.909 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 11:02:37.946 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration' of type [org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 11:02:37.947 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusMetricsExportAutoConfiguration' of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusMetricsExportAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 11:02:37.952 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'management.metrics.export.prometheus-org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusProperties' of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 11:02:37.954 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'prometheusConfig' of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusPropertiesConfigAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 11:02:37.957 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'collectorRegistry' of type [io.prometheus.client.CollectorRegistry] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 11:02:37.957 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration' of type [org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 11:02:37.959 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'micrometerClock' of type [io.micrometer.core.instrument.Clock$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 11:02:37.987 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'prometheusMeterRegistry' of type [io.micrometer.prometheus.PrometheusMeterRegistry] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 11:02:37.993 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'micrometerOptions' of type [io.lettuce.core.metrics.MicrometerOptions] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 11:02:37.994 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'lettuceMetrics' of type [org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration$$Lambda$493/5930625] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 11:02:38.121 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 11:02:38.270 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 11:02:38.276 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jeecgBaseConfig' of type [org.jeecg.config.JeecgBaseConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 11:02:38.278 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'shiroConfig' of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$dcda099d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 11:02:38.772 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 11:02:38.778 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$e72c3275] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 11:02:38.786 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 11:02:38.824 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'redisConfig' of type [org.jeecg.common.modules.redis.config.RedisConfig$$EnhancerBySpringCGLIB$$fa3c19cd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 11:02:38.876 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'shiroRealm' of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 11:02:38.962 [main] INFO  org.jeecg.config.shiro.ShiroConfig:226 - ===============(1)创建缓存管理器RedisCacheManager
2025-05-29 11:02:38.966 [main] INFO  org.jeecg.config.shiro.ShiroConfig:244 - ===============(2)创建RedisManager,连接Redis..
2025-05-29 11:02:38.969 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 11:02:38.977 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 11:02:39.004 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 11:02:39.042 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$90b69f54] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 11:02:39.048 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 11:02:39.568 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8428 (http)
2025-05-29 11:02:39.714 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:168 - Initializing ProtocolHandler ["http-nio-8428"]
2025-05-29 11:02:39.717 [main] INFO  org.apache.catalina.core.StandardService:168 - Starting service [Tomcat]
2025-05-29 11:02:39.717 [main] INFO  org.apache.catalina.core.StandardEngine:168 - Starting Servlet engine: [Apache Tomcat/9.0.104]
2025-05-29 11:02:39.767 [main] WARN  org.apache.catalina.webresources.DirResourceSet:168 - Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8428.2087889680828157026] which is part of the web application [/quzgzpt]
2025-05-29 11:02:39.981 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/quzgzpt]:168 - Initializing Spring embedded WebApplicationContext
2025-05-29 11:02:39.982 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:292 - Root WebApplicationContext: initialization completed in 6502 ms
2025-05-29 11:02:40.329 [main] INFO  org.apache.catalina.core.StandardContext:168 - Suspicious URL pattern: [/sys/api/**] in context [/quzgzpt], see sections 12.1 and 12.2 of the Servlet specification
2025-05-29 11:02:41.342 [main] INFO  com.alibaba.druid.pool.DruidDataSource:994 - {dataSource-1,master} inited
2025-05-29 11:02:41.345 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:132 - dynamic-datasource - load a datasource named [master] success
2025-05-29 11:02:41.345 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:237 - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-05-29 11:02:45.281 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector:56 - class com.jky.boot.system.module.manage.entity.JkySysDictData ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-05-29 11:02:47.211 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector:56 - class com.jky.boot.core.module.gzpt.entity.InstitutionBinding ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-05-29 11:02:49.338 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector:56 - class com.jky.boot.zlb.entity.TMCoach ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-05-29 11:02:49.733 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector:56 - class com.jky.boot.zlb.entity.ZzTransferInscode ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-05-29 11:02:51.197 [main] INFO  org.jeecg.common.modules.redis.config.RedisConfig:56 -  --- redis config init --- 
2025-05-29 11:02:53.192 [main] INFO  o.j.b.s.l.c.s.i.StandaloneRedissonConfigStrategyImpl:33 - 初始化Redisson单机配置,连接地址:************:6379
2025-05-29 11:02:53.569 [main] INFO  org.redisson.Version:41 - Redisson 3.16.1
2025-05-29 11:02:54.151 [redisson-netty-4-23] INFO  o.r.connection.pool.MasterPubSubConnectionPool:166 - 1 connections initialized for ************/************:6379
2025-05-29 11:02:54.166 [redisson-netty-4-18] INFO  org.redisson.connection.pool.MasterConnectionPool:166 - 24 connections initialized for ************/************:6379
2025-05-29 11:02:54.289 [main] INFO  o.j.boot.starter.lock.config.RedissonConfiguration:32 - RedissonManager初始化完成,当前连接方式:STANDALONE,连接地址:************:6379
2025-05-29 11:03:08.840 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector:56 - class com.jky.boot.zlb.entity.TDCity ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-05-29 11:03:11.207 [main] INFO  org.quartz.impl.StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-05-29 11:03:11.220 [main] INFO  org.quartz.simpl.SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-05-29 11:03:11.290 [main] INFO  org.quartz.core.SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-05-29 11:03:11.290 [main] INFO  org.quartz.core.QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-05-29 11:03:11.313 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-05-29 11:03:11.322 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-05-29 11:03:11.326 [main] INFO  org.quartz.core.QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId 'LAPTOP-8C2H06KS1748487791211'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-05-29 11:03:11.326 [main] INFO  org.quartz.impl.StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-05-29 11:03:11.327 [main] INFO  org.quartz.impl.StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-05-29 11:03:11.327 [main] INFO  org.quartz.core.QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@79a15001
2025-05-29 11:03:15.727 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 0 endpoint(s) beneath base path '/actuator'
2025-05-29 11:03:16.007 [main] INFO  org.jeecg.config.init.CodeGenerateDbConfig:46 -  Init CodeGenerate Config [ Get Db Config From application.yml ] 
2025-05-29 11:03:20.659 [main] INFO  org.springframework.cloud.commons.util.InetUtils:170 - Cannot determine local hostname
2025-05-29 11:03:21.245 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:168 - Starting ProtocolHandler ["http-nio-8428"]
2025-05-29 11:03:21.280 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8428 (http) with context path '/quzgzpt'
2025-05-29 11:03:23.521 [main] INFO  org.springframework.cloud.commons.util.InetUtils:170 - Cannot determine local hostname
2025-05-29 11:03:25.618 [main] INFO  com.jky.boot.JkyCdtpublicApplication:61 - Started JkyCdtpublicApplication in 55.117 seconds (JVM running for 57.165)
2025-05-29 11:03:25.646 [main] INFO  com.jky.boot.JkyCdtpublicApplication:40 - 
----------------------------------------------------------
	Cdtpublic JKY Boot is running! Access URLs:
	Local: 		http://localhost:8428/quzgzpt/
	External: 	http://**************:8428/quzgzpt/
	Swagger文档: 	http://**************:8428/quzgzpt/doc.html
----------------------------------------------------------
2025-05-29 11:10:33.675 [http-nio-8428-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/quzgzpt]:168 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-29 11:10:33.676 [http-nio-8428-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-05-29 11:10:33.678 [http-nio-8428-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet:547 - Completed initialization in 2 ms
2025-05-29 11:10:35.225 [http-nio-8428-exec-2] INFO  o.j.boot.starter.lock.client.RedissonLockClient:56 - 获取锁成功,lockName=bankReg:apply:478f5ebe-cd2d-4ded-817b-2fe806b4cfa8
2025-05-29 11:12:20.390 [http-nio-8428-exec-2] ERROR o.j.boot.starter.lock.client.RedissonLockClient:121 - 解锁异常，lockName=bankReg:apply:478f5ebe-cd2d-4ded-817b-2fe806b4cfa8
java.lang.IllegalMonitorStateException: attempt to unlock lock, not locked by current thread by node id: f36cf395-b6fd-4483-9163-7e4c7bca4f2d thread-id: 188
	at org.redisson.RedissonBaseLock.lambda$unlockAsync$1(RedissonBaseLock.java:314)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:187)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:105)
	at org.redisson.misc.RedissonPromise.trySuccess(RedissonPromise.java:82)
	at org.redisson.RedissonBaseLock.lambda$evalWriteAsync$0(RedissonBaseLock.java:226)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:187)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:105)
	at org.redisson.misc.RedissonPromise.trySuccess(RedissonPromise.java:82)
	at org.redisson.command.CommandBatchService.lambda$executeAsync$7(CommandBatchService.java:335)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:187)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners0(DefaultPromise.java:583)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:559)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:105)
	at org.redisson.misc.RedissonPromise.trySuccess(RedissonPromise.java:82)
	at org.redisson.command.RedisCommonBatchExecutor.handleResult(RedisCommonBatchExecutor.java:130)
	at org.redisson.command.RedisExecutor.checkAttemptPromise(RedisExecutor.java:449)
	at org.redisson.command.RedisExecutor.lambda$execute$3(RedisExecutor.java:169)
	at org.redisson.misc.RedissonPromise.lambda$onComplete$0(RedissonPromise.java:187)
	at io.netty.util.concurrent.DefaultPromise.notifyListener0(DefaultPromise.java:590)
	at io.netty.util.concurrent.DefaultPromise.notifyListenersNow(DefaultPromise.java:557)
	at io.netty.util.concurrent.DefaultPromise.notifyListeners(DefaultPromise.java:492)
	at io.netty.util.concurrent.DefaultPromise.setValue0(DefaultPromise.java:636)
	at io.netty.util.concurrent.DefaultPromise.setSuccess0(DefaultPromise.java:625)
	at io.netty.util.concurrent.DefaultPromise.trySuccess(DefaultPromise.java:105)
	at org.redisson.misc.RedissonPromise.trySuccess(RedissonPromise.java:82)
	at org.redisson.client.handler.CommandDecoder.decodeCommandBatch(CommandDecoder.java:301)
	at org.redisson.client.handler.CommandDecoder.decodeCommand(CommandDecoder.java:194)
	at org.redisson.client.handler.CommandDecoder.decode(CommandDecoder.java:122)
	at org.redisson.client.handler.CommandDecoder.decode(CommandDecoder.java:107)
	at io.netty.handler.codec.ByteToMessageDecoder.decodeRemovalReentryProtection(ByteToMessageDecoder.java:529)
	at io.netty.handler.codec.ReplayingDecoder.callDecode(ReplayingDecoder.java:366)
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 11:12:25.587 [http-nio-8428-exec-5] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 11:14:16.474 [http-nio-8428-exec-9] INFO  o.j.boot.starter.lock.client.RedissonLockClient:56 - 获取锁成功,lockName=bankReg:apply:478f5ebe-cd2d-4ded-817b-2fe806b4cfa8
2025-05-29 11:14:19.033 [http-nio-8428-exec-1] INFO  o.j.boot.starter.lock.client.RedissonLockClient:56 - 获取锁成功,lockName=bankReg:apply:478f5ebe-cd2d-4ded-817b-2fe806b4cfa8
2025-05-29 11:16:55.286 [http-nio-8428-exec-5] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 11:33:34.340 [http-nio-8428-exec-9] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 11:39:01.117 [http-nio-8428-exec-4] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 11:41:21.990 [http-nio-8428-exec-7] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 11:41:24.639 [http-nio-8428-exec-9] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 11:41:30.091 [http-nio-8428-exec-10] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 11:41:34.302 [http-nio-8428-exec-4] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 11:41:35.453 [http-nio-8428-exec-5] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 11:51:29.432 [http-nio-8428-exec-9] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 11:52:01.116 [http-nio-8428-exec-10] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 12:33:37.593 [http-nio-8428-exec-5] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 12:34:12.817 [http-nio-8428-exec-6] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 12:34:26.962 [http-nio-8428-exec-9] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 12:35:11.464 [http-nio-8428-exec-10] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 12:35:26.640 [http-nio-8428-exec-2] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 12:36:05.496 [http-nio-8428-exec-5] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 13:54:21.572 [http-nio-8428-exec-9] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 13:58:41.963 [http-nio-8428-exec-2] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:00:44.428 [http-nio-8428-exec-6] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:00:49.931 [http-nio-8428-exec-9] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:00:53.728 [http-nio-8428-exec-10] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:00:58.728 [http-nio-8428-exec-2] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:01:18.567 [http-nio-8428-exec-5] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:01:28.783 [http-nio-8428-exec-6] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:01:34.035 [http-nio-8428-exec-9] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:01:42.234 [http-nio-8428-exec-10] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:01:44.501 [http-nio-8428-exec-2] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:01:48.068 [http-nio-8428-exec-5] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:01:58.781 [http-nio-8428-exec-6] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:02:07.764 [http-nio-8428-exec-9] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:02:13.693 [http-nio-8428-exec-10] INFO  o.j.boot.starter.lock.client.RedissonLockClient:56 - 获取锁成功,lockName=bankReg:apply:478f5ebe-cd2d-4ded-817b-2fe806b4cfa8
2025-05-29 14:02:16.316 [http-nio-8428-exec-2] INFO  o.j.boot.starter.lock.client.RedissonLockClient:56 - 获取锁成功,lockName=bankReg:apply:478f5ebe-cd2d-4ded-817b-2fe806b4cfa8
2025-05-29 14:02:24.109 [http-nio-8428-exec-5] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:02:25.088 [http-nio-8428-exec-6] INFO  o.j.boot.starter.lock.client.RedissonLockClient:56 - 获取锁成功,lockName=bankReg:apply:478f5ebe-cd2d-4ded-817b-2fe806b4cfa8
2025-05-29 14:03:04.328 [http-nio-8428-exec-9] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:03:04.352 [http-nio-8428-exec-10] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:03:04.357 [http-nio-8428-exec-2] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:03:04.414 [http-nio-8428-exec-5] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:03:06.668 [http-nio-8428-exec-6] INFO  o.j.boot.starter.lock.client.RedissonLockClient:56 - 获取锁成功,lockName=bankReg:apply:478f5ebe-cd2d-4ded-817b-2fe806b4cfa8
2025-05-29 14:03:29.742 [http-nio-8428-exec-10] INFO  o.j.boot.starter.lock.client.RedissonLockClient:56 - 获取锁成功,lockName=bankReg:apply:478f5ebe-cd2d-4ded-817b-2fe806b4cfa8
2025-05-29 14:03:52.833 [http-nio-8428-exec-6] INFO  o.j.boot.starter.lock.client.RedissonLockClient:56 - 获取锁成功,lockName=bankReg:apply:478f5ebe-cd2d-4ded-817b-2fe806b4cfa8
2025-05-29 14:04:12.790 [http-nio-8428-exec-8] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:04:14.371 [http-nio-8428-exec-4] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:04:26.168 [http-nio-8428-exec-5] INFO  o.j.boot.starter.lock.client.RedissonLockClient:56 - 获取锁成功,lockName=bankReg:apply:478f5ebe-cd2d-4ded-817b-2fe806b4cfa8
2025-05-29 14:05:03.670 [http-nio-8428-exec-6] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:05:06.588 [http-nio-8428-exec-8] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:05:10.813 [http-nio-8428-exec-4] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:15:56.685 [Thread-87] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder:108 - [HttpClientBeanHolder] Start destroying common HttpClient
2025-05-29 14:15:56.691 [Thread-87] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder:114 - [HttpClientBeanHolder] Destruction of the end
2025-05-29 14:15:58.337 [SpringApplicationShutdownHook] INFO  o.s.scheduling.quartz.SchedulerFactoryBean:847 - Shutting down Quartz Scheduler
2025-05-29 14:15:58.338 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler:666 - Scheduler MyScheduler_$_LAPTOP-8C2H06KS1748487791211 shutting down.
2025-05-29 14:15:58.338 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler:585 - Scheduler MyScheduler_$_LAPTOP-8C2H06KS1748487791211 paused.
2025-05-29 14:15:58.338 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler:740 - Scheduler MyScheduler_$_LAPTOP-8C2H06KS1748487791211 shutdown complete.
2025-05-29 14:15:58.392 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:217 - dynamic-datasource start closing ....
2025-05-29 14:15:58.394 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource:2029 - {dataSource-1} closing ...
2025-05-29 14:15:58.401 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource:2101 - {dataSource-1} closed
2025-05-29 14:15:58.401 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:221 - dynamic-datasource all closed success,bye
2025-05-29 14:16:14.682 [background-preinit] INFO  org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-05-29 14:16:14.721 [main] INFO  com.jky.boot.JkyCdtpublicApplication:55 - Starting JkyCdtpublicApplication using Java 1.8.0_452 on LAPTOP-8C2H06KS with PID 71824 (E:\核心\交科科技\驾培\衢州驾培\pcPublicAndZlb\quzPublic\public\jky-jeecg-boot-cdtpublic-parent\jky-jeecg-boot-cdtpublic-web\target\classes started by 52417 in E:\核心\交科科技\驾培\衢州驾培\pcPublicAndZlb)
2025-05-29 14:16:14.721 [main] INFO  com.jky.boot.JkyCdtpublicApplication:638 - The following 1 profile is active: "dev"
2025-05-29 14:16:17.810 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-29 14:16:17.815 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-29 14:16:18.015 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:201 - Finished Spring Data repository scanning in 176 ms. Found 0 Redis repository interfaces.
2025-05-29 14:16:18.528 [main] INFO  o.springframework.cloud.context.scope.GenericScope:283 - BeanFactory id=5478f4db-0ff6-3e0e-9d63-dcd55379166c
2025-05-29 14:16:18.727 [main] INFO  c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor:50 - Post-processing PropertySource instances
2025-05-29 14:16:18.802 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-05-29 14:16:18.804 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource shiro [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-05-29 14:16:18.804 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-05-29 14:16:18.804 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-05-29 14:16:18.805 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-05-29 14:16:18.805 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-05-29 14:16:18.805 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-05-29 14:16:18.806 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-05-29 14:16:18.806 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-05-29 14:16:18.806 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-05-29 14:16:18.806 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-05-29 14:16:18.850 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 14:16:18.852 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 14:16:18.853 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$445/180623266] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 14:16:18.855 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 14:16:18.864 [main] INFO  c.u.j.filter.DefaultLazyPropertyFilter:31 - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-05-29 14:16:18.869 [main] INFO  c.u.j.resolver.DefaultLazyPropertyResolver:31 - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-05-29 14:16:18.870 [main] INFO  c.u.j.detector.DefaultLazyPropertyDetector:30 - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-05-29 14:16:18.877 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 14:16:18.881 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 14:16:19.763 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration' of type [org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 14:16:19.765 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusMetricsExportAutoConfiguration' of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusMetricsExportAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 14:16:19.768 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'management.metrics.export.prometheus-org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusProperties' of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 14:16:19.772 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'prometheusConfig' of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusPropertiesConfigAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 14:16:19.774 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'collectorRegistry' of type [io.prometheus.client.CollectorRegistry] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 14:16:19.774 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration' of type [org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 14:16:19.775 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'micrometerClock' of type [io.micrometer.core.instrument.Clock$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 14:16:19.800 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'prometheusMeterRegistry' of type [io.micrometer.prometheus.PrometheusMeterRegistry] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 14:16:19.804 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'micrometerOptions' of type [io.lettuce.core.metrics.MicrometerOptions] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 14:16:19.804 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'lettuceMetrics' of type [org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration$$Lambda$493/5930625] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 14:16:19.918 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 14:16:20.041 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 14:16:20.045 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jeecgBaseConfig' of type [org.jeecg.config.JeecgBaseConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 14:16:20.048 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'shiroConfig' of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$dcda099d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 14:16:20.568 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 14:16:20.575 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$e72c3275] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 14:16:20.583 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 14:16:20.627 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'redisConfig' of type [org.jeecg.common.modules.redis.config.RedisConfig$$EnhancerBySpringCGLIB$$fa3c19cd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 14:16:20.683 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'shiroRealm' of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 14:16:20.765 [main] INFO  org.jeecg.config.shiro.ShiroConfig:226 - ===============(1)创建缓存管理器RedisCacheManager
2025-05-29 14:16:20.767 [main] INFO  org.jeecg.config.shiro.ShiroConfig:244 - ===============(2)创建RedisManager,连接Redis..
2025-05-29 14:16:20.771 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 14:16:20.779 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 14:16:20.805 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 14:16:20.833 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$90b69f54] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 14:16:20.836 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 14:16:21.281 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8428 (http)
2025-05-29 14:16:21.414 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:168 - Initializing ProtocolHandler ["http-nio-8428"]
2025-05-29 14:16:21.418 [main] INFO  org.apache.catalina.core.StandardService:168 - Starting service [Tomcat]
2025-05-29 14:16:21.418 [main] INFO  org.apache.catalina.core.StandardEngine:168 - Starting Servlet engine: [Apache Tomcat/9.0.104]
2025-05-29 14:16:21.463 [main] WARN  org.apache.catalina.webresources.DirResourceSet:168 - Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8428.2455161966460797884] which is part of the web application [/quzgzpt]
2025-05-29 14:16:21.687 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/quzgzpt]:168 - Initializing Spring embedded WebApplicationContext
2025-05-29 14:16:21.688 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:292 - Root WebApplicationContext: initialization completed in 6871 ms
2025-05-29 14:16:22.033 [main] INFO  org.apache.catalina.core.StandardContext:168 - Suspicious URL pattern: [/sys/api/**] in context [/quzgzpt], see sections 12.1 and 12.2 of the Servlet specification
2025-05-29 14:16:22.941 [main] INFO  com.alibaba.druid.pool.DruidDataSource:994 - {dataSource-1,master} inited
2025-05-29 14:16:22.943 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:132 - dynamic-datasource - load a datasource named [master] success
2025-05-29 14:16:22.944 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:237 - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-05-29 14:16:27.328 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector:56 - class com.jky.boot.system.module.manage.entity.JkySysDictData ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-05-29 14:16:29.184 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector:56 - class com.jky.boot.core.module.gzpt.entity.InstitutionBinding ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-05-29 14:16:31.323 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector:56 - class com.jky.boot.zlb.entity.TMCoach ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-05-29 14:16:31.788 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector:56 - class com.jky.boot.zlb.entity.ZzTransferInscode ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-05-29 14:16:33.674 [main] INFO  org.jeecg.common.modules.redis.config.RedisConfig:56 -  --- redis config init --- 
2025-05-29 14:16:35.905 [main] INFO  o.j.b.s.l.c.s.i.StandaloneRedissonConfigStrategyImpl:33 - 初始化Redisson单机配置,连接地址:************:6379
2025-05-29 14:16:36.312 [main] INFO  org.redisson.Version:41 - Redisson 3.16.1
2025-05-29 14:16:36.878 [redisson-netty-4-13] INFO  o.r.connection.pool.MasterPubSubConnectionPool:166 - 1 connections initialized for ************/************:6379
2025-05-29 14:16:36.917 [redisson-netty-4-18] INFO  org.redisson.connection.pool.MasterConnectionPool:166 - 24 connections initialized for ************/************:6379
2025-05-29 14:16:37.026 [main] INFO  o.j.boot.starter.lock.config.RedissonConfiguration:32 - RedissonManager初始化完成,当前连接方式:STANDALONE,连接地址:************:6379
2025-05-29 14:16:49.852 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector:56 - class com.jky.boot.zlb.entity.TDCity ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-05-29 14:16:52.407 [main] INFO  org.quartz.impl.StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-05-29 14:16:52.419 [main] INFO  org.quartz.simpl.SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-05-29 14:16:52.493 [main] INFO  org.quartz.core.SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-05-29 14:16:52.493 [main] INFO  org.quartz.core.QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-05-29 14:16:52.516 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-05-29 14:16:52.524 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-05-29 14:16:52.527 [main] INFO  org.quartz.core.QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId 'LAPTOP-8C2H06KS1748499412411'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-05-29 14:16:52.528 [main] INFO  org.quartz.impl.StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-05-29 14:16:52.528 [main] INFO  org.quartz.impl.StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-05-29 14:16:52.528 [main] INFO  org.quartz.core.QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@5fc3b20b
2025-05-29 14:16:57.787 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 0 endpoint(s) beneath base path '/actuator'
2025-05-29 14:16:58.119 [main] INFO  org.jeecg.config.init.CodeGenerateDbConfig:46 -  Init CodeGenerate Config [ Get Db Config From application.yml ] 
2025-05-29 14:17:02.948 [main] INFO  org.springframework.cloud.commons.util.InetUtils:170 - Cannot determine local hostname
2025-05-29 14:17:03.629 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:168 - Starting ProtocolHandler ["http-nio-8428"]
2025-05-29 14:17:03.668 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8428 (http) with context path '/quzgzpt'
2025-05-29 14:17:06.036 [main] INFO  org.springframework.cloud.commons.util.InetUtils:170 - Cannot determine local hostname
2025-05-29 14:17:08.115 [main] INFO  com.jky.boot.JkyCdtpublicApplication:61 - Started JkyCdtpublicApplication in 57.127 seconds (JVM running for 60.058)
2025-05-29 14:17:08.145 [main] INFO  com.jky.boot.JkyCdtpublicApplication:40 - 
----------------------------------------------------------
	Cdtpublic JKY Boot is running! Access URLs:
	Local: 		http://localhost:8428/quzgzpt/
	External: 	http://**************:8428/quzgzpt/
	Swagger文档: 	http://**************:8428/quzgzpt/doc.html
----------------------------------------------------------
2025-05-29 14:17:58.813 [http-nio-8428-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/quzgzpt]:168 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-29 14:17:58.813 [http-nio-8428-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-05-29 14:17:58.817 [http-nio-8428-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet:547 - Completed initialization in 4 ms
2025-05-29 14:18:01.690 [http-nio-8428-exec-1] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:18:22.889 [http-nio-8428-exec-4] INFO  o.jeecg.modules.cas.controller.ZlbLoginController:232 - gotoUrl============================&token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NDg1MDMxMDIsInVzZXJuYW1lIjoiemxiTG9naW4ifQ.LHs0XX6Ioes5rQSDgixWStqcPzgODCtTy_C2Qwkzb0I&id=777
2025-05-29 14:18:25.608 [http-nio-8428-exec-1] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:18:25.608 [http-nio-8428-exec-2] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:18:28.345 [http-nio-8428-exec-3] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:19:37.731 [http-nio-8428-exec-4] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:19:40.840 [http-nio-8428-exec-5] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:21:35.606 [http-nio-8428-exec-7] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:22:25.542 [http-nio-8428-exec-8] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:22:45.483 [http-nio-8428-exec-4] INFO  o.j.boot.starter.lock.client.RedissonLockClient:56 - 获取锁成功,lockName=bankReg:apply:fe1d58cd-fa83-4e01-8a6e-d2d588e3421a
2025-05-29 14:22:57.042 [http-nio-8428-exec-5] INFO  o.j.boot.starter.lock.client.RedissonLockClient:56 - 获取锁成功,lockName=bankReg:apply:fe1d58cd-fa83-4e01-8a6e-d2d588e3421a
2025-05-29 14:23:05.713 [http-nio-8428-exec-1] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:23:07.910 [http-nio-8428-exec-7] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:23:10.787 [http-nio-8428-exec-8] INFO  o.j.boot.starter.lock.client.RedissonLockClient:56 - 获取锁成功,lockName=bankReg:apply:fe1d58cd-fa83-4e01-8a6e-d2d588e3421a
2025-05-29 14:25:08.076 [http-nio-8428-exec-5] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:25:20.235 [http-nio-8428-exec-1] INFO  o.j.boot.starter.lock.client.RedissonLockClient:56 - 获取锁成功,lockName=bankReg:apply:1927969260123168770
2025-05-29 14:25:25.394 [http-nio-8428-exec-7] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:25:25.411 [http-nio-8428-exec-7] INFO  org.jeecg.common.aspect.DictAspect:306 - translateManyDict.dictCodes:bank_reg_state,bank_reg_apply_type,bank_reg_apply_status
2025-05-29 14:25:25.412 [http-nio-8428-exec-7] INFO  org.jeecg.common.aspect.DictAspect:307 - translateManyDict.values:10
2025-05-29 14:25:26.620 [http-nio-8428-exec-7] INFO  org.jeecg.common.aspect.DictAspect:309 - translateManyDict.result:{bank_reg_apply_status=[DictModel(value=10, text=学员确认（驾校待审核）)]}
2025-05-29 14:26:23.959 [http-nio-8428-exec-4] INFO  o.j.boot.starter.lock.client.RedissonLockClient:56 - 获取锁成功,lockName=bankReg:apply:fe1d58cd-fa83-4e01-8a6e-d2d588e3421a
2025-05-29 14:26:31.513 [http-nio-8428-exec-5] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:26:43.295 [http-nio-8428-exec-1] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:26:45.100 [http-nio-8428-exec-7] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:27:11.727 [http-nio-8428-exec-8] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:27:15.288 [http-nio-8428-exec-4] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:27:20.255 [http-nio-8428-exec-5] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:27:36.282 [http-nio-8428-exec-1] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:27:46.510 [http-nio-8428-exec-7] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:27:49.325 [http-nio-8428-exec-8] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:27:50.744 [http-nio-8428-exec-4] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:27:57.206 [http-nio-8428-exec-5] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:28:00.455 [http-nio-8428-exec-1] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:28:05.665 [http-nio-8428-exec-7] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:28:36.348 [http-nio-8428-exec-4] INFO  o.j.boot.starter.lock.client.RedissonLockClient:56 - 获取锁成功,lockName=bankReg:apply:fe1d58cd-fa83-4e01-8a6e-d2d588e3421a
2025-05-29 14:28:36.463 [http-nio-8428-exec-5] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:28:39.956 [http-nio-8428-exec-1] INFO  o.j.boot.starter.lock.client.RedissonLockClient:56 - 获取锁成功,lockName=bankReg:apply:1927975342618898433
2025-05-29 14:28:40.136 [http-nio-8428-exec-7] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:37:05.691 [http-nio-8428-exec-4] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:38:42.202 [http-nio-8428-exec-1] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:38:42.207 [http-nio-8428-exec-1] INFO  org.jeecg.common.aspect.DictAspect:306 - translateManyDict.dictCodes:bank_reg_state,bank_reg_apply_type,bank_reg_apply_status
2025-05-29 14:38:42.207 [http-nio-8428-exec-1] INFO  org.jeecg.common.aspect.DictAspect:307 - translateManyDict.values:3
2025-05-29 14:38:42.230 [http-nio-8428-exec-1] INFO  org.jeecg.common.aspect.DictAspect:309 - translateManyDict.result:{bank_reg_apply_status=[DictModel(value=3, text=处理成功)]}
2025-05-29 14:39:14.382 [http-nio-8428-exec-7] INFO  o.j.boot.starter.lock.client.RedissonLockClient:56 - 获取锁成功,lockName=bankReg:apply:fe1d58cd-fa83-4e01-8a6e-d2d588e3421a
2025-05-29 14:39:14.518 [http-nio-8428-exec-8] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 14:39:14.526 [http-nio-8428-exec-8] INFO  org.jeecg.common.aspect.DictAspect:306 - translateManyDict.dictCodes:bank_reg_state,bank_reg_apply_type,bank_reg_apply_status
2025-05-29 14:39:14.526 [http-nio-8428-exec-8] INFO  org.jeecg.common.aspect.DictAspect:307 - translateManyDict.values:2
2025-05-29 14:39:14.532 [http-nio-8428-exec-8] INFO  org.jeecg.common.aspect.DictAspect:309 - translateManyDict.result:{bank_reg_apply_type=[DictModel(value=2, text=解冻)], bank_reg_apply_status=[DictModel(value=2, text=处理中)]}
2025-05-29 14:41:28.331 [http-nio-8428-exec-3] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 16:54:35.885 [http-nio-8428-exec-4] INFO  o.jeecg.modules.system.controller.LoginController:185 -  用户名:  明月,退出成功！ 
2025-05-29 16:54:36.010 [http-nio-8428-exec-3] INFO  o.jeecg.modules.system.controller.LoginController:488 - 获取验证码，Redis checkCode = ERJf，key = *************
2025-05-29 16:54:38.762 [http-nio-8428-exec-2] INFO  o.jeecg.modules.system.controller.LoginController:488 - 获取验证码，Redis checkCode = VCxJ，key = *************
2025-05-29 16:54:45.244 [http-nio-8428-exec-3] INFO  o.j.c.desensitization.aspect.SensitiveDataAspect:76 - 加密操作，Aspect程序耗时：1ms
2025-05-29 16:54:45.309 [http-nio-8428-exec-3] INFO  o.j.c.modules.redis.writer.JeecgRedisCacheWriter:113 - redis remove key:sys:cache:encrypt:user::mingyue
2025-05-29 16:54:45.336 [http-nio-8428-exec-2] INFO  o.j.c.desensitization.aspect.SensitiveDataAspect:76 - 加密操作，Aspect程序耗时：0ms
2025-05-29 16:54:46.343 [http-nio-8428-exec-6] INFO  org.jeecg.config.filter.WebsocketFilter:39 - websocket连接 Token安全校验，Path = /quzgzpt/websocket/1693937562453651458，token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************.KN0PACpmYpX3ra0PDx9mqvnP7OPN1GLMSpP6JJ2re28
2025-05-29 16:54:46.616 [http-nio-8428-exec-6] INFO  org.jeecg.modules.message.websocket.WebSocket:64 - 【websocket消息】有新的连接，总数为:1
2025-05-29 16:55:15.390 [http-nio-8428-exec-5] INFO  o.j.m.system.controller.SysPermissionController:126 - ======获取一级菜单数据=====耗时:29毫秒
2025-05-29 16:55:30.768 [http-nio-8428-exec-3] INFO  org.jeecg.common.aspect.PermissionDataAspect:71 - 拦截请求 >> /sys/jky/user/list ; 请求类型 >> GET . 
2025-05-29 16:55:30.888 [http-nio-8428-exec-3] INFO  c.j.b.s.m.manage.controller.JkySysUserController:176 - com.baomidou.mybatisplus.extension.plugins.pagination.Page@33e6679a
2025-05-29 16:55:30.890 [http-nio-8428-exec-3] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 16:55:35.220 [http-nio-8428-exec-3] INFO  o.j.config.sign.interceptor.SignAuthInterceptor:37 - Sign Interceptor request URI = /quzgzpt/sys/dict/getDictItems/undefined
2025-05-29 16:55:35.228 [http-nio-8428-exec-3] INFO  org.jeecg.config.sign.util.SignUtil:49 - Param paramsJsonStr : {}
2025-05-29 16:55:35.236 [http-nio-8428-exec-3] INFO  org.jeecg.config.sign.util.SignUtil:36 - Param Sign : E19D6243CB1945AB4F7202A1B00F77D5
2025-05-29 16:55:35.261 [http-nio-8428-exec-3] INFO  o.j.modules.system.controller.SysDictController:161 -  dictCode : undefined
2025-05-29 16:55:35.280 [http-nio-8428-exec-2] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 16:55:50.408 [http-nio-8428-exec-4] INFO  o.j.m.system.controller.DuplicateCheckController:50 - ----duplicate check------：DuplicateCheckVo(tableName=sys_user, fieldName=work_no, fieldVal=1122, dataId=1693937562453651458)
2025-05-29 16:55:50.408 [http-nio-8428-exec-8] INFO  o.j.m.system.controller.DuplicateCheckController:50 - ----duplicate check------：DuplicateCheckVo(tableName=sys_user, fieldName=username, fieldVal=mingyue, dataId=1693937562453651458)
2025-05-29 16:55:50.802 [http-nio-8428-exec-5] INFO  o.j.c.desensitization.aspect.SensitiveDataAspect:76 - 加密操作，Aspect程序耗时：0ms
2025-05-29 16:55:50.809 [http-nio-8428-exec-5] INFO  org.jeecg.common.aspect.PermissionDataAspect:71 - 拦截请求 >> /sys/jky/user/list ; 请求类型 >> GET . 
2025-05-29 16:55:50.875 [http-nio-8428-exec-5] INFO  c.j.b.s.m.manage.controller.JkySysUserController:176 - com.baomidou.mybatisplus.extension.plugins.pagination.Page@25944271
2025-05-29 16:55:50.877 [http-nio-8428-exec-5] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 16:55:56.520 [http-nio-8428-exec-10] INFO  o.j.c.modules.redis.writer.JeecgRedisCacheWriter:113 - redis remove key:sys:cache:encrypt:user::mingyue
2025-05-29 16:55:58.958 [http-nio-8428-exec-4] INFO  org.jeecg.modules.message.websocket.WebSocket:74 - 【websocket消息】连接断开，总数为:0
2025-05-29 16:55:59.960 [http-nio-8428-exec-6] INFO  o.j.c.desensitization.aspect.SensitiveDataAspect:76 - 加密操作，Aspect程序耗时：0ms
2025-05-29 16:56:00.647 [http-nio-8428-exec-1] INFO  org.jeecg.config.filter.WebsocketFilter:39 - websocket连接 Token安全校验，Path = /quzgzpt/websocket/1693937562453651458，token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************.KN0PACpmYpX3ra0PDx9mqvnP7OPN1GLMSpP6JJ2re28
2025-05-29 16:56:00.652 [http-nio-8428-exec-1] INFO  org.jeecg.modules.message.websocket.WebSocket:64 - 【websocket消息】有新的连接，总数为:1
2025-05-29 16:56:00.710 [http-nio-8428-exec-10] INFO  org.jeecg.common.aspect.PermissionDataAspect:71 - 拦截请求 >> /sys/jky/user/list ; 请求类型 >> GET . 
2025-05-29 16:56:00.772 [http-nio-8428-exec-10] INFO  c.j.b.s.m.manage.controller.JkySysUserController:176 - com.baomidou.mybatisplus.extension.plugins.pagination.Page@4d5a7d07
2025-05-29 16:56:00.772 [http-nio-8428-exec-10] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 17:00:05.260 [http-nio-8428-exec-9] INFO  org.jeecg.modules.message.websocket.WebSocket:74 - 【websocket消息】连接断开，总数为:0
2025-05-29 17:00:10.433 [http-nio-8428-exec-6] INFO  org.jeecg.config.filter.WebsocketFilter:39 - websocket连接 Token安全校验，Path = /quzgzpt/websocket/1693937562453651458，token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************.KN0PACpmYpX3ra0PDx9mqvnP7OPN1GLMSpP6JJ2re28
2025-05-29 17:00:10.440 [http-nio-8428-exec-6] INFO  org.jeecg.modules.message.websocket.WebSocket:64 - 【websocket消息】有新的连接，总数为:1
2025-05-29 17:00:46.957 [http-nio-8428-exec-6] INFO  o.j.m.system.controller.SysPermissionController:126 - ======获取一级菜单数据=====耗时:13毫秒
2025-05-29 17:01:14.322 [http-nio-8428-exec-6] INFO  org.jeecg.modules.message.websocket.WebSocket:74 - 【websocket消息】连接断开，总数为:0
2025-05-29 17:01:18.614 [http-nio-8428-exec-3] INFO  org.jeecg.config.filter.WebsocketFilter:39 - websocket连接 Token安全校验，Path = /quzgzpt/websocket/1693937562453651458，token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************.KN0PACpmYpX3ra0PDx9mqvnP7OPN1GLMSpP6JJ2re28
2025-05-29 17:01:18.620 [http-nio-8428-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:64 - 【websocket消息】有新的连接，总数为:1
2025-05-29 17:01:18.715 [http-nio-8428-exec-9] INFO  o.j.m.system.controller.SysPermissionController:126 - ======获取一级菜单数据=====耗时:16毫秒
2025-05-29 17:01:33.085 [http-nio-8428-exec-5] INFO  o.j.m.system.controller.SysPermissionController:126 - ======获取一级菜单数据=====耗时:13毫秒
2025-05-29 17:01:41.397 [http-nio-8428-exec-2] INFO  org.jeecg.modules.message.websocket.WebSocket:74 - 【websocket消息】连接断开，总数为:0
2025-05-29 17:01:43.014 [http-nio-8428-exec-7] INFO  org.jeecg.config.filter.WebsocketFilter:39 - websocket连接 Token安全校验，Path = /quzgzpt/websocket/1693937562453651458，token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************.KN0PACpmYpX3ra0PDx9mqvnP7OPN1GLMSpP6JJ2re28
2025-05-29 17:01:43.019 [http-nio-8428-exec-7] INFO  org.jeecg.modules.message.websocket.WebSocket:64 - 【websocket消息】有新的连接，总数为:1
2025-05-29 17:01:43.081 [http-nio-8428-exec-10] INFO  o.j.m.system.controller.SysPermissionController:126 - ======获取一级菜单数据=====耗时:17毫秒
2025-05-29 17:02:51.643 [http-nio-8428-exec-9] INFO  o.j.m.system.controller.SysPermissionController:518 - ======角色授权成功=====耗时:66毫秒
2025-05-29 17:02:53.308 [http-nio-8428-exec-5] INFO  org.jeecg.common.aspect.PermissionDataAspect:71 - 拦截请求 >> /sys/jky/user/list ; 请求类型 >> GET . 
2025-05-29 17:02:53.367 [http-nio-8428-exec-5] INFO  c.j.b.s.m.manage.controller.JkySysUserController:176 - com.baomidou.mybatisplus.extension.plugins.pagination.Page@39ca41b5
2025-05-29 17:02:53.367 [http-nio-8428-exec-5] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 17:02:58.635 [http-nio-8428-exec-5] INFO  o.j.config.sign.interceptor.SignAuthInterceptor:37 - Sign Interceptor request URI = /quzgzpt/sys/dict/getDictItems/undefined
2025-05-29 17:02:58.635 [http-nio-8428-exec-5] INFO  org.jeecg.config.sign.util.SignUtil:49 - Param paramsJsonStr : {}
2025-05-29 17:02:58.635 [http-nio-8428-exec-5] INFO  org.jeecg.config.sign.util.SignUtil:36 - Param Sign : E19D6243CB1945AB4F7202A1B00F77D5
2025-05-29 17:02:58.636 [http-nio-8428-exec-5] INFO  o.j.modules.system.controller.SysDictController:161 -  dictCode : undefined
2025-05-29 17:02:58.649 [http-nio-8428-exec-2] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 17:03:08.646 [http-nio-8428-exec-8] INFO  org.jeecg.common.aspect.PermissionDataAspect:71 - 拦截请求 >> /sys/jky/user/list ; 请求类型 >> GET . 
2025-05-29 17:03:08.704 [http-nio-8428-exec-8] INFO  c.j.b.s.m.manage.controller.JkySysUserController:176 - com.baomidou.mybatisplus.extension.plugins.pagination.Page@6c838ab5
2025-05-29 17:03:08.704 [http-nio-8428-exec-8] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 17:03:09.900 [http-nio-8428-exec-1] INFO  org.jeecg.common.aspect.PermissionDataAspect:71 - 拦截请求 >> /sys/jky/user/list ; 请求类型 >> GET . 
2025-05-29 17:03:09.955 [http-nio-8428-exec-1] INFO  c.j.b.s.m.manage.controller.JkySysUserController:176 - com.baomidou.mybatisplus.extension.plugins.pagination.Page@7b2482be
2025-05-29 17:03:09.956 [http-nio-8428-exec-1] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 17:04:40.528 [http-nio-8428-exec-1] INFO  org.jeecg.modules.message.websocket.WebSocket:74 - 【websocket消息】连接断开，总数为:0
2025-05-29 17:04:45.593 [http-nio-8428-exec-4] INFO  org.jeecg.config.filter.WebsocketFilter:39 - websocket连接 Token安全校验，Path = /quzgzpt/websocket/1693937562453651458，token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************.KN0PACpmYpX3ra0PDx9mqvnP7OPN1GLMSpP6JJ2re28
2025-05-29 17:04:45.646 [http-nio-8428-exec-4] INFO  org.jeecg.modules.message.websocket.WebSocket:64 - 【websocket消息】有新的连接，总数为:1
2025-05-29 17:04:45.762 [http-nio-8428-exec-8] INFO  org.jeecg.common.aspect.PermissionDataAspect:71 - 拦截请求 >> /sys/jky/user/list ; 请求类型 >> GET . 
2025-05-29 17:04:45.835 [http-nio-8428-exec-8] INFO  c.j.b.s.m.manage.controller.JkySysUserController:176 - com.baomidou.mybatisplus.extension.plugins.pagination.Page@c49a351
2025-05-29 17:04:45.836 [http-nio-8428-exec-8] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 17:05:31.977 [http-nio-8428-exec-9] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:833 - -------通过数据库读取用户拥有的角色Rules------username： mingyue,Roles size: 1
2025-05-29 17:05:32.042 [http-nio-8428-exec-9] INFO  o.jeecg.modules.system.service.impl.SysBaseApiImpl:855 - -------通过数据库读取用户拥有的权限Perms------username： mingyue,Perms size: 263
2025-05-29 17:05:32.043 [http-nio-8428-exec-9] INFO  org.jeecg.config.shiro.ShiroRealm:79 - ===============Shiro权限认证成功==============
2025-05-29 17:05:32.092 [http-nio-8428-exec-9] ERROR c.j.b.c.exception.JkyCdtpublicExceptionHandler:136 - Subject does not have permission [gzpt:bankRegApply:list]
org.apache.shiro.authz.UnauthorizedException: Subject does not have permission [gzpt:bankRegApply:list]
	at org.apache.shiro.authz.ModularRealmAuthorizer.checkPermission(ModularRealmAuthorizer.java:323)
	at org.apache.shiro.mgt.AuthorizingSecurityManager.checkPermission(AuthorizingSecurityManager.java:137)
	at org.apache.shiro.subject.support.DelegatingSubject.checkPermission(DelegatingSubject.java:209)
	at org.apache.shiro.authz.aop.PermissionAnnotationHandler.assertAuthorized(PermissionAnnotationHandler.java:74)
	at org.apache.shiro.authz.aop.AuthorizingAnnotationMethodInterceptor.assertAuthorized(AuthorizingAnnotationMethodInterceptor.java:84)
	at org.apache.shiro.authz.aop.AnnotationsAuthorizingMethodInterceptor.assertAuthorized(AnnotationsAuthorizingMethodInterceptor.java:100)
	at org.apache.shiro.authz.aop.AuthorizingMethodInterceptor.invoke(AuthorizingMethodInterceptor.java:38)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor.invoke(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:115)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.jky.boot.core.module.gzpt.controller.BankRegApplyController$$EnhancerBySpringCGLIB$$f3632b72.queryPageList(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.apache.shiro.authz.AuthorizationException: Not authorized to invoke method: public org.jeecg.common.api.vo.Result com.jky.boot.core.module.gzpt.controller.BankRegApplyController.queryPageList(com.jky.boot.core.module.gzpt.entity.BankRegApply,java.lang.Integer,java.lang.Integer,javax.servlet.http.HttpServletRequest)
	at org.apache.shiro.authz.aop.AuthorizingAnnotationMethodInterceptor.assertAuthorized(AuthorizingAnnotationMethodInterceptor.java:90)
	... 93 common frames omitted
2025-05-29 17:05:34.793 [http-nio-8428-exec-2] ERROR c.j.b.c.exception.JkyCdtpublicExceptionHandler:136 - Subject does not have permission [gzpt:bankRegApply:list]
org.apache.shiro.authz.UnauthorizedException: Subject does not have permission [gzpt:bankRegApply:list]
	at org.apache.shiro.authz.ModularRealmAuthorizer.checkPermission(ModularRealmAuthorizer.java:323)
	at org.apache.shiro.mgt.AuthorizingSecurityManager.checkPermission(AuthorizingSecurityManager.java:137)
	at org.apache.shiro.subject.support.DelegatingSubject.checkPermission(DelegatingSubject.java:209)
	at org.apache.shiro.authz.aop.PermissionAnnotationHandler.assertAuthorized(PermissionAnnotationHandler.java:74)
	at org.apache.shiro.authz.aop.AuthorizingAnnotationMethodInterceptor.assertAuthorized(AuthorizingAnnotationMethodInterceptor.java:84)
	at org.apache.shiro.authz.aop.AnnotationsAuthorizingMethodInterceptor.assertAuthorized(AnnotationsAuthorizingMethodInterceptor.java:100)
	at org.apache.shiro.authz.aop.AuthorizingMethodInterceptor.invoke(AuthorizingMethodInterceptor.java:38)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor.invoke(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:115)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.jky.boot.core.module.gzpt.controller.BankRegApplyController$$EnhancerBySpringCGLIB$$f3632b72.queryPageList(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.apache.shiro.authz.AuthorizationException: Not authorized to invoke method: public org.jeecg.common.api.vo.Result com.jky.boot.core.module.gzpt.controller.BankRegApplyController.queryPageList(com.jky.boot.core.module.gzpt.entity.BankRegApply,java.lang.Integer,java.lang.Integer,javax.servlet.http.HttpServletRequest)
	at org.apache.shiro.authz.aop.AuthorizingAnnotationMethodInterceptor.assertAuthorized(AuthorizingAnnotationMethodInterceptor.java:90)
	... 93 common frames omitted
2025-05-29 17:05:37.942 [http-nio-8428-exec-1] ERROR c.j.b.c.exception.JkyCdtpublicExceptionHandler:136 - Subject does not have permission [gzpt:bankRegApply:list]
org.apache.shiro.authz.UnauthorizedException: Subject does not have permission [gzpt:bankRegApply:list]
	at org.apache.shiro.authz.ModularRealmAuthorizer.checkPermission(ModularRealmAuthorizer.java:323)
	at org.apache.shiro.mgt.AuthorizingSecurityManager.checkPermission(AuthorizingSecurityManager.java:137)
	at org.apache.shiro.subject.support.DelegatingSubject.checkPermission(DelegatingSubject.java:209)
	at org.apache.shiro.authz.aop.PermissionAnnotationHandler.assertAuthorized(PermissionAnnotationHandler.java:74)
	at org.apache.shiro.authz.aop.AuthorizingAnnotationMethodInterceptor.assertAuthorized(AuthorizingAnnotationMethodInterceptor.java:84)
	at org.apache.shiro.authz.aop.AnnotationsAuthorizingMethodInterceptor.assertAuthorized(AnnotationsAuthorizingMethodInterceptor.java:100)
	at org.apache.shiro.authz.aop.AuthorizingMethodInterceptor.invoke(AuthorizingMethodInterceptor.java:38)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor.invoke(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:115)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.jky.boot.core.module.gzpt.controller.BankRegApplyController$$EnhancerBySpringCGLIB$$f3632b72.queryPageList(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.apache.shiro.authz.AuthorizationException: Not authorized to invoke method: public org.jeecg.common.api.vo.Result com.jky.boot.core.module.gzpt.controller.BankRegApplyController.queryPageList(com.jky.boot.core.module.gzpt.entity.BankRegApply,java.lang.Integer,java.lang.Integer,javax.servlet.http.HttpServletRequest)
	at org.apache.shiro.authz.aop.AuthorizingAnnotationMethodInterceptor.assertAuthorized(AuthorizingAnnotationMethodInterceptor.java:90)
	... 93 common frames omitted
2025-05-29 17:05:38.829 [http-nio-8428-exec-10] INFO  org.jeecg.modules.message.websocket.WebSocket:74 - 【websocket消息】连接断开，总数为:0
2025-05-29 17:05:43.924 [http-nio-8428-exec-8] INFO  org.jeecg.config.filter.WebsocketFilter:39 - websocket连接 Token安全校验，Path = /quzgzpt/websocket/1693937562453651458，token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************.KN0PACpmYpX3ra0PDx9mqvnP7OPN1GLMSpP6JJ2re28
2025-05-29 17:05:43.929 [http-nio-8428-exec-8] INFO  org.jeecg.modules.message.websocket.WebSocket:64 - 【websocket消息】有新的连接，总数为:1
2025-05-29 17:05:43.991 [http-nio-8428-exec-6] ERROR c.j.b.c.exception.JkyCdtpublicExceptionHandler:136 - Subject does not have permission [gzpt:bankRegApply:list]
org.apache.shiro.authz.UnauthorizedException: Subject does not have permission [gzpt:bankRegApply:list]
	at org.apache.shiro.authz.ModularRealmAuthorizer.checkPermission(ModularRealmAuthorizer.java:323)
	at org.apache.shiro.mgt.AuthorizingSecurityManager.checkPermission(AuthorizingSecurityManager.java:137)
	at org.apache.shiro.subject.support.DelegatingSubject.checkPermission(DelegatingSubject.java:209)
	at org.apache.shiro.authz.aop.PermissionAnnotationHandler.assertAuthorized(PermissionAnnotationHandler.java:74)
	at org.apache.shiro.authz.aop.AuthorizingAnnotationMethodInterceptor.assertAuthorized(AuthorizingAnnotationMethodInterceptor.java:84)
	at org.apache.shiro.authz.aop.AnnotationsAuthorizingMethodInterceptor.assertAuthorized(AnnotationsAuthorizingMethodInterceptor.java:100)
	at org.apache.shiro.authz.aop.AuthorizingMethodInterceptor.invoke(AuthorizingMethodInterceptor.java:38)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor.invoke(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:115)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.jky.boot.core.module.gzpt.controller.BankRegApplyController$$EnhancerBySpringCGLIB$$f3632b72.queryPageList(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.apache.shiro.authz.AuthorizationException: Not authorized to invoke method: public org.jeecg.common.api.vo.Result com.jky.boot.core.module.gzpt.controller.BankRegApplyController.queryPageList(com.jky.boot.core.module.gzpt.entity.BankRegApply,java.lang.Integer,java.lang.Integer,javax.servlet.http.HttpServletRequest)
	at org.apache.shiro.authz.aop.AuthorizingAnnotationMethodInterceptor.assertAuthorized(AuthorizingAnnotationMethodInterceptor.java:90)
	... 93 common frames omitted
2025-05-29 17:05:55.246 [http-nio-8428-exec-2] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 17:05:55.246 [http-nio-8428-exec-10] INFO  org.jeecg.common.aspect.PermissionDataAspect:71 - 拦截请求 >> /sys/user/list ; 请求类型 >> GET . 
2025-05-29 17:05:55.275 [http-nio-8428-exec-10] INFO  org.jeecg.common.system.query.QueryGenerator:276 - 当前字段有：[birthday, relTenantIds, activitiSync, userIdentity, delFlag, workNo, password, post, updateBy, orgCode, id, email, clientId, salt, sex, departIds, telephone, updateTime, avatar, realname, uniscid, areaCode, createBy, createTime, phone, orgCodeTxt, status, username]
2025-05-29 17:05:55.311 [http-nio-8428-exec-10] INFO  o.j.modules.system.controller.SysUserController:187 - com.baomidou.mybatisplus.extension.plugins.pagination.Page@5d4269be
2025-05-29 17:05:55.317 [http-nio-8428-exec-10] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 17:06:17.074 [http-nio-8428-exec-4] INFO  o.j.c.modules.redis.writer.JeecgRedisCacheWriter:113 - redis remove key:sys:cache:encrypt:user::mingyue
2025-05-29 17:06:19.927 [http-nio-8428-exec-5] INFO  o.j.c.desensitization.aspect.SensitiveDataAspect:76 - 加密操作，Aspect程序耗时：0ms
2025-05-29 17:06:19.935 [http-nio-8428-exec-5] ERROR c.j.b.c.exception.JkyCdtpublicExceptionHandler:136 - Subject does not have permission [gzpt:bankRegApply:list]
org.apache.shiro.authz.UnauthorizedException: Subject does not have permission [gzpt:bankRegApply:list]
	at org.apache.shiro.authz.ModularRealmAuthorizer.checkPermission(ModularRealmAuthorizer.java:323)
	at org.apache.shiro.mgt.AuthorizingSecurityManager.checkPermission(AuthorizingSecurityManager.java:137)
	at org.apache.shiro.subject.support.DelegatingSubject.checkPermission(DelegatingSubject.java:209)
	at org.apache.shiro.authz.aop.PermissionAnnotationHandler.assertAuthorized(PermissionAnnotationHandler.java:74)
	at org.apache.shiro.authz.aop.AuthorizingAnnotationMethodInterceptor.assertAuthorized(AuthorizingAnnotationMethodInterceptor.java:84)
	at org.apache.shiro.authz.aop.AnnotationsAuthorizingMethodInterceptor.assertAuthorized(AnnotationsAuthorizingMethodInterceptor.java:100)
	at org.apache.shiro.authz.aop.AuthorizingMethodInterceptor.invoke(AuthorizingMethodInterceptor.java:38)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor.invoke(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:115)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.jky.boot.core.module.gzpt.controller.BankRegApplyController$$EnhancerBySpringCGLIB$$f3632b72.queryPageList(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.apache.shiro.authz.AuthorizationException: Not authorized to invoke method: public org.jeecg.common.api.vo.Result com.jky.boot.core.module.gzpt.controller.BankRegApplyController.queryPageList(com.jky.boot.core.module.gzpt.entity.BankRegApply,java.lang.Integer,java.lang.Integer,javax.servlet.http.HttpServletRequest)
	at org.apache.shiro.authz.aop.AuthorizingAnnotationMethodInterceptor.assertAuthorized(AuthorizingAnnotationMethodInterceptor.java:90)
	... 93 common frames omitted
2025-05-29 17:06:32.569 [http-nio-8428-exec-9] INFO  org.jeecg.modules.message.websocket.WebSocket:74 - 【websocket消息】连接断开，总数为:0
2025-05-29 17:06:37.426 [http-nio-8428-exec-1] INFO  org.jeecg.config.filter.WebsocketFilter:39 - websocket连接 Token安全校验，Path = /quzgzpt/websocket/1693937562453651458，token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************.KN0PACpmYpX3ra0PDx9mqvnP7OPN1GLMSpP6JJ2re28
2025-05-29 17:06:37.431 [http-nio-8428-exec-1] INFO  org.jeecg.modules.message.websocket.WebSocket:64 - 【websocket消息】有新的连接，总数为:1
2025-05-29 17:06:37.510 [http-nio-8428-exec-3] ERROR c.j.b.c.exception.JkyCdtpublicExceptionHandler:136 - Subject does not have permission [gzpt:bankRegApply:list]
org.apache.shiro.authz.UnauthorizedException: Subject does not have permission [gzpt:bankRegApply:list]
	at org.apache.shiro.authz.ModularRealmAuthorizer.checkPermission(ModularRealmAuthorizer.java:323)
	at org.apache.shiro.mgt.AuthorizingSecurityManager.checkPermission(AuthorizingSecurityManager.java:137)
	at org.apache.shiro.subject.support.DelegatingSubject.checkPermission(DelegatingSubject.java:209)
	at org.apache.shiro.authz.aop.PermissionAnnotationHandler.assertAuthorized(PermissionAnnotationHandler.java:74)
	at org.apache.shiro.authz.aop.AuthorizingAnnotationMethodInterceptor.assertAuthorized(AuthorizingAnnotationMethodInterceptor.java:84)
	at org.apache.shiro.authz.aop.AnnotationsAuthorizingMethodInterceptor.assertAuthorized(AnnotationsAuthorizingMethodInterceptor.java:100)
	at org.apache.shiro.authz.aop.AuthorizingMethodInterceptor.invoke(AuthorizingMethodInterceptor.java:38)
	at org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor.invoke(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:115)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.jky.boot.core.module.gzpt.controller.BankRegApplyController$$EnhancerBySpringCGLIB$$f3632b72.queryPageList(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.apache.shiro.authz.AuthorizationException: Not authorized to invoke method: public org.jeecg.common.api.vo.Result com.jky.boot.core.module.gzpt.controller.BankRegApplyController.queryPageList(com.jky.boot.core.module.gzpt.entity.BankRegApply,java.lang.Integer,java.lang.Integer,javax.servlet.http.HttpServletRequest)
	at org.apache.shiro.authz.aop.AuthorizingAnnotationMethodInterceptor.assertAuthorized(AuthorizingAnnotationMethodInterceptor.java:90)
	... 93 common frames omitted
2025-05-29 17:10:02.704 [Thread-74] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder:108 - [HttpClientBeanHolder] Start destroying common HttpClient
2025-05-29 17:10:02.708 [Thread-74] WARN  com.alibaba.nacos.common.http.HttpClientBeanHolder:114 - [HttpClientBeanHolder] Destruction of the end
2025-05-29 17:10:04.262 [SpringApplicationShutdownHook] INFO  org.jeecg.modules.message.websocket.WebSocket:74 - 【websocket消息】连接断开，总数为:0
2025-05-29 17:10:04.299 [SpringApplicationShutdownHook] INFO  o.s.scheduling.quartz.SchedulerFactoryBean:847 - Shutting down Quartz Scheduler
2025-05-29 17:10:04.299 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler:666 - Scheduler MyScheduler_$_LAPTOP-8C2H06KS1748499412411 shutting down.
2025-05-29 17:10:04.299 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler:585 - Scheduler MyScheduler_$_LAPTOP-8C2H06KS1748499412411 paused.
2025-05-29 17:10:04.301 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler:740 - Scheduler MyScheduler_$_LAPTOP-8C2H06KS1748499412411 shutdown complete.
2025-05-29 17:10:04.371 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:217 - dynamic-datasource start closing ....
2025-05-29 17:10:04.373 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource:2029 - {dataSource-1} closing ...
2025-05-29 17:10:04.379 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource:2101 - {dataSource-1} closed
2025-05-29 17:10:04.380 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:221 - dynamic-datasource all closed success,bye
2025-05-29 17:10:16.153 [background-preinit] INFO  org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-05-29 17:10:16.179 [main] INFO  com.jky.boot.JkyCdtpublicApplication:55 - Starting JkyCdtpublicApplication using Java 1.8.0_452 on LAPTOP-8C2H06KS with PID 27324 (E:\核心\交科科技\驾培\衢州驾培\pcPublicAndZlb\quzPublic\public\jky-jeecg-boot-cdtpublic-parent\jky-jeecg-boot-cdtpublic-web\target\classes started by 52417 in E:\核心\交科科技\驾培\衢州驾培\pcPublicAndZlb)
2025-05-29 17:10:16.179 [main] INFO  com.jky.boot.JkyCdtpublicApplication:638 - The following 1 profile is active: "dev"
2025-05-29 17:10:18.754 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-29 17:10:18.759 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-29 17:10:18.928 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:201 - Finished Spring Data repository scanning in 149 ms. Found 0 Redis repository interfaces.
2025-05-29 17:10:19.379 [main] INFO  o.springframework.cloud.context.scope.GenericScope:283 - BeanFactory id=5478f4db-0ff6-3e0e-9d63-dcd55379166c
2025-05-29 17:10:19.554 [main] INFO  c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor:50 - Post-processing PropertySource instances
2025-05-29 17:10:19.627 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
2025-05-29 17:10:19.629 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource shiro [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-05-29 17:10:19.630 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-05-29 17:10:19.630 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
2025-05-29 17:10:19.630 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-05-29 17:10:19.630 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableMapPropertySourceWrapper
2025-05-29 17:10:19.630 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-05-29 17:10:19.631 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-05-29 17:10:19.631 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-05-29 17:10:19.631 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-05-29 17:10:19.631 [main] INFO  c.u.j.EncryptablePropertySourceConverter:38 - Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-05-29 17:10:19.678 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 17:10:19.680 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 17:10:19.680 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$445/671396159] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 17:10:19.684 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 17:10:19.693 [main] INFO  c.u.j.filter.DefaultLazyPropertyFilter:31 - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-05-29 17:10:19.698 [main] INFO  c.u.j.resolver.DefaultLazyPropertyResolver:31 - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-05-29 17:10:19.699 [main] INFO  c.u.j.detector.DefaultLazyPropertyDetector:30 - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-05-29 17:10:19.704 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 17:10:19.710 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 17:10:20.719 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration' of type [org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 17:10:20.723 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusMetricsExportAutoConfiguration' of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusMetricsExportAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 17:10:20.728 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'management.metrics.export.prometheus-org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusProperties' of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 17:10:20.731 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'prometheusConfig' of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusPropertiesConfigAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 17:10:20.733 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'collectorRegistry' of type [io.prometheus.client.CollectorRegistry] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 17:10:20.734 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration' of type [org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 17:10:20.734 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'micrometerClock' of type [io.micrometer.core.instrument.Clock$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 17:10:20.760 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'prometheusMeterRegistry' of type [io.micrometer.prometheus.PrometheusMeterRegistry] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 17:10:20.764 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'micrometerOptions' of type [io.lettuce.core.metrics.MicrometerOptions] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 17:10:20.765 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'lettuceMetrics' of type [org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration$$Lambda$493/531204642] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 17:10:20.873 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 17:10:20.993 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 17:10:20.999 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jeecgBaseConfig' of type [org.jeecg.config.JeecgBaseConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 17:10:21.001 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'shiroConfig' of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$75b20d86] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 17:10:21.415 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 17:10:21.420 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$8004365e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 17:10:21.428 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 17:10:21.463 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'redisConfig' of type [org.jeecg.common.modules.redis.config.RedisConfig$$EnhancerBySpringCGLIB$$93141db6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 17:10:21.515 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'shiroRealm' of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 17:10:21.594 [main] INFO  org.jeecg.config.shiro.ShiroConfig:226 - ===============(1)创建缓存管理器RedisCacheManager
2025-05-29 17:10:21.596 [main] INFO  org.jeecg.config.shiro.ShiroConfig:244 - ===============(2)创建RedisManager,连接Redis..
2025-05-29 17:10:21.601 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 17:10:21.609 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 17:10:21.632 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 17:10:21.661 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$298ea33d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 17:10:21.665 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-05-29 17:10:22.139 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8428 (http)
2025-05-29 17:10:22.271 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:168 - Initializing ProtocolHandler ["http-nio-8428"]
2025-05-29 17:10:22.275 [main] INFO  org.apache.catalina.core.StandardService:168 - Starting service [Tomcat]
2025-05-29 17:10:22.275 [main] INFO  org.apache.catalina.core.StandardEngine:168 - Starting Servlet engine: [Apache Tomcat/9.0.104]
2025-05-29 17:10:22.316 [main] WARN  org.apache.catalina.webresources.DirResourceSet:168 - Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8428.5919036903906290702] which is part of the web application [/quzgzpt]
2025-05-29 17:10:22.529 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/quzgzpt]:168 - Initializing Spring embedded WebApplicationContext
2025-05-29 17:10:22.529 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:292 - Root WebApplicationContext: initialization completed in 6263 ms
2025-05-29 17:10:22.846 [main] INFO  org.apache.catalina.core.StandardContext:168 - Suspicious URL pattern: [/sys/api/**] in context [/quzgzpt], see sections 12.1 and 12.2 of the Servlet specification
2025-05-29 17:10:23.679 [main] INFO  com.alibaba.druid.pool.DruidDataSource:994 - {dataSource-1,master} inited
2025-05-29 17:10:23.682 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:132 - dynamic-datasource - load a datasource named [master] success
2025-05-29 17:10:23.682 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:237 - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-05-29 17:10:27.579 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector:56 - class com.jky.boot.system.module.manage.entity.JkySysDictData ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-05-29 17:10:29.593 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector:56 - class com.jky.boot.core.module.gzpt.entity.InstitutionBinding ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-05-29 17:10:31.579 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector:56 - class com.jky.boot.zlb.entity.TMCoach ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-05-29 17:10:31.987 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector:56 - class com.jky.boot.zlb.entity.ZzTransferInscode ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-05-29 17:10:33.411 [main] INFO  org.jeecg.common.modules.redis.config.RedisConfig:56 -  --- redis config init --- 
2025-05-29 17:10:35.148 [main] INFO  o.j.b.s.l.c.s.i.StandaloneRedissonConfigStrategyImpl:33 - 初始化Redisson单机配置,连接地址:************:6379
2025-05-29 17:10:35.510 [main] INFO  org.redisson.Version:41 - Redisson 3.16.1
2025-05-29 17:10:36.066 [redisson-netty-4-23] INFO  o.r.connection.pool.MasterPubSubConnectionPool:166 - 1 connections initialized for ************/************:6379
2025-05-29 17:10:36.080 [redisson-netty-4-19] INFO  org.redisson.connection.pool.MasterConnectionPool:166 - 24 connections initialized for ************/************:6379
2025-05-29 17:10:36.196 [main] INFO  o.j.boot.starter.lock.config.RedissonConfiguration:32 - RedissonManager初始化完成,当前连接方式:STANDALONE,连接地址:************:6379
2025-05-29 17:10:48.539 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector:56 - class com.jky.boot.zlb.entity.TDCity ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-05-29 17:10:50.908 [main] INFO  org.quartz.impl.StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-05-29 17:10:50.919 [main] INFO  org.quartz.simpl.SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-05-29 17:10:50.998 [main] INFO  org.quartz.core.SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-05-29 17:10:50.998 [main] INFO  org.quartz.core.QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-05-29 17:10:51.022 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-05-29 17:10:51.031 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-05-29 17:10:51.034 [main] INFO  org.quartz.core.QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId 'LAPTOP-8C2H06KS1748509850910'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-05-29 17:10:51.034 [main] INFO  org.quartz.impl.StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-05-29 17:10:51.034 [main] INFO  org.quartz.impl.StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-05-29 17:10:51.034 [main] INFO  org.quartz.core.QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@724945
2025-05-29 17:10:55.399 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 0 endpoint(s) beneath base path '/actuator'
2025-05-29 17:10:55.686 [main] INFO  org.jeecg.config.init.CodeGenerateDbConfig:46 -  Init CodeGenerate Config [ Get Db Config From application.yml ] 
2025-05-29 17:11:00.663 [main] INFO  org.springframework.cloud.commons.util.InetUtils:170 - Cannot determine local hostname
2025-05-29 17:11:01.260 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:168 - Starting ProtocolHandler ["http-nio-8428"]
2025-05-29 17:11:01.294 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8428 (http) with context path '/quzgzpt'
2025-05-29 17:11:03.408 [main] INFO  org.springframework.cloud.commons.util.InetUtils:170 - Cannot determine local hostname
2025-05-29 17:11:05.404 [main] INFO  com.jky.boot.JkyCdtpublicApplication:61 - Started JkyCdtpublicApplication in 52.136 seconds (JVM running for 54.268)
2025-05-29 17:11:05.432 [main] INFO  com.jky.boot.JkyCdtpublicApplication:40 - 
----------------------------------------------------------
	Cdtpublic JKY Boot is running! Access URLs:
	Local: 		http://localhost:8428/quzgzpt/
	External: 	http://**************:8428/quzgzpt/
	Swagger文档: 	http://**************:8428/quzgzpt/doc.html
----------------------------------------------------------
2025-05-29 17:11:08.989 [http-nio-8428-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/quzgzpt]:168 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-29 17:11:08.991 [http-nio-8428-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-05-29 17:11:08.993 [http-nio-8428-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet:547 - Completed initialization in 2 ms
2025-05-29 17:11:09.210 [http-nio-8428-exec-1] INFO  org.jeecg.config.filter.WebsocketFilter:39 - websocket连接 Token安全校验，Path = /quzgzpt/websocket/1693937562453651458，token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************.KN0PACpmYpX3ra0PDx9mqvnP7OPN1GLMSpP6JJ2re28
2025-05-29 17:11:10.421 [http-nio-8428-exec-1] INFO  org.jeecg.modules.message.websocket.WebSocket:64 - 【websocket消息】有新的连接，总数为:1
2025-05-29 17:11:23.379 [http-nio-8428-exec-7] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 17:12:05.962 [http-nio-8428-exec-5] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 17:14:02.189 [http-nio-8428-exec-3] INFO  org.jeecg.modules.message.websocket.WebSocket:74 - 【websocket消息】连接断开，总数为:0
2025-05-29 17:14:06.486 [http-nio-8428-exec-9] INFO  org.jeecg.config.filter.WebsocketFilter:39 - websocket连接 Token安全校验，Path = /quzgzpt/websocket/1693937562453651458，token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************.KN0PACpmYpX3ra0PDx9mqvnP7OPN1GLMSpP6JJ2re28
2025-05-29 17:14:06.507 [http-nio-8428-exec-9] INFO  org.jeecg.modules.message.websocket.WebSocket:64 - 【websocket消息】有新的连接，总数为:1
2025-05-29 17:14:06.785 [http-nio-8428-exec-7] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 17:16:00.399 [http-nio-8428-exec-8] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 17:17:36.960 [http-nio-8428-exec-1] INFO  org.jeecg.common.aspect.DictAspect:111 -  __ 进入字典翻译切面 DictAspect —— 
2025-05-29 17:18:01.637 [http-nio-8428-exec-7] ERROR org.apache.tomcat.websocket.pojo.PojoEndpointBase:170 - No error handling configured for [org.jeecg.modules.message.websocket.WebSocket] and the following error occurred
java.io.EOFException: null
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.fillReadBuffer(NioEndpoint.java:1344)
	at org.apache.tomcat.util.net.NioEndpoint$NioSocketWrapper.read(NioEndpoint.java:1232)
	at org.apache.tomcat.websocket.server.WsFrameServer.onDataAvailable(WsFrameServer.java:74)
	at org.apache.tomcat.websocket.server.WsFrameServer.doOnDataAvailable(WsFrameServer.java:184)
	at org.apache.tomcat.websocket.server.WsFrameServer.notifyDataAvailable(WsFrameServer.java:164)
	at org.apache.tomcat.websocket.server.WsHttpUpgradeHandler.upgradeDispatch(WsHttpUpgradeHandler.java:152)
	at org.apache.coyote.http11.upgrade.UpgradeProcessorInternal.dispatch(UpgradeProcessorInternal.java:60)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:57)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 17:18:01.639 [http-nio-8428-exec-7] INFO  org.jeecg.modules.message.websocket.WebSocket:74 - 【websocket消息】连接断开，总数为:0
2025-05-29 20:59:34.242 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xc15a2558, L:/192.168.26.197:10907 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 20:59:34.245 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x4b11f075, L:/192.168.26.197:10905 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 20:59:34.246 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x8475e30e, L:/192.168.26.197:10902 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 20:59:34.247 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xd3bf9fc0, L:/192.168.26.197:10911 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 20:59:34.247 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x1c94372d, L:/192.168.26.197:10903 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 20:59:34.248 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x546b2149, L:/192.168.26.197:10906 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 20:59:34.248 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x63aad740, L:/192.168.26.197:10908 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 20:59:34.249 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xa6fc3bc0, L:/192.168.26.197:10910 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 20:59:34.249 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xb4ff01ad, L:/192.168.26.197:10904 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 20:59:34.250 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x52db5ea5, L:/192.168.26.197:10909 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 20:59:34.250 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x9b944af6, L:/192.168.26.197:10913 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 20:59:34.251 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x403424b7, L:/192.168.26.197:10914 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 20:59:34.251 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x5895c693, L:/192.168.26.197:10915 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 20:59:34.252 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xc5abd465, L:/192.168.26.197:10916 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 20:59:34.252 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x2d673c29, L:/192.168.26.197:10917 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 20:59:34.253 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x0c9a6f2d, L:/192.168.26.197:10918 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 20:59:34.253 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x91c19deb, L:/192.168.26.197:10919 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 20:59:34.254 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x4ef2184f, L:/192.168.26.197:10920 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 20:59:34.254 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x59816ca2, L:/192.168.26.197:10921 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 20:59:34.255 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xfcb02d3e, L:/192.168.26.197:10922 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 20:59:34.255 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x592e724d, L:/192.168.26.197:10926 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 20:59:34.255 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xaee3bc76, L:/192.168.26.197:10924 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 20:59:34.255 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x990b57d3, L:/192.168.26.197:10927 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 20:59:34.255 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0x3ab10481, L:/192.168.26.197:10925 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 20:59:34.255 [redisson-timer-6-1] ERROR org.redisson.client.handler.PingConnectionHandler:89 - Unable to send PING command over channel: [id: 0xc6c81798, L:/192.168.26.197:10923 - R:************/************:6379]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://************:6379]
	at org.redisson.client.RedisConnection.lambda$async$1(RedisConnection.java:251)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 20:59:37.737 [http-nio-8428-exec-4] INFO  org.jeecg.config.filter.WebsocketFilter:39 - websocket连接 Token安全校验，Path = /quzgzpt/websocket/1693937562453651458，token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************.KN0PACpmYpX3ra0PDx9mqvnP7OPN1GLMSpP6JJ2re28
2025-05-29 20:59:41.194 [lettuce-nioEventLoop-7-2] INFO  io.lettuce.core.protocol.CommandHandler:217 - null Unexpected exception during request: java.io.IOException: 远程主机强迫关闭了一个现有的连接。
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:254)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 20:59:41.316 [lettuce-eventExecutorLoop-1-28] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was /************:6379
2025-05-29 20:59:41.316 [lettuce-eventExecutorLoop-1-29] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was /************:6379
2025-05-29 20:59:41.353 [lettuce-nioEventLoop-7-3] WARN  io.lettuce.core.protocol.ConnectionWatchdog:105 - Cannot reconnect to [************:6379]: No route to host: no further information: /************:6379
io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:6379
Caused by: java.net.NoRouteToHostException: No route to host: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:337)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:776)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 20:59:41.353 [lettuce-nioEventLoop-7-4] WARN  io.lettuce.core.protocol.ConnectionWatchdog:105 - Cannot reconnect to [************:6379]: No route to host: no further information: /************:6379
io.netty.channel.AbstractChannel$AnnotatedNoRouteToHostException: No route to host: no further information: /************:6379
Caused by: java.net.NoRouteToHostException: No route to host: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:716)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:337)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:776)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 20:59:54.093 [lettuce-eventExecutorLoop-1-18] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 20:59:54.093 [lettuce-eventExecutorLoop-1-17] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:00:04.111 [lettuce-nioEventLoop-7-23] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:00:04.111 [lettuce-nioEventLoop-7-24] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:00:06.187 [lettuce-eventExecutorLoop-1-19] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:00:06.187 [lettuce-eventExecutorLoop-1-20] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:00:16.203 [lettuce-nioEventLoop-7-26] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:00:16.203 [lettuce-nioEventLoop-7-25] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:00:20.393 [lettuce-eventExecutorLoop-1-22] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:00:20.393 [lettuce-eventExecutorLoop-1-21] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:00:30.410 [lettuce-nioEventLoop-7-28] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:00:30.410 [lettuce-nioEventLoop-7-27] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:00:38.685 [lettuce-eventExecutorLoop-1-24] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:00:38.685 [lettuce-eventExecutorLoop-1-23] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:00:41.384 [http-nio-8428-exec-4] ERROR org.jeecg.config.filter.WebsocketFilter:44 - websocket连接校验失败，Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 minute(s)，token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************.KN0PACpmYpX3ra0PDx9mqvnP7OPN1GLMSpP6JJ2re28
2025-05-29 21:00:48.694 [lettuce-nioEventLoop-7-29] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:00:48.694 [lettuce-nioEventLoop-7-30] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:01:01.392 [http-nio-8428-exec-5] INFO  org.jeecg.config.filter.WebsocketFilter:39 - websocket连接 Token安全校验，Path = /quzgzpt/websocket/1693937562453651458，token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************.KN0PACpmYpX3ra0PDx9mqvnP7OPN1GLMSpP6JJ2re28
2025-05-29 21:01:05.085 [lettuce-eventExecutorLoop-1-17] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:01:05.085 [lettuce-eventExecutorLoop-1-16] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:01:15.089 [lettuce-nioEventLoop-7-31] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:01:15.089 [lettuce-nioEventLoop-7-32] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:01:45.188 [lettuce-eventExecutorLoop-1-24] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:01:45.188 [lettuce-eventExecutorLoop-1-25] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:01:55.193 [lettuce-nioEventLoop-7-2] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:01:55.193 [lettuce-nioEventLoop-7-1] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:02:25.285 [lettuce-eventExecutorLoop-1-29] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:02:25.285 [lettuce-eventExecutorLoop-1-30] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:02:35.299 [lettuce-nioEventLoop-7-4] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:02:35.299 [lettuce-nioEventLoop-7-3] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:03:01.410 [http-nio-8428-exec-5] ERROR org.jeecg.config.filter.WebsocketFilter:44 - websocket连接校验失败，Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 minute(s)，token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************.KN0PACpmYpX3ra0PDx9mqvnP7OPN1GLMSpP6JJ2re28
2025-05-29 21:03:05.394 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:03:05.394 [lettuce-eventExecutorLoop-1-2] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:03:15.400 [lettuce-nioEventLoop-7-5] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:03:15.400 [lettuce-nioEventLoop-7-6] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:03:21.430 [http-nio-8428-exec-8] INFO  org.jeecg.config.filter.WebsocketFilter:39 - websocket连接 Token安全校验，Path = /quzgzpt/websocket/1693937562453651458，token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************.KN0PACpmYpX3ra0PDx9mqvnP7OPN1GLMSpP6JJ2re28
2025-05-29 21:03:45.484 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:03:45.484 [lettuce-eventExecutorLoop-1-6] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:03:55.499 [lettuce-nioEventLoop-7-7] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:03:55.515 [lettuce-nioEventLoop-7-8] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:04:25.585 [lettuce-eventExecutorLoop-1-12] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:04:25.585 [lettuce-eventExecutorLoop-1-11] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:04:35.599 [lettuce-nioEventLoop-7-10] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:04:35.599 [lettuce-nioEventLoop-7-9] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:05:05.685 [lettuce-eventExecutorLoop-1-16] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:05:05.685 [lettuce-eventExecutorLoop-1-15] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:05:15.698 [lettuce-nioEventLoop-7-12] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:05:15.698 [lettuce-nioEventLoop-7-11] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:05:21.451 [http-nio-8428-exec-8] ERROR org.jeecg.config.filter.WebsocketFilter:44 - websocket连接校验失败，Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 minute(s)，token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************.KN0PACpmYpX3ra0PDx9mqvnP7OPN1GLMSpP6JJ2re28
2025-05-29 21:05:41.657 [http-nio-8428-exec-10] INFO  org.jeecg.config.filter.WebsocketFilter:39 - websocket连接 Token安全校验，Path = /quzgzpt/websocket/1693937562453651458，token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************.KN0PACpmYpX3ra0PDx9mqvnP7OPN1GLMSpP6JJ2re28
2025-05-29 21:05:45.788 [lettuce-eventExecutorLoop-1-20] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:05:45.788 [lettuce-eventExecutorLoop-1-21] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:05:55.792 [lettuce-nioEventLoop-7-14] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:05:55.792 [lettuce-nioEventLoop-7-13] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:06:25.889 [lettuce-eventExecutorLoop-1-25] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:06:25.889 [lettuce-eventExecutorLoop-1-24] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:06:35.904 [lettuce-nioEventLoop-7-16] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:06:35.904 [lettuce-nioEventLoop-7-15] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:07:05.985 [lettuce-eventExecutorLoop-1-29] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:07:05.985 [lettuce-eventExecutorLoop-1-30] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:07:15.988 [lettuce-nioEventLoop-7-18] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:07:15.988 [lettuce-nioEventLoop-7-17] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:07:41.679 [http-nio-8428-exec-10] ERROR org.jeecg.config.filter.WebsocketFilter:44 - websocket连接校验失败，Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 minute(s)，token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************.KN0PACpmYpX3ra0PDx9mqvnP7OPN1GLMSpP6JJ2re28
2025-05-29 21:07:46.084 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:07:46.084 [lettuce-eventExecutorLoop-1-2] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:07:56.088 [lettuce-nioEventLoop-7-19] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:07:56.088 [lettuce-nioEventLoop-7-20] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:08:02.661 [http-nio-8428-exec-1] INFO  org.jeecg.config.filter.WebsocketFilter:39 - websocket连接 Token安全校验，Path = /quzgzpt/websocket/1693937562453651458，token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************.KN0PACpmYpX3ra0PDx9mqvnP7OPN1GLMSpP6JJ2re28
2025-05-29 21:08:26.185 [lettuce-eventExecutorLoop-1-6] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:08:26.185 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:08:36.188 [lettuce-nioEventLoop-7-21] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:08:36.188 [lettuce-nioEventLoop-7-22] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:09:06.285 [lettuce-eventExecutorLoop-1-12] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:09:06.285 [lettuce-eventExecutorLoop-1-11] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:09:16.296 [lettuce-nioEventLoop-7-23] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:09:16.296 [lettuce-nioEventLoop-7-24] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:09:46.385 [lettuce-eventExecutorLoop-1-15] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:09:46.385 [lettuce-eventExecutorLoop-1-16] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:09:56.386 [lettuce-nioEventLoop-7-25] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:09:56.418 [lettuce-nioEventLoop-7-26] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:10:02.683 [http-nio-8428-exec-1] ERROR org.jeecg.config.filter.WebsocketFilter:44 - websocket连接校验失败，Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 minute(s)，token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************.KN0PACpmYpX3ra0PDx9mqvnP7OPN1GLMSpP6JJ2re28
2025-05-29 21:10:26.485 [lettuce-eventExecutorLoop-1-20] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:10:26.485 [lettuce-eventExecutorLoop-1-19] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:10:36.498 [lettuce-nioEventLoop-7-27] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:10:36.498 [lettuce-nioEventLoop-7-28] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:11:06.592 [lettuce-eventExecutorLoop-1-23] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:11:06.592 [lettuce-eventExecutorLoop-1-24] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:11:16.608 [lettuce-nioEventLoop-7-29] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:11:16.608 [lettuce-nioEventLoop-7-30] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:11:46.685 [lettuce-eventExecutorLoop-1-28] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:11:46.685 [lettuce-eventExecutorLoop-1-27] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:11:56.701 [lettuce-nioEventLoop-7-32] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:11:56.701 [lettuce-nioEventLoop-7-31] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:12:26.784 [lettuce-eventExecutorLoop-1-31] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:12:26.784 [lettuce-eventExecutorLoop-1-32] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:12:36.796 [lettuce-nioEventLoop-7-2] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:12:36.796 [lettuce-nioEventLoop-7-1] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:13:06.885 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:13:06.885 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:13:16.893 [lettuce-nioEventLoop-7-3] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:13:16.893 [lettuce-nioEventLoop-7-4] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:13:46.984 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:13:46.984 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:13:56.995 [lettuce-nioEventLoop-7-5] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:13:56.995 [lettuce-nioEventLoop-7-6] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:14:27.084 [lettuce-eventExecutorLoop-1-12] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:14:27.084 [lettuce-eventExecutorLoop-1-11] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:14:37.092 [lettuce-nioEventLoop-7-8] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:14:37.092 [lettuce-nioEventLoop-7-7] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:15:07.188 [lettuce-eventExecutorLoop-1-15] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:15:07.188 [lettuce-eventExecutorLoop-1-16] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:15:17.203 [lettuce-nioEventLoop-7-9] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:15:17.203 [lettuce-nioEventLoop-7-10] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:15:47.285 [lettuce-eventExecutorLoop-1-19] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:15:47.285 [lettuce-eventExecutorLoop-1-20] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:15:57.296 [lettuce-nioEventLoop-7-12] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:15:57.296 [lettuce-nioEventLoop-7-11] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:16:27.384 [lettuce-eventExecutorLoop-1-23] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:16:27.384 [lettuce-eventExecutorLoop-1-24] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:16:37.389 [lettuce-nioEventLoop-7-13] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:16:37.389 [lettuce-nioEventLoop-7-14] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:17:07.486 [lettuce-eventExecutorLoop-1-28] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:17:07.486 [lettuce-eventExecutorLoop-1-27] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:17:17.491 [lettuce-nioEventLoop-7-15] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:17:17.491 [lettuce-nioEventLoop-7-16] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:17:47.588 [lettuce-eventExecutorLoop-1-31] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:17:47.588 [lettuce-eventExecutorLoop-1-32] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:17:57.599 [lettuce-nioEventLoop-7-17] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:17:57.599 [lettuce-nioEventLoop-7-18] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:18:27.684 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:18:27.684 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:18:37.695 [lettuce-nioEventLoop-7-19] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:18:37.695 [lettuce-nioEventLoop-7-20] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:19:07.786 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:19:07.786 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:19:17.790 [lettuce-nioEventLoop-7-22] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:19:17.790 [lettuce-nioEventLoop-7-21] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:19:47.889 [lettuce-eventExecutorLoop-1-12] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:19:47.889 [lettuce-eventExecutorLoop-1-11] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:19:57.893 [lettuce-nioEventLoop-7-23] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:19:57.893 [lettuce-nioEventLoop-7-24] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:20:27.984 [lettuce-eventExecutorLoop-1-15] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:20:27.984 [lettuce-eventExecutorLoop-1-16] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:20:37.997 [lettuce-nioEventLoop-7-26] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:20:37.997 [lettuce-nioEventLoop-7-25] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:21:08.085 [lettuce-eventExecutorLoop-1-19] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:21:08.085 [lettuce-eventExecutorLoop-1-20] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:21:18.088 [lettuce-nioEventLoop-7-27] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:21:18.088 [lettuce-nioEventLoop-7-28] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:21:48.185 [lettuce-eventExecutorLoop-1-24] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:21:48.185 [lettuce-eventExecutorLoop-1-23] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:21:58.200 [lettuce-nioEventLoop-7-30] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:21:58.200 [lettuce-nioEventLoop-7-29] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:22:28.284 [lettuce-eventExecutorLoop-1-27] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:22:28.284 [lettuce-eventExecutorLoop-1-28] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:22:38.295 [lettuce-nioEventLoop-7-32] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:22:38.295 [lettuce-nioEventLoop-7-31] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:23:08.385 [lettuce-eventExecutorLoop-1-31] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:23:08.385 [lettuce-eventExecutorLoop-1-32] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:23:18.394 [lettuce-nioEventLoop-7-2] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:23:18.394 [lettuce-nioEventLoop-7-1] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:23:48.484 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:23:48.484 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:23:58.499 [lettuce-nioEventLoop-7-3] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:23:58.499 [lettuce-nioEventLoop-7-4] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:24:28.594 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:24:28.594 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:24:38.602 [lettuce-nioEventLoop-7-6] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:24:38.602 [lettuce-nioEventLoop-7-5] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:25:08.691 [lettuce-eventExecutorLoop-1-12] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:25:08.691 [lettuce-eventExecutorLoop-1-11] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:25:18.706 [lettuce-nioEventLoop-7-7] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:25:18.706 [lettuce-nioEventLoop-7-8] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:25:48.788 [lettuce-eventExecutorLoop-1-15] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:25:48.788 [lettuce-eventExecutorLoop-1-16] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:25:58.796 [lettuce-nioEventLoop-7-10] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:25:58.796 [lettuce-nioEventLoop-7-9] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:26:28.884 [lettuce-eventExecutorLoop-1-19] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:26:28.884 [lettuce-eventExecutorLoop-1-20] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:26:38.890 [lettuce-nioEventLoop-7-12] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:26:38.890 [lettuce-nioEventLoop-7-11] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:27:08.986 [lettuce-eventExecutorLoop-1-23] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:27:08.986 [lettuce-eventExecutorLoop-1-24] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:27:18.991 [lettuce-nioEventLoop-7-13] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:27:18.991 [lettuce-nioEventLoop-7-14] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:27:49.085 [lettuce-eventExecutorLoop-1-28] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:27:49.085 [lettuce-eventExecutorLoop-1-27] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:27:59.101 [lettuce-nioEventLoop-7-16] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:27:59.101 [lettuce-nioEventLoop-7-15] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:28:29.185 [lettuce-eventExecutorLoop-1-32] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:28:29.185 [lettuce-eventExecutorLoop-1-31] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:28:39.199 [lettuce-nioEventLoop-7-18] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:28:39.199 [lettuce-nioEventLoop-7-17] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:29:09.288 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:29:09.288 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:29:19.302 [lettuce-nioEventLoop-7-19] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:29:19.302 [lettuce-nioEventLoop-7-20] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:29:49.384 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:29:49.384 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:29:59.395 [lettuce-nioEventLoop-7-22] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:29:59.395 [lettuce-nioEventLoop-7-21] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:30:29.492 [lettuce-eventExecutorLoop-1-11] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:30:29.492 [lettuce-eventExecutorLoop-1-12] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:30:39.501 [lettuce-nioEventLoop-7-23] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:30:39.501 [lettuce-nioEventLoop-7-24] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:31:09.585 [lettuce-eventExecutorLoop-1-15] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:31:09.585 [lettuce-eventExecutorLoop-1-16] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:31:19.599 [lettuce-nioEventLoop-7-26] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:31:19.599 [lettuce-nioEventLoop-7-25] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:31:49.686 [lettuce-eventExecutorLoop-1-19] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:31:49.686 [lettuce-eventExecutorLoop-1-20] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:31:59.692 [lettuce-nioEventLoop-7-27] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:31:59.692 [lettuce-nioEventLoop-7-28] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:32:29.785 [lettuce-eventExecutorLoop-1-23] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:32:29.785 [lettuce-eventExecutorLoop-1-24] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:32:39.787 [lettuce-nioEventLoop-7-30] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:32:39.787 [lettuce-nioEventLoop-7-29] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:33:09.888 [lettuce-eventExecutorLoop-1-28] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:33:09.888 [lettuce-eventExecutorLoop-1-27] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:33:19.898 [lettuce-nioEventLoop-7-31] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:33:19.898 [lettuce-nioEventLoop-7-32] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:33:49.985 [lettuce-eventExecutorLoop-1-31] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:33:49.985 [lettuce-eventExecutorLoop-1-32] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:33:59.988 [lettuce-nioEventLoop-7-1] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:33:59.988 [lettuce-nioEventLoop-7-2] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:34:30.086 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:34:30.086 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:34:40.096 [lettuce-nioEventLoop-7-4] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:34:40.096 [lettuce-nioEventLoop-7-3] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:35:10.191 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:35:10.191 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:35:20.200 [lettuce-nioEventLoop-7-6] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:35:20.200 [lettuce-nioEventLoop-7-5] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:35:50.284 [lettuce-eventExecutorLoop-1-11] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:35:50.284 [lettuce-eventExecutorLoop-1-12] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:36:00.287 [lettuce-nioEventLoop-7-8] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:36:00.287 [lettuce-nioEventLoop-7-7] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:36:30.389 [lettuce-eventExecutorLoop-1-15] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:36:30.389 [lettuce-eventExecutorLoop-1-16] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:36:40.392 [lettuce-nioEventLoop-7-9] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:36:40.392 [lettuce-nioEventLoop-7-10] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:37:10.492 [lettuce-eventExecutorLoop-1-19] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:37:10.492 [lettuce-eventExecutorLoop-1-20] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:37:20.502 [lettuce-nioEventLoop-7-12] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:37:20.502 [lettuce-nioEventLoop-7-11] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:37:50.585 [lettuce-eventExecutorLoop-1-23] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:37:50.585 [lettuce-eventExecutorLoop-1-24] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:38:00.595 [lettuce-nioEventLoop-7-13] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:38:00.595 [lettuce-nioEventLoop-7-14] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:38:30.685 [lettuce-eventExecutorLoop-1-27] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:38:30.685 [lettuce-eventExecutorLoop-1-28] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:38:40.688 [lettuce-nioEventLoop-7-16] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:38:40.688 [lettuce-nioEventLoop-7-15] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:39:10.784 [lettuce-eventExecutorLoop-1-31] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:39:10.784 [lettuce-eventExecutorLoop-1-32] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:39:20.790 [lettuce-nioEventLoop-7-18] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:39:20.790 [lettuce-nioEventLoop-7-17] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:39:50.885 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:39:50.885 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:40:00.895 [lettuce-nioEventLoop-7-20] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:40:00.895 [lettuce-nioEventLoop-7-19] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:40:30.984 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:40:30.984 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:40:41.001 [lettuce-nioEventLoop-7-21] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:40:41.001 [lettuce-nioEventLoop-7-22] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:41:11.087 [lettuce-eventExecutorLoop-1-12] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:41:11.087 [lettuce-eventExecutorLoop-1-11] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:41:21.089 [lettuce-nioEventLoop-7-24] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:41:21.089 [lettuce-nioEventLoop-7-23] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:41:51.187 [lettuce-eventExecutorLoop-1-15] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:41:51.187 [lettuce-eventExecutorLoop-1-16] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:42:01.194 [lettuce-nioEventLoop-7-26] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:42:01.194 [lettuce-nioEventLoop-7-25] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:42:31.285 [lettuce-eventExecutorLoop-1-19] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:42:31.285 [lettuce-eventExecutorLoop-1-20] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:42:41.299 [lettuce-nioEventLoop-7-27] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:42:41.299 [lettuce-nioEventLoop-7-28] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:43:11.385 [lettuce-eventExecutorLoop-1-24] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:43:11.385 [lettuce-eventExecutorLoop-1-23] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:43:21.393 [lettuce-nioEventLoop-7-29] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:43:21.393 [lettuce-nioEventLoop-7-30] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:43:51.486 [lettuce-eventExecutorLoop-1-27] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:43:51.486 [lettuce-eventExecutorLoop-1-28] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:44:01.501 [lettuce-nioEventLoop-7-32] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:44:01.501 [lettuce-nioEventLoop-7-31] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:44:31.585 [lettuce-eventExecutorLoop-1-31] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:44:31.585 [lettuce-eventExecutorLoop-1-32] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:44:41.599 [lettuce-nioEventLoop-7-2] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:44:41.599 [lettuce-nioEventLoop-7-1] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:45:11.686 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:45:11.686 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:45:21.701 [lettuce-nioEventLoop-7-4] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:45:21.701 [lettuce-nioEventLoop-7-3] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:45:51.784 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:45:51.784 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:46:01.793 [lettuce-nioEventLoop-7-5] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:46:01.793 [lettuce-nioEventLoop-7-6] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:46:31.887 [lettuce-eventExecutorLoop-1-11] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:46:31.887 [lettuce-eventExecutorLoop-1-12] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:46:41.898 [lettuce-nioEventLoop-7-7] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:46:41.898 [lettuce-nioEventLoop-7-8] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:47:11.987 [lettuce-eventExecutorLoop-1-15] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:47:11.987 [lettuce-eventExecutorLoop-1-16] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:47:21.993 [lettuce-nioEventLoop-7-10] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:47:21.993 [lettuce-nioEventLoop-7-9] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:47:52.084 [lettuce-eventExecutorLoop-1-19] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:47:52.084 [lettuce-eventExecutorLoop-1-20] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:48:02.101 [lettuce-nioEventLoop-7-12] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:48:02.101 [lettuce-nioEventLoop-7-11] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:48:32.188 [lettuce-eventExecutorLoop-1-23] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:48:32.188 [lettuce-eventExecutorLoop-1-24] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:48:42.202 [lettuce-nioEventLoop-7-13] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:48:42.202 [lettuce-nioEventLoop-7-14] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:49:12.285 [lettuce-eventExecutorLoop-1-28] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:49:12.285 [lettuce-eventExecutorLoop-1-27] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:49:22.292 [lettuce-nioEventLoop-7-16] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:49:22.292 [lettuce-nioEventLoop-7-15] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:49:52.400 [lettuce-eventExecutorLoop-1-31] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:49:52.400 [lettuce-eventExecutorLoop-1-32] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:50:02.412 [lettuce-nioEventLoop-7-17] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:50:02.412 [lettuce-nioEventLoop-7-18] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:50:32.489 [lettuce-eventExecutorLoop-1-4] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:50:32.489 [lettuce-eventExecutorLoop-1-3] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:50:42.498 [lettuce-nioEventLoop-7-19] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:50:42.498 [lettuce-nioEventLoop-7-20] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:51:12.587 [lettuce-eventExecutorLoop-1-8] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:51:12.587 [lettuce-eventExecutorLoop-1-7] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:51:22.596 [lettuce-nioEventLoop-7-21] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:51:22.596 [lettuce-nioEventLoop-7-22] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:51:52.690 [lettuce-eventExecutorLoop-1-11] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:51:52.690 [lettuce-eventExecutorLoop-1-12] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:52:02.694 [lettuce-nioEventLoop-7-23] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:52:02.694 [lettuce-nioEventLoop-7-24] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:52:32.787 [lettuce-eventExecutorLoop-1-15] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:52:32.787 [lettuce-eventExecutorLoop-1-16] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:52:42.795 [lettuce-nioEventLoop-7-25] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:52:42.795 [lettuce-nioEventLoop-7-26] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:53:12.885 [lettuce-eventExecutorLoop-1-20] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:53:12.885 [lettuce-eventExecutorLoop-1-19] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:53:22.889 [lettuce-nioEventLoop-7-28] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:53:22.889 [lettuce-nioEventLoop-7-27] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:53:52.984 [lettuce-eventExecutorLoop-1-23] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:53:52.984 [lettuce-eventExecutorLoop-1-24] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:54:03.001 [lettuce-nioEventLoop-7-30] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:54:03.001 [lettuce-nioEventLoop-7-29] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:54:33.092 [lettuce-eventExecutorLoop-1-28] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:54:33.092 [lettuce-eventExecutorLoop-1-27] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:54:43.096 [lettuce-nioEventLoop-7-31] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:54:43.096 [lettuce-nioEventLoop-7-32] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:55:13.195 [lettuce-eventExecutorLoop-1-1] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:55:13.195 [lettuce-eventExecutorLoop-1-32] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:55:23.201 [lettuce-nioEventLoop-7-2] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:55:23.201 [lettuce-nioEventLoop-7-1] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:55:53.285 [lettuce-eventExecutorLoop-1-6] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:55:53.285 [lettuce-eventExecutorLoop-1-5] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:56:03.290 [lettuce-nioEventLoop-7-4] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:56:03.290 [lettuce-nioEventLoop-7-3] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:56:33.391 [lettuce-eventExecutorLoop-1-11] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:56:33.391 [lettuce-eventExecutorLoop-1-10] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:56:43.401 [lettuce-nioEventLoop-7-6] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:56:43.401 [lettuce-nioEventLoop-7-5] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:57:00.757 [http-nio-8428-exec-4] WARN  org.apache.shiro.authc.AbstractAuthenticator:216 - Authentication failed for token submission [org.jeecg.config.shiro.JwtToken@38ab8f62].  Possible unexpected error? (Typical or expected login exceptions should extend from AuthenticationException).
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 minute(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:277)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1085)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$4(LettuceConnection.java:938)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:673)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$DefaultSingleInvocationSpec.get(LettuceInvoker.java:589)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:79)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:81)
	at org.jeecg.common.modules.redis.writer.JeecgRedisCacheWriter.doCheckLock(JeecgRedisCacheWriter.java:181)
	at org.jeecg.common.modules.redis.writer.JeecgRedisCacheWriter.checkAndPotentiallyWaitUntilUnlocked(JeecgRedisCacheWriter.java:214)
	at org.jeecg.common.modules.redis.writer.JeecgRedisCacheWriter.execute(JeecgRedisCacheWriter.java:192)
	at org.jeecg.common.modules.redis.writer.JeecgRedisCacheWriter.get(JeecgRedisCacheWriter.java:67)
	at org.springframework.data.redis.cache.RedisCache.lookup(RedisCache.java:91)
	at org.springframework.cache.support.AbstractValueAdaptingCache.get(AbstractValueAdaptingCache.java:58)
	at org.springframework.cache.transaction.TransactionAwareCacheDecorator.get(TransactionAwareCacheDecorator.java:80)
	at org.springframework.cache.interceptor.AbstractCacheInvoker.doGet(AbstractCacheInvoker.java:73)
	at org.springframework.cache.interceptor.CacheAspectSupport.findInCaches(CacheAspectSupport.java:570)
	at org.springframework.cache.interceptor.CacheAspectSupport.findCachedItem(CacheAspectSupport.java:535)
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:401)
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:345)
	at org.springframework.cache.interceptor.CacheInterceptor.invoke(CacheInterceptor.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at org.jeecg.modules.system.service.impl.SysUserServiceImpl$$EnhancerBySpringCGLIB$$b9938ce3.getEncodeUserInfo(<generated>)
	at org.jeecg.modules.system.service.impl.SysBaseApiImpl.getUserByName(SysBaseApiImpl.java:113)
	at org.jeecg.modules.system.service.impl.SysBaseApiImpl$$FastClassBySpringCGLIB$$93fd2f03.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703)
	at org.jeecg.modules.system.service.impl.SysBaseApiImpl$$EnhancerBySpringCGLIB$$147abbf9.getUserByName(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234)
	at com.sun.proxy.$Proxy120.getUserByName(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234)
	at com.sun.proxy.$Proxy120.getUserByName(Unknown Source)
	at org.jeecg.common.util.TokenUtils.getLoginUser(TokenUtils.java:121)
	at org.jeecg.config.shiro.ShiroRealm.checkUserTokenIsEffect(ShiroRealm.java:126)
	at org.jeecg.config.shiro.ShiroRealm.doGetAuthenticationInfo(ShiroRealm.java:103)
	at org.apache.shiro.realm.AuthenticatingRealm.getAuthenticationInfo(AuthenticatingRealm.java:571)
	at org.apache.shiro.authc.pam.ModularRealmAuthenticator.doSingleRealmAuthentication(ModularRealmAuthenticator.java:180)
	at org.apache.shiro.authc.pam.ModularRealmAuthenticator.doAuthenticate(ModularRealmAuthenticator.java:273)
	at org.apache.shiro.authc.AbstractAuthenticator.authenticate(AbstractAuthenticator.java:198)
	at org.apache.shiro.mgt.AuthenticatingSecurityManager.authenticate(AuthenticatingSecurityManager.java:106)
	at org.apache.shiro.mgt.DefaultSecurityManager.login(DefaultSecurityManager.java:275)
	at org.apache.shiro.subject.support.DelegatingSubject.login(DelegatingSubject.java:260)
	at org.jeecg.config.shiro.filters.JwtFilter.executeLogin(JwtFilter.java:74)
	at org.jeecg.config.shiro.filters.JwtFilter.isAccessAllowed(JwtFilter.java:50)
	at org.apache.shiro.web.filter.AccessControlFilter.onPreHandle(AccessControlFilter.java:162)
	at org.apache.shiro.web.filter.PathMatchingFilter.isFilterChainContinued(PathMatchingFilter.java:223)
	at org.apache.shiro.web.filter.PathMatchingFilter.preHandle(PathMatchingFilter.java:198)
	at org.jeecg.config.shiro.filters.JwtFilter.preHandle(JwtFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:131)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 minute(s)
	at io.lettuce.core.internal.ExceptionFactory.createTimeoutException(ExceptionFactory.java:59)
	at io.lettuce.core.internal.Futures.awaitOrCancel(Futures.java:246)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1083)
	... 110 common frames omitted
2025-05-29 21:57:13.485 [lettuce-eventExecutorLoop-1-16] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:57:13.485 [lettuce-eventExecutorLoop-1-15] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:57:23.498 [lettuce-nioEventLoop-7-8] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:57:23.498 [lettuce-nioEventLoop-7-7] WARN  io.lettuce.core.protocol.ConnectionWatchdog:151 - Cannot reconnect to [************:6379]: connection timed out: /************:6379
2025-05-29 21:57:48.074 [http-nio-8428-exec-5] WARN  org.apache.shiro.authc.AbstractAuthenticator:216 - Authentication failed for token submission [org.jeecg.config.shiro.JwtToken@3d30b3e3].  Possible unexpected error? (Typical or expected login exceptions should extend from AuthenticationException).
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 minute(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:277)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1085)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$4(LettuceConnection.java:938)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:673)
	at org.springframework.data.redis.connection.lettuce.LettuceInvoker$DefaultSingleInvocationSpec.get(LettuceInvoker.java:589)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:79)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:81)
	at org.jeecg.common.modules.redis.writer.JeecgRedisCacheWriter.doCheckLock(JeecgRedisCacheWriter.java:181)
	at org.jeecg.common.modules.redis.writer.JeecgRedisCacheWriter.checkAndPotentiallyWaitUntilUnlocked(JeecgRedisCacheWriter.java:214)
	at org.jeecg.common.modules.redis.writer.JeecgRedisCacheWriter.execute(JeecgRedisCacheWriter.java:192)
	at org.jeecg.common.modules.redis.writer.JeecgRedisCacheWriter.get(JeecgRedisCacheWriter.java:67)
	at org.springframework.data.redis.cache.RedisCache.lookup(RedisCache.java:91)
	at org.springframework.cache.support.AbstractValueAdaptingCache.get(AbstractValueAdaptingCache.java:58)
	at org.springframework.cache.transaction.TransactionAwareCacheDecorator.get(TransactionAwareCacheDecorator.java:80)
	at org.springframework.cache.interceptor.AbstractCacheInvoker.doGet(AbstractCacheInvoker.java:73)
	at org.springframework.cache.interceptor.CacheAspectSupport.findInCaches(CacheAspectSupport.java:570)
	at org.springframework.cache.interceptor.CacheAspectSupport.findCachedItem(CacheAspectSupport.java:535)
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:401)
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:345)
	at org.springframework.cache.interceptor.CacheInterceptor.invoke(CacheInterceptor.java:64)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at org.jeecg.modules.system.service.impl.SysUserServiceImpl$$EnhancerBySpringCGLIB$$b9938ce3.getEncodeUserInfo(<generated>)
	at org.jeecg.modules.system.service.impl.SysBaseApiImpl.getUserByName(SysBaseApiImpl.java:113)
	at org.jeecg.modules.system.service.impl.SysBaseApiImpl$$FastClassBySpringCGLIB$$93fd2f03.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703)
	at org.jeecg.modules.system.service.impl.SysBaseApiImpl$$EnhancerBySpringCGLIB$$147abbf9.getUserByName(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234)
	at com.sun.proxy.$Proxy120.getUserByName(Unknown Source)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:234)
	at com.sun.proxy.$Proxy120.getUserByName(Unknown Source)
	at org.jeecg.common.util.TokenUtils.getLoginUser(TokenUtils.java:121)
	at org.jeecg.config.shiro.ShiroRealm.checkUserTokenIsEffect(ShiroRealm.java:126)
	at org.jeecg.config.shiro.ShiroRealm.doGetAuthenticationInfo(ShiroRealm.java:103)
	at org.apache.shiro.realm.AuthenticatingRealm.getAuthenticationInfo(AuthenticatingRealm.java:571)
	at org.apache.shiro.authc.pam.ModularRealmAuthenticator.doSingleRealmAuthentication(ModularRealmAuthenticator.java:180)
	at org.apache.shiro.authc.pam.ModularRealmAuthenticator.doAuthenticate(ModularRealmAuthenticator.java:273)
	at org.apache.shiro.authc.AbstractAuthenticator.authenticate(AbstractAuthenticator.java:198)
	at org.apache.shiro.mgt.AuthenticatingSecurityManager.authenticate(AuthenticatingSecurityManager.java:106)
	at org.apache.shiro.mgt.DefaultSecurityManager.login(DefaultSecurityManager.java:275)
	at org.apache.shiro.subject.support.DelegatingSubject.login(DelegatingSubject.java:260)
	at org.jeecg.config.shiro.filters.JwtFilter.executeLogin(JwtFilter.java:74)
	at org.jeecg.config.shiro.filters.JwtFilter.isAccessAllowed(JwtFilter.java:50)
	at org.apache.shiro.web.filter.AccessControlFilter.onPreHandle(AccessControlFilter.java:162)
	at org.apache.shiro.web.filter.PathMatchingFilter.isFilterChainContinued(PathMatchingFilter.java:223)
	at org.apache.shiro.web.filter.PathMatchingFilter.preHandle(PathMatchingFilter.java:198)
	at org.jeecg.config.shiro.filters.JwtFilter.preHandle(JwtFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:131)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:458)
	at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:373)
	at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:370)
	at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:154)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1792)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 minute(s)
	at io.lettuce.core.internal.ExceptionFactory.createTimeoutException(ExceptionFactory.java:59)
	at io.lettuce.core.internal.Futures.awaitOrCancel(Futures.java:246)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1083)
	... 110 common frames omitted
2025-05-29 21:57:53.586 [lettuce-eventExecutorLoop-1-20] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
2025-05-29 21:57:53.586 [lettuce-eventExecutorLoop-1-19] INFO  io.lettuce.core.protocol.ConnectionWatchdog:171 - Reconnecting, last destination was ************:6379
