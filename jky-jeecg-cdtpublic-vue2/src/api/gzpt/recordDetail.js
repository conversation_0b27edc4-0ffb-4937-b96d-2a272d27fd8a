import {axios as request} from '@/utils/request'

// 查询电子教学日志列表
export function listRecordDetail(query) {
  return request({
    url: '/jgpt/recordDetail/list',
    method: 'get',
    params: query
  })
}

// 查询电子教学日志详细
export function getRecordDetail(id) {
  return request({
    url: '/jgpt/recordDetail/' + id,
    method: 'get'
  })
}

// 新增电子教学日志
export function addRecordDetail(data) {
  return request({
    url: '/jgpt/recordDetail',
    method: 'post',
    data: data
  })
}

// 修改电子教学日志
export function updateRecordDetail(data) {
  return request({
    url: '/jgpt/recordDetail',
    method: 'put',
    data: data
  })
}

// 删除电子教学日志
export function delRecordDetail(id) {
  return request({
    url: '/jgpt/recordDetail/' + id,
    method: 'delete'
  })
}

export function getDetailInfo(data) {
  return request({
    url: '/jgpt/recordDetail/getDetailInfo',
    method: 'post',
    data: data
  })
}
/* 获取照片url和教练员信息 */
export function getphotoAndCoach(query) {
  return request({
    url: '/jgpt/recordDetail/getphotoAndCoach',
    method: 'get',
    params: query
  })
}
//审核
export function detailaudit(data) {
  return request({
    url: '/jgpt/recordDetail/audit',
    method: 'post',
    data: data
  })
}

//疑似违规
export function addAbnormalNo(data) {
  return request({
    url: '/jgpt/recordDetail/addAbnormalNo',
    method: 'post',
    data: data
  })
}
//异常组
export function addAbnormal(data) {
  return request({
    url: '/jgpt/recordDetail/addAbnormal',
    method: 'post',
    data: data
  })
}

//提交工单
export function submitWork(data) {
  return request({
    url: '/jgpt/recordDetail/submitWork',
    method: 'post',
    data: data
  })
}

//提交工单
export function workorderBack(data) {
  return request({
    url: '/jgpt/recordDetail/workorderBack',
    method: 'post',
    data: data
  })
}
export function getGps(query) {
  return request({
    url: '/jgpt/recordDetail/getGps',
    method: 'get',
    params: query
  })
}


export function getInfo(query) {
  return request({
    url: '/jgpt/recordDetail/getInfo',
    method: 'get',
    params: query
  })
}


export function exportDate(data) {
  return request({
    url: '/jgpt/recordDetail/export',
    method: 'post',
    data: data
  })
}