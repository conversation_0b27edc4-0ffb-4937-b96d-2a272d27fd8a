<template>
  <a-row type="flex" :gutter="16">
    <a-col :md="5" :sm="24">
      <document-list-left />
    </a-col>
    <a-col :md="24-5" :sm="24">
      <document-list-right :nodeInfo="nodeInfo" />
    </a-col>
  </a-row>
</template>

<script>
  import {
    getUrlKey
  } from '@/utils/util';
  import DocumentListLeft from './modules/DocumentListLeft'
  import DocumentListRight from './modules/DocumentListRight'

  export default {
    name: 'DocumentList',
    components: {
      DocumentListLeft,
      DocumentListRight
    },
    data() {
      return {
        nodeInfo: {},
      }
    },
    methods: {
      treeNodeSelected(nodeDetail) {
        this.nodeInfo = nodeDetail.node.dataRef;
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>
