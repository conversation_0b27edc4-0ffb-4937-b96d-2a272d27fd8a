<template>
  <a-modal
    :visible="visible"
    title="预约详情"
    :footer="null"
    width="1000px"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <j-form-container :disabled="true">
        <a-form-model ref="form" :model="internalModel">
          <!-- 第一行：预约ID 与 类型 -->
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-model-item label="预约ID" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input v-model="internalModel.id" disabled />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="类型" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input
                  :value="internalModel.type === 1 ? '实操' : internalModel.type === 2 ? '课堂' : internalModel.type === 3 ? '模拟' : internalModel.type"
                  disabled
                />
              </a-form-model-item>
            </a-col>
          </a-row>
          <!-- 第二行：班级ID 与 学员编号 -->
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-model-item label="班级ID" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input v-model="internalModel.classId" disabled />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="学员编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input v-model="internalModel.stunum" disabled />
              </a-form-model-item>
            </a-col>
          </a-row>
          <!-- 第三行：学员名称 与 手机号 -->
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-model-item label="学员名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input v-model="internalModel.stuname" disabled />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="手机号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input v-model="internalModel.phone" disabled />
              </a-form-model-item>
            </a-col>
          </a-row>
          <!-- 第四行：推送状态 与 预约状态 -->
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-model-item label="推送状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input :value="internalModel.pushJs === 0 ? '未推送' : '已推送'" disabled />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="预约状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input :value="internalModel.cancel === 0 ? '已预约' : '已取消'" disabled />
              </a-form-model-item>
            </a-col>
          </a-row>
          <!-- 第五行：支付状态 与 创建时间 -->
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-model-item label="支付状态" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input :value="paidObj[internalModel.paid]" disabled />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="创建时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input v-model="internalModel.createTime" disabled />
              </a-form-model-item>
            </a-col>
          </a-row>
          <!-- 第六行：教练编号 与 培训车型 -->
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-model-item label="教练编号" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input v-model="internalModel.coachnum" disabled />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="培训车型" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input v-model="internalModel.traintype" disabled />
              </a-form-model-item>
            </a-col>
          </a-row>
          <!-- 第七行：科目 与 上课日期 -->
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-model-item label="科目" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input
                  :value="internalModel.subject === 1 ? '科目一' : internalModel.subject === 2 ? '科目二' : internalModel.subject === 3 ? '科目三' : internalModel.subject"
                  disabled
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="上课日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input v-model="internalModel.classDate" disabled />
              </a-form-model-item>
            </a-col>
          </a-row>
          <!-- 第八行：上课时间 与 学时 -->
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-model-item label="上课时间" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input v-model="internalModel.classTime" disabled />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="学时" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input v-model="internalModel.classHours" disabled />
              </a-form-model-item>
            </a-col>
          </a-row>
          <!-- 第九行：机构编码 与 机构名称 -->
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-model-item label="机构编码" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input v-model="internalModel.inscode" disabled />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="机构名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input v-model="internalModel.inscode_dictText" disabled />
              </a-form-model-item>
            </a-col>
          </a-row>
          <!-- 第十行：教练名称 -->
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-model-item label="教练名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
                <a-input v-model="internalModel.coachnum_dictText" disabled />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </j-form-container>
    </a-spin>
  </a-modal>
</template>

<script>
export default {
  name: "ScheduleDetailModal",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    detailRecord: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      paidObj:{
        0:"未支付",
        1:"已支付",
        2:"付款失败",
        3:"金额超限",
        9:"取消预约"
      },
      confirmLoading: false,
      labelCol: { span: 6 },
      wrapperCol: { span: 18 },
      internalModel: {
        id: "",
        type: null,
        classId: "",
        stunum: "",
        stuname: "",
        pushJs: null,
        phone: "",
        cancel: null,
        paid: null,
        createTime: "",
        coachnum: "",
        traintype: "",
        inscode: "",
        subject: null,
        classDate: "",
        classTime: "",
        classHours: "",
        inscode_dictText: "",
        coachnum_dictText: ""
      }
    };
  },
  watch: {
    detailRecord: {
      immediate: true,
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          // 对 classTime 进行学时计算
          let hours = "";
          if (newVal.classTime && newVal.classTime.indexOf('-') > -1) {
            const times = newVal.classTime.split('-');
            const startParts = times[0].trim().split(':');
            const endParts = times[1].trim().split(':');
            const startMinutes = parseInt(startParts[0]) * 60 + parseInt(startParts[1]);
            const endMinutes = parseInt(endParts[0]) * 60 + parseInt(endParts[1]);
            hours = Math.round((endMinutes - startMinutes) / 60);
          }
          this.internalModel = {
            id: newVal.id || "",
            type: newVal.type || null,
            classId: newVal.classId || "",
            stunum: newVal.stunum || "",
            stuname: newVal.stuname || "",
            pushJs: newVal.pushJs || 0,
            phone: newVal.phone || "",
            cancel: newVal.cancel || 0,
            paid: newVal.paid || 0,
            createTime: newVal.createTime || "",
            coachnum: newVal.coachnum || "",
            traintype: newVal.traintype || "",
            inscode: newVal.inscode || "",
            subject: newVal.subject || null,
            classDate: newVal.classDate || "",
            classTime: newVal.classTime || "",
            classHours: hours,
            inscode_dictText: newVal.inscode_dictText || "",
            coachnum_dictText: newVal.coachnum_dictText || ""
          };
        }
      }
    }
  },
  methods: {
    handleCancel() {
      this.$emit("update:visible", false);
    }
  }
};
</script>

<style scoped>
/* 如有需要，可添加组件相关样式 */
</style>