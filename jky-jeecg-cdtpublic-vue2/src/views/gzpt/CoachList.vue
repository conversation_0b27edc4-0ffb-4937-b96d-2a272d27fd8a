<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="培训机构">
              <j-tree-select-depart placeholder="请选择培训机构" v-model="queryParam.inscode"> </j-tree-select-depart>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="教练姓名">
              <a-input v-model="queryParam.name" placeholder="请输入教练姓名" />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-input-group compact>
              <a-select v-model="selectQuery">
                <a-select-option value="1">
                  身份证号
                </a-select-option>
                <a-select-option value="2">
                  驾驶证号
                </a-select-option>
              </a-select>
              <a-input style="width: 50%" v-model="inputQuery" />
            </a-input-group>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="手机号">
              <j-input type="list" v-model="queryParam.mobile" dictCode="sys_Serve_status" placeholder="请输入手机号" />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="供职状态">
              <j-dict-select-tag
                type="list"
                v-model="queryParam.employstatus"
                dictCode="sys_Serve_status"
                placeholder="请选择供职状态"
              />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="审核状态">
              <j-dict-select-tag
                type="list"
                v-model="queryParam.auditstate"
                dictCode="sys_general_audit"
                placeholder="请选择审核状态"
              />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="停训状态">
              <j-dict-select-tag
                type="list"
                v-model="queryParam.stopTrain"
                dictCode="coach_stop_train"
                placeholder="请选择停训状态"
              />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="备案状态">
              <j-dict-select-tag
                type="list"
                v-model="queryParam.status"
                dictCode="sys_general_status"
                placeholder="请选择备案状态"
              />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="带教类型">
              <j-dict-select-tag
                type="list"
                v-model="queryParam.teachtype"
                dictCode="sys_teach_type"
                placeholder="请选择带教类型"
              />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="设备厂商">
              <j-dict-select-tag
                type="list"
                v-model="queryParam.platform"
                dictCode="sys_platform"
                placeholder="请选择设备厂商"
              />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
            <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置 </a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus" v-has="'gzpt:coach:add'">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('教练员信息对象')" v-has="'gzpt:coach:export'"
        >导出</a-button
      >
      <!--			<a-button @click="statusVisible = true" type="primary" :disabled="selectedRowKeys.length==1?false:true" v-has="'gzpt:coach:editStatus'">状态修改-->
      <!--			</a-button>-->
      <a-button
        @click="handleTransferPraram"
        type="primary"
        :disabled="selectedRowKeys.length == 1 ? false : true"
        v-has="'gzpt:coach:editTransfer'"
        >转校
      </a-button>
      <a-dropdown :disabled="selectedRowKeys.length == 1 ? false : true" v-has="'gzpt:coach:pushJs'">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="PushQgPt" v-has="'gzpt:coach:pushQg'">
            全国平台
          </a-menu-item>
          <a-menu-item key="2" @click="PushJsPt" v-has="'gzpt:coach:pushJs'">
            计时平台
          </a-menu-item>
        </a-menu>
        <a-button>
          推送
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
      <!--      <a-button @click="contractOpenModal" type="primary" :disabled="selectedRowKeys.length==1?false:true" v-has="'gzpt:coachContract:add'">上传合同-->
      <!--      </a-button>-->
      <a-dropdown :disabled="selectedRowKeys.length == 1 ? false : true">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="contractOpenModal" v-has="'gzpt:coachContract:add'">
            上传合同
          </a-menu-item>
          <a-menu-item key="2" @click="viewContract">
            查看合同
          </a-menu-item>
        </a-menu>
        <a-button>
          教练员合同
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
      <a-button
        @click="handleAccident('0')"
        type="primary"
        :disabled="selectedRowKeys.length == 1 ? false : true"
        v-has="'gzpt:releaseStuCoach:add'"
        >人脸比对取消
      </a-button>
      <a-button
        @click="handleAccident('1')"
        type="primary"
        :disabled="selectedRowKeys.length == 1 ? false : true"
        v-has="'gzpt:releaseStuCoach:add'"
        >定位判断取消
      </a-button>
      <a-button
        @click="handleSyncStatus"
        type="primary"
        :disabled="selectedRowKeys.length !== 1"
        v-has="'gzpt:coach:syncStatus'"
      >
        同步状态
      </a-button>
      <a-button @click="handleSingleSync" type="primary" v-has="'gzpt:coach:syncInfo'">教练员信息同步</a-button>
      <a-button
        @click="handleAbolish"
        type="primary"
        :disabled="selectedRowKeys.length !== 1"
        v-has="'gzpt:coach:abolish'"
        >教练员解除备案</a-button
      >
      <a-dropdown v-if="selectedRowKeys.length > 0" v-has="'gzpt:coach:deleteBatch'">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"> <a-icon type="delete" />删除 </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px">
          批量操作
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择
        <a style="font-weight: 600">{{ selectedRowKeys.length }}</a
        >项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{ x: true }"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        class="j-table-force-nowrap"
        @change="handleTableChange"
      >
        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text, record">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img
            v-else
            :src="getImgView(text)"
            :preview="record.id"
            height="25px"
            alt=""
            style="max-width:80px;font-size: 12px;font-style: italic;"
          />
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)" v-has="'gzpt:coach:edit'">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link"
              >更多
              <a-icon type="down" />
            </a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item v-has="'gzpt:coach:remove'">
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </a-table>
    </div>

    <coach-modal ref="modalForm" @ok="modalFormOk"></coach-modal>
    <a-modal v-model="statusVisible" title="修改状态" @ok="handleupdateEMPLOYSTATUS" :width="800">
      <a-form-model ref="queryTranForm" :model="form" :rules="statusFormvalidatorRules">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="供职状态" prop="employstatus" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-model="form.employstatus"
                dictCode="sys_Serve_status"
                placeholder="请选择供职状态"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="审核状态" prop="auditstate" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-model="form.auditstate"
                dictCode="sys_general_audit"
                placeholder="请选择审核状态"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="停训状态" prop="stopTrain" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-model="form.stopTrain"
                dictCode="coach_stop_train"
                placeholder="请选择停训状态"
              />
            </a-form-model-item>
          </a-col>

          <a-col :span="24">
            <a-form-model-item label="备案状态" prop="status" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-model="form.status"
                dictCode="sys_general_status"
                placeholder="请选择备案状态"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-modal>
    <a-modal v-model="transferVisible" title="转校" @ok="handleTransfer" :width="800">
      <a-form-model ref="transferForm" :model="transferform" :rules="transferFormvalidatorRules">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="教练员" prop="name" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input style="width: 50%" v-model="transferform.name" disabled />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="培训机构" prop="inscode" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-tree-select-depart placeholder="请选择培训机构" v-model="transferform.inscode"> </j-tree-select-depart>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-modal>
    <a-modal v-model="syncOpen" title="教练信息同步" @ok="submitSyncForm" :width="800">
      <a-form-model ref="syncForm" :model="syncForm" :rules="syncFormValidatorRules">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="身份证号" prop="idcard" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-model="syncForm.idcard" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-modal>
    <a-modal v-model="contractVisible" title="上传合同" @ok="contractAdd" :width="800">
      <a-form-model ref="contractForm" :model="contractform" :rules="contractFormvalidatorRules">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="合同" prop="pdfurl" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-upload v-model="contractform.pdfurl" fileTypeList="pdf" :multiple="true" :number="1" fileType="pdf">
              </j-upload>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="签订日期" prop="signingTime" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择签订日期"
                class="query-group-cust"
                v-model="contractform.signingTime"
                style="width:100%"
                dateFormat="YYYY-MM-DD"
              >
              </j-date>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import CoachModal from './modules/CoachModal'
import JTreeSelectDepart from '@/components/common/JTreeSelectDepart.vue'
import { updateCoach, updateTransfer, PushJs, PushQg } from '@/api/gzpt/coach.js'
import { getAction, postAction } from '@/api/manage'
export default {
  name: 'CoachList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    CoachModal,
    JTreeSelectDepart
  },
  data() {
    return {
      queryParam: {
        employstatus: 0,
        auditstate:1,
        stopTrain: 0,
        status: 1,
      },
      syncOpen: false,
      syncForm: {},
      description: '教练员信息对象管理页面',
      form: {},
      transferform: {},
      transferVisible: false,
      statusVisible: false,
      record: [],
      // 表头
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '培训机构',
          align: 'center',
          dataIndex: 'insname'
        },
        {
          title: '姓名',
          align: 'center',
          dataIndex: 'name'
        },
        {
          title: '性别',
          align: 'center',
          dataIndex: 'sex_dictText'
        },
        {
          title: '身份证号',
          align: 'center',
          dataIndex: 'idcard'
        },
        {
          title: '手机号码',
          align: 'center',
          dataIndex: 'mobile'
        },

        {
          title: '准教车型', //A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P
          align: 'center',
          dataIndex: 'teachpermitted'
        },
        {
          title: '设备厂商',
          align: 'center',
          dataIndex: 'platformName'
        },
        {
          title: '供职状态', // 0:在职1:离职
          align: 'center',
          dataIndex: 'employstatus_dictText'
        },
        {
          title: '审核状态',
          align: 'center',
          dataIndex: 'auditstate_dictText'
        },
        {
          title: '停训状态',
          align: 'center',
          dataIndex: 'stopTrain_dictText'
        },
        {
          title: '备案状态',
          align: 'center',
          dataIndex: 'status_dictText'
        },
        {
          title: '带教类型',
          align: 'center',
          dataIndex: 'teachtype_dictText'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 147,
          scopedSlots: {
            customRender: 'action'
          }
        }
      ],
      url: {
        list: '/gzpt/coach/list',
        delete: '/gzpt/coach/delete',
        deleteBatch: '/gzpt/coach/deleteBatch',
        exportXlsUrl: '/gzpt/coach/exportXls',
        importExcelUrl: 'gzpt/coach/importExcel'
      },
      syncFormValidatorRules: {
        idcard: [
          {
            required: true,
            message: '请输入身份证号!'
          }
        ]
      },
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 5
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 16
        }
      },
      statusFormvalidatorRules: {
        auditstate: [
          {
            required: true,
            message: '请输入审核状态'
          }
        ],
        employstatus: [
          {
            required: true,
            message: '请输入供职状态'
          }
        ],
        stopTrain: [
          {
            required: true,
            message: '请输入停训状态'
          }
        ],
        status: [
          {
            required: true,
            message: '请输入备案状态'
          }
        ]
      },
      transferFormvalidatorRules: {
        inscode: [
          {
            required: true,
            message: '请选择培训机构'
          }
        ]
      },
      contractFormvalidatorRules: {
        pdfurl: [
          {
            required: true,
            message: '请上传合同'
          }
        ],
        signingTime: [
          {
            required: true,
            message: '请选择签订日期'
          }
        ]
      },
      selectQuery: '1',
      inputQuery: '',
      queryParams: {
        idcard: '',
        drilicence: ''
      },
      contractVisible: false,
      contractform: {}
    }
  },
  created() {
    this.queryParam.Blacklist = '2'
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    handleAbolish() {
      const id = this.selectedRowKeys[0]
      this.$confirm({
        title: '提示',
        content: '确认进行解除备案操作吗？',
        onOk: () => {
          this.loading = true
          getAction('/gzpt/coach/abolish/' + id).then(response => {
            if (response.success) {
              this.$message.success(response.message)
              this.loading = false
              this.loadData()
            } else {
              this.$message.error(response.message)
              this.loading = false
            }
          })
        }
      })
    },
    handleSingleSync() {
      this.syncOpen = true
    },
    submitSyncForm() {
      this.$refs['syncForm'].validate(valid => {
        this.loading = true
        if (valid) {
          //避免异步问题，暂时把第二个请求嵌在第一个里面
          getAction('/gzpt/coach/syncInfo', this.syncForm)
            .then(response => {
              if (response.success) {
                this.$message.success(response.message)
                this.syncOpen = false
                this.loading = false
                this.loadData()
              } else {
                this.$message.error(response.message)
              }
            })
            .catch(() => {
              this.loading = false
            })
            .finally(() => {
              this.loading = false
            })
        }
      })
    },
    initDictConfig() {},
    loadData(arg) {
      if (!this.url.list) {
        this.$message.error('请设置url.list属性!')
        return
      }
      //加载数据 若传入参数1则加载第一页的内容
      if (arg === 1) {
        this.ipagination.current = 1
      }
      var params = this.getQueryParams() //查询条件
      if (this.selectQuery != '') {
        if (this.selectQuery == 1) {
          params.idcard = this.inputQuery
          params.drilicence = null
        }
        if (this.selectQuery == 2) {
          params.drilicence = this.inputQuery
          params.idcard = null
        }
      }
      this.loading = true
      getAction(this.url.list, params)
        .then(res => {
          if (res.success) {
            //update-begin---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
            this.dataSource = res.result.records || res.result
            if (res.result.total) {
              this.ipagination.total = res.result.total
            } else {
              this.ipagination.total = 0
            }
            //update-end---author:zhangyafei    Date:20201118  for：适配不分页的数据列表------------
          } else {
            this.$message.warning(res.message)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    searchReset() {
      this.queryParam = {}
      this.selectQuery = '1'
      this.inputQuery = ''
      this.loadData(1)
    },
    handleAccident(operateType) {
      this.$confirm({
        title: '提示',
        content: '确认进行豁免操作吗？',
        onOk: () => {
          postAction('/gzpt/releaseStuCoach/add', {
            operateId: this.record[0].coachnum,
            roleType: '1',
            operateType
          }).then(res => {
            if (res.success) {
              this.$message.success('操作成功!')
            } else {
              this.$message.error(res.message)
            }
          })
        }
      })
    },
    handleupdateEMPLOYSTATUS() {
      //	this.form.coachid = this.selectedRowKeys[0]
      postAction('/gzpt/coach/editStatus', {
        ...this.form,
        id: this.selectedRowKeys[0]
      }).then(response => {
        if (response.success) {
          this.$message.success('修改成功')
          this.statusVisible = false
          this.loadData()
        } else {
          this.$message.error(response.message)
        }
        this.$refs.queryTranForm.resetFields()
      })
    },
    handleTransferPraram(row) {
      this.transferVisible = true
      this.transferform.name = this.record.map(item => item.name).join(',')
    },
    //转校
    handleTransfer(row) {
      this.transferform.ids = this.selectedRowKeys.join(',')
      console.log(this.transferform)
      updateTransfer(this.transferform).then(response => {
        if (response.success) {
          this.$message.success('转校操作成功')
          this.transferVisible = false
          this.loadData()
        } else {
          this.$message.error(response.message)
        }
        this.$form.transferForm.resetFields()
      })
    },
    PushJsPt() {
      const coachid = this.selectedRowKeys[0]
      PushJs(coachid).then(response => {
        if (response.code == 200) {
          this.$message.success('推送成功')
          this.loadData()
        } else {
          this.$message.error(response.message)
        }
      })
    },
    PushQgPt() {
      const coachid = this.selectedRowKeys[0]
      PushQg(coachid).then(response => {
        console.log(response)
        if (response.code == 200) {
          this.$message.success('推送成功')
        } else {
          this.$message.error(response.message)
        }
        this.loadData()
      })
    },
    onSelectChange(rowKey, record) {
      this.selectedRowKeys = rowKey
      this.record = record
    },
    //上传合同
    contractOpenModal() {
      this.contractVisible = true
    },
    contractAdd() {
      const that = this
      this.$refs.contractForm.validate(valid => {
        if (valid) {
          // that.confirmLoading = true;
          that.contractform.id = that.selectedRowKeys[0]
          postAction('/gzpt/coach/contractAdd', that.contractform)
            .then(res => {
              if (res.success) {
                that.$message.success(res.message)
                // that.$emit('ok');
                that.loadData()
                that.selectedRowKeys = []
                that.contractVisible == false
                that.contractform = {}
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              // that.confirmLoading = false;
            })
        }
      })
    },
    //查看教练员合同
    viewContract() {
      const id = this.selectedRowKeys[0]
      getAction('/gzpt/coach/viewContract/' + id).then(response => {
        if (response.result) {
          javascipt: window.open(response.result)
        } else {
          this.$message.error('教练员未上传合同,无法查看!')
        }
      })
    },
    // 同步状态
    handleSyncStatus() {
      const id = this.selectedRowKeys[0]
      getAction('/gzpt/coach/syncStatus', {
        id: id
      }).then(res => {
        if (res.success) {
          this.$message.success(res.message)
          this.loadData()
        } else {
          this.$message.warning(res.message)
        }
      })
    }
  }
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>
