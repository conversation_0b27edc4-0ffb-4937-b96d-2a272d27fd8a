<template>
  <a-spin :spinning="confirmLoading">
    <!-- 弹窗内容 -->
    <el-tabs v-model="activeTab">
      <el-tab-pane label="培训机构基本信息" name="basic-info">
        <j-form-container :disabled="formDisabled">
          <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
            <a-row>
              <a-col :span="12">
                <a-form-model-item label="培训机构全称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="name">
                  <a-input v-model="model.name" placeholder="请输入培训机构全称"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item
                  label="区县行政区划代码"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  prop="district"
                >
                  <a-input v-model="model.district" placeholder="请输入区县行政区划代码"></a-input>
                </a-form-model-item>
              </a-col>

              <a-col :span="12">
                <a-form-model-item label="培训机构简称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="shortname">
                  <a-input v-model="model.shortname" placeholder="请输入培训机构简称"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="12" v-if="disabled">
                <a-form-model-item label="培训机构编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="shortname">
                  <a-input v-model="model.inscode" placeholder=""></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="计时厂商" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="shortname">
                  <j-dict-select-tag
                    type="list"
                    v-model="model.platform"
                    dictCode="sys_platform"
                    placeholder="请选择计时厂商"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="经营许可证编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="licnum">
                  <a-input v-model="model.licnum" placeholder="请输入经营许可证编号"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="经营许可日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="licetime">
                  <a-input v-model="model.licetime" placeholder="请输入经营许可日期"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item
                  label="营业执照注册号 "
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  prop="business"
                >
                  <a-input v-model="model.business" placeholder="请输入营业执照注册号"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item
                  label="统一社会信用代码"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  prop="creditcode"
                >
                  <a-input v-model="model.creditcode" placeholder="请输入统一社会信用代码"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="培训机构地址" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="address">
                  <a-textarea v-model="model.address" rows="4" placeholder="请输入培训机构地址" />
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="邮政编码" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="postcode">
                  <a-input v-model="model.postcode" placeholder="请输入邮政编码"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="法人代表" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="legal">
                  <a-input v-model="model.legal" placeholder="请输入法人代表"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="联系人" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="contact">
                  <a-input v-model="model.contact" placeholder="请输入联系人"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="联系电话" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="phone">
                  <a-input v-model="model.phone" placeholder="请输入联系电话"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="经营范围" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="busiscope">
                  <a-input v-model="model.busiscope" placeholder="请输入经营范围"></a-input>
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="经营状态" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="busistatus">
                  <j-dict-select-tag
                    type="list"
                    v-model="model.busistatus"
                    dictCode="sys_school_status"
                    placeholder="请选择经营状态"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="备案状态" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="isBankruptcy">
                  <j-dict-select-tag
                    type="list"
                    v-model="model.isBankruptcy"
                    dictCode="sys_isBankruptcy"
                    placeholder="请选择备案状态"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="分类等级" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="levels">
                  <j-dict-select-tag
                    type="list"
                    v-model="model.levels"
                    dictCode="sys_school_level"
                    placeholder="请选择分类等级"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="教练员总数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="coachnumber">
                  <a-input-number v-model="model.coachnumber" placeholder="请输入教练员总数" style="width: 100%" />
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="考核员总数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="grasupvnum">
                  <a-input-number v-model="model.grasupvnum" placeholder="请输入考核员总数" style="width: 100%" />
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="安全员总数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="safmngnum">
                  <a-input-number v-model="model.safmngnum" placeholder="请输入安全员总数" style="width: 100%" />
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="教练车总数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="tracarnum">
                  <a-input-number v-model="model.tracarnum" placeholder="请输入教练车总数" style="width: 100%" />
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item label="教室总面积" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="classroom">
                  <a-input-number v-model="model.classroom" placeholder="请输入教室总面积 " style="width: 100%" />
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item
                  label="理论教室面积"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  prop="thclassroom"
                >
                  <a-input-number v-model="model.thclassroom" placeholder="请输入理论教室面积" style="width: 100%" />
                </a-form-model-item>
              </a-col>
              <a-col :span="12">
                <a-form-model-item
                  label="教练场总面积"
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  prop="praticefield"
                >
                  <a-input-number v-model="model.praticefield" placeholder="请输入教练场总面积" style="width: 100%" />
                </a-form-model-item>
              </a-col>
            </a-row>
          </a-form-model> </j-form-container
      ></el-tab-pane>
      <el-tab-pane v-if="institutionFormTitle !== '新增'" label="安全生产管理制度" name="safety-policy">
        <div class="safety-production-management">
          <a-row>
            <el-form :model='securitySystemModel' :rules='bdtzAddFormRules' ref='securitySystemModelRef'>
              <el-form-item label="上传扫描盖章版管理制度" prop='uploadFile'>
                <j-upload style='display: -webkit-box' v-model="securitySystemModel.uploadFile"  fileTypeList="pdf,png,jpg,jpeg,image" :number="1"></j-upload>
              </el-form-item>
            </el-form>
              <a-form-model-item label="查看盖章版管理制度">
                <div>
                  <a v-if='attachmentModel.securitySystem' @click='viewFile(attachmentModel.securitySystem)' >点击查看</a>
                  <span v-show='!attachmentModel.securitySystem'>无</span>
                </div>
              </a-form-model-item>
          </a-row>
          <a-col  style="text-align: center;">
            <a-button type="primary" @click="saveAttachment('securitySystem',securitySystemModel)">保存</a-button>
          </a-col>
        </div>



      </el-tab-pane>
      <el-tab-pane v-if="institutionFormTitle !== '新增'" label="安全生产管理机构" name="safety-structure">

            <div class="safety-production-management">
              <a-row>

                <el-form :model='securityAgenciesModel' :rules='bdtzAddFormRules' ref='securityAgenciesModelRef' >

                  <el-form-item label="上传扫描盖章版管理机构"  prop='uploadFile'>
                    <j-upload style='display: -webkit-box' v-model="securityAgenciesModel.uploadFile" fileTypeList="pdf,png,jpg,jpeg,image" :number="1"></j-upload>
                  </el-form-item>
                </el-form>
                  <a-form-model-item label="查看盖章版管理机构">
                    <a v-if='attachmentModel.securityAgencies' @click='viewFile(attachmentModel.securityAgencies)'>点击查看</a>
                    <span v-show='!attachmentModel.securityAgencies'>无</span>
                  </a-form-model-item>

              </a-row>
              <a-col :span="24" style="text-align: center;">
                <a-button type="primary" @click="saveAttachment('securityAgencies',securityAgenciesModel)">保存</a-button>
              </a-col>


            </div>

      </el-tab-pane>
      <el-tab-pane v-if="institutionFormTitle !== '新增'" label="安全经费提取" name="safety-funding">
        <el-button @click="showFundingModal('securityFunding')" type="primary">新增</el-button>
        <el-table :data="fundingData" style="width: 100%" :default-sort='{prop:"uploadTime",order:"descending"}' >
          <el-table-column  type="index" label="序号" width="60"></el-table-column>
          <el-table-column prop="uploadFile" label="安全经费提取">
            <template slot-scope="scope">
              <span>查看上传材料：<a @click='viewFile(scope.row)' >点击查看</a></span>
            </template>
          </el-table-column>
          <el-table-column prop="uploadYear" label="年度"></el-table-column>
          <el-table-column prop="uploadTime"  label="上传时间"></el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button @click="handleFundingCheck(scope.row,'securityFunding')" type="text" size="small">查看</el-button>
              <el-button type="text" size="small" @click="handleEdit(scope.row,'securityFunding')">编辑</el-button>
              <el-button type="text" size="small" @click="handleDel(scope.row)">删除</el-button>
            </template></el-table-column
          >
        </el-table>
      </el-tab-pane>
      <el-tab-pane v-if="institutionFormTitle !== '新增'" label="安全考核" name="safety-exam">
        <div class="safety-production-management">
          <a-row>
            <el-form :model='safetyAssessmentModel' :rules='bdtzAddFormRules' ref='safetyAssessmentModelRef' >

            <el-form-item label="上传扫描盖章版安全考核："  prop='uploadFile'>
              <j-upload style='display: -webkit-box' v-model="safetyAssessmentModel.uploadFile" fileTypeList="pdf,png,jpg,jpeg,image" :number="1"></j-upload>
            </el-form-item>
            </el-form>

            <a-form-model-item label="查看盖章版安全考核">
              <a v-if='attachmentModel.safetyAssessment!==undefined' @click='viewFile(attachmentModel.safetyAssessment)'>点击查看</a>
              <span v-show='attachmentModel.safetyAssessment===undefined'>无</span>
            </a-form-model-item>

          </a-row>
          <a-col :span="24" style="text-align: center;">
            <a-button type="primary" @click="saveAttachment('safetyAssessment',safetyAssessmentModel)">保存</a-button>
          </a-col>
        </div>

      </el-tab-pane>
      <el-tab-pane v-if="institutionFormTitle !== '新增'" label="安全生产责任制" name="responsibility">

        <div class="safety-production-management">
          <a-row>

            <el-form :model='safetyAccountabilityModel' :rules='bdtzAddFormRules' ref='safetyAccountabilityModelRef' >

            <el-form-item label="上传扫描盖章版责任制" prop='uploadFile'>
              <j-upload style='display: -webkit-box' v-model="safetyAccountabilityModel.uploadFile" fileTypeList="pdf,png,jpg,jpeg,image" :number="1"></j-upload>
            </el-form-item>
            </el-form>
            <a-form-model-item label="查看盖章版责任制" >
              <a v-if='attachmentModel.safetyAccountability' @click='viewFile(attachmentModel.safetyAccountability)'>点击查看</a>
              <span v-show='attachmentModel.safetyAccountability===undefined'>无</span>
            </a-form-model-item>

          </a-row>
          <a-col :span="24" style="text-align: center;">
            <a-button type="primary" @click="saveAttachment('safetyAccountability',safetyAccountabilityModel)">保存</a-button>
          </a-col>
        </div>


      </el-tab-pane>
      <el-tab-pane v-if="institutionFormTitle !== '新增'" label="应急演练" name="emergency">
        <el-button @click="showFundingModal('emergencyDrills')" type="primary">新增</el-button>
        <el-table :data="emergencyDrillsTableData" style="width: 100%" :default-sort='{prop:"uploadTime",order:"descending"}'>
          <el-table-column  type="index" label="序号" width="60"></el-table-column>
          <el-table-column prop="uploadFile" label="应急演练">
            <template slot-scope="scope">
              <span>查看上传材料：<a @click='viewFile(scope.row)' >点击查看</a></span>
            </template>
          </el-table-column>
          <el-table-column :formatter='yearFormatter' prop="uploadYear" label="年度">

          </el-table-column>
          <el-table-column prop="uploadTime" label="上传时间"></el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button @click="handleFundingCheck(scope.row,'emergencyDrills')" type="text" size="small">查看</el-button>
              <el-button type="text" size="small" @click="handleEdit(scope.row,'emergencyDrills')">编辑</el-button>
              <el-button type="text" size="small" @click="handleDel(scope.row)">删除</el-button>
            </template></el-table-column
          >
        </el-table>
      </el-tab-pane>
      <el-tab-pane v-if="institutionFormTitle !== '新增'" label="八大台账" name="ledger">
        <el-button @click="showLedgerModal('ledger')" type="primary">新增</el-button>
        <el-table :data="ledgerTableData" style="width: 100%" :default-sort='{prop:"uploadTime",order:"descending"}'>
          <el-table-column  type="index" label="序号" width="60"></el-table-column>
          <el-table-column prop="uploadFile" label="安全生产八大台账">
            <template slot-scope="scope">
              <span>查看上传材料：<a  @click='viewFile(scope.row)'>点击查看</a></span>
            </template>
          </el-table-column>
          <el-table-column prop="uploadYear" label="上传年度"></el-table-column>
          <el-table-column prop="uploadType" :formatter='uploadTypeFormatter' label="季度/月份">

          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button @click="handleFundingCheck(scope.row,'ledger')" type="text" size="small">查看</el-button>
              <el-button type="text" size="small" @click="handleEdit(scope.row,'ledger')">编辑</el-button>
              <el-button type="text" size="small" @click="handleDel(scope.row)">删除</el-button>
            </template></el-table-column
          >
        </el-table>
      </el-tab-pane>
    </el-tabs>
    <!-- 应急演练-新增弹窗 -->
    <el-dialog
      :visible.sync="emergencyDrillsVisible"
      :title="title"
      custom-class="InstitutionFormDialog"
      :modal-append-to-body="false"
    >
      <el-form :model="emergencyDrillsModel" :rules="addFormRules" ref="emergencyDrillsModelRef">


        <el-form-item :disabled="true" label="培训机构名称" prop="institutionName">
          <el-input v-model="model.name" :disabled="true" placeholder="请输入培训机构名称"></el-input>
        </el-form-item>


        <el-row>
          <el-col :span='9'>
            <el-form-item label="上传年份" prop="uploadYear">
              <el-select :disabled="isDisabled()" v-model="emergencyDrillsModel.uploadYear" placeholder="请选择上传年份">
                <el-option v-for="year in years" :key="year" :label="year" :value="year"></el-option>
              </el-select>
            </el-form-item>

          </el-col>
          <el-col :span='9'>
            <el-form-item  prop="uploadYearType">
              <el-select :disabled="isDisabled()" v-model="emergencyDrillsModel.uploadYearType" placeholder="请选择上/下半年">
                <el-option label="上半年" :value="1"></el-option>
                <el-option label="下半年" :value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>



        <el-form-item label="上传材料" prop="uploadFile">
          <a v-if="isDisabled() && emergencyDrillsModel.uploadFile!==''" @click="viewFile(emergencyDrillsModel)">点击查看</a>

          <j-upload v-else v-model="emergencyDrillsModel.uploadFile" fileTypeList="pdf,png,jpg,jpeg,image" :number="1">
            <el-button size="small" icon="el-icon-upload2">点击上传</el-button>
          </j-upload>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="emergencyDrillsVisible = false">关闭</el-button>
        <el-button type="primary" @click="saveEmergencyDrills">保存</el-button>
      </span>
    </el-dialog>
    <!-- 安全经费提取-新增弹窗 -->
    <el-dialog
      :visible.sync="securityFundModalVisible"
      :title="title"
      custom-class="InstitutionFormDialog"
      :modal-append-to-body="false"
    >
      <el-form :model="addForm" :rules="addFormRules" ref="addFormRef">


        <el-form-item :disabled="true" label="培训机构名称" prop="institutionName">
          <el-input v-model="model.name" :disabled="true" placeholder="请输入培训机构名称"></el-input>
        </el-form-item>

        <el-form-item :disabled="isDisabled()" label="上传年份" prop="uploadYear">
          <el-select :disabled="isDisabled()" v-model="addForm.uploadYear" placeholder="请选择上传年份">
            <el-option v-for="year in years" :key="year" :label="year" :value="year"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item :disabled="isDisabled()" label="上传材料" prop="uploadFile">

          <a v-if="isDisabled() && addForm.uploadFile!==''" @click="viewFile(addForm)">点击查看</a>

          <j-upload v-else  v-model="addForm.uploadFile" fileTypeList="pdf,png,jpg,jpeg,image" :number="1">
            <el-button size="small" icon="el-icon-upload2">点击上传</el-button>
          </j-upload>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="securityFundModalVisible = false">关闭</el-button>
        <el-button type="primary" @click="submitAddForm">保存</el-button>
      </span>
    </el-dialog>
    <!--八大台账新增弹窗-->
    <el-dialog
      :visible.sync="ledgerModalVisible"
      :title="title"
      custom-class="InstitutionFormDialog"
      :modal-append-to-body="false"
    >
      <el-form :model="addLedgerForm" :rules="bdtzAddFormRules" ref="bdtzAddFormRef">
        <el-form-item :disabled="true" label="培训机构名称">
          <el-input v-model="model.name" :disabled="true" placeholder="请输入培训机构名称"></el-input>
        </el-form-item>

        <el-form-item label="上传年份" prop="uploadYear">
          <el-select :disabled="isDisabled()" v-model="addLedgerForm.uploadYear" placeholder="请选择上传年份">
            <el-option v-for="year in years" :key="year" :label="year" :value="year"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="台账类型（月/季度）" prop="uploadType">
          <el-select :disabled="isDisabled()" v-model="addLedgerForm.uploadType" @change='uploadTypeChange' placeholder="请选择台账类型">
            <el-option  label="月" value="month"></el-option>
            <el-option  label="季度" value="quarter"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="月/季度" prop="typeTime">
          <el-select :disabled="isDisabled()" v-model="addLedgerForm.typeTime" @change='typeTimeChange'  placeholder="请选择月/季度">
            <el-option v-for='item in cycles' :key='item.value' :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="上传材料" prop="uploadFile">

          <a v-if="isDisabled() && addLedgerForm.uploadFile!==''" @click="viewFile(addLedgerForm)">点击查看</a>

          <j-upload v-else v-model="addLedgerForm.uploadFile" fileTypeList="pdf,png,jpg,jpeg,image" :number="1">
            <el-button size="small" icon="el-icon-upload2">点击上传</el-button>
          </j-upload>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="ledgerModalVisible = false">关闭</el-button>
        <el-button type="primary" @click="submitAddLedgerForm">保存</el-button>
      </span>
    </el-dialog>
    <!--图片预览-->
    <el-dialog
      :visible.sync='previewVisible'
      title='预览'
      custom-class='InstitutionFormDialog'
      :modal-append-to-body='false'
    >
      <el-image
        style='width: 100%;height:100%'
        :src='imgUrl'
        :preview-src-list='imgUrls'
      >
      </el-image>
    </el-dialog>
  </a-spin>
</template>

<script>
import { httpAction, getAction, postAction } from '@/api/manage'
import { validateDuplicateValue } from '@/utils/util'
import { formMixins } from '@/mixins/formMixins'
import dayjs from 'dayjs'
import { Base64 } from 'js-base64'

export default {
  name: 'InstitutionForm',
  components: {},
  mixins: [formMixins],
  props: {
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      activeTab: 'basic-info', // 默认显示的 Tab
      securityFundModalVisible: false, // 控制新增弹窗的显示
      addForm: {
        uploadYear: '',
        uploadFile: ''
      },
      years: [2022, 2023, 2024,2025,2026], // 可选择的年份
      bdtzAddFormRules: {
        uploadYear: [{ required: true, message: '上传年份不能为空', trigger: 'change' }],
        uploadType: [{ required: true, message: '台账类型（月/季度）不能为空', trigger: 'change' }],
        typeTime: [{ required: true, message: '月/季度不能为空', trigger: 'change' }],
        uploadFile: [{ required: true, message: '上传材料不能为空', trigger: 'change' }]
      },
      addLedgerForm:{
        uploadYear:'',
        uploadType:'',
        uploadFile:'',
        typeTime:'',
      },
      addFormRules: {
        uploadYear: [{ required: true, message: '请选择上传年份', trigger: 'change' }],
        uploadYearType: [{ required: true, message: '请选择上下半年', trigger: 'change' }],
        uploadFile: [{ required: true, message: '请上传材料', trigger: 'change' }]
      },
      fundingModalVisible: false,
      fundingForm: {
        file: null,
        remark: ''
      },
      //周期
      cycles:[],
      //月份
      months:[
        {
          label:'一月',
          value:'1'
        },
        {
          label:'二月',
          value:'2'
        },
        {
          label:'三月',
          value:'3'
        },
        {
          label:'四月',
          value:'4'
        },
        {
          label:'五月',
          value:'5'
        },
        {
          label:'六月',
          value:'6'
        },
        {
          label:'七月',
          value:'7'
        },
        {
          label:'八月',
          value:'8'
        },
        {
          label:'九月',
          value:'9'
        },
        {
          label:'十月',
          value:'10'
        },
        {
          label:'十一月',
          value:'11'
        },
        {
          label:'十二月',
          value:'12'
        },
      ],
      //季度
      quarter:[
        {
          label:'第一季度',
          value:'1'
        },
        {
          label:'第二季度',
          value:'2'
        },
        {
          label:'第三季度',
          value:'3'
        },
        {
          label:'第四季度',
          value:'4'
        },
      ],
      //区分安全经费还是安全演练(新增的时候)
      type:'',
      fundingData: [],
      emergencyDrillsTableData:[],
      ledgerTableData:[],
      dialogVisible: false, // 控制弹窗显示的布尔值
      ledgerModalVisible:false,
      //安全生产管理制度
      securitySystemModel:{},
      //安全生产管理机构
      securityAgenciesModel:{},
      //安全考核
      safetyAssessmentModel:{},

      safetyAccountabilityModel:{},
      title:'',
      emergencyDrillsVisible:false,
      emergencyDrillsModel:{
        uploadYear:'',
        uploadYearType:'',
        uploadFile:'',
      },

      //模块附件信息
      attachmentModel:{
        //安全生产管理制度
        securitySystem:{},
        securityAgencies:{},
        //安全考核
        safetyAssessment:{},
        safetyAccountability:{},
      },
      previewVisible:false,
      imgUrl:'',
      imgUrls:[],

      model: {},
      labelCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 8
        }
      },
      wrapperCol: {
        xs: {
          span: 24
        },
        sm: {
          span: 15
        }
      },
      confirmLoading: false,
      validatorRules: {
        district: [
          {
            required: true,
            message: '请输入区县行政区划代码!'
          }
        ],
        name: [
          {
            required: true,
            message: '请输入培训机构全称!'
          }
        ],
        licnum: [
          {
            required: true,
            message: '请输入经营许可证编号!'
          }
        ],
        licetime: [
          {
            required: true,
            message: '请输入经营许可日期!'
          }
        ],
        address: [
          {
            required: true,
            message: '请输入培训机构地址!'
          }
        ],
        postcode: [
          {
            required: true,
            message: '请输入邮政编码!'
          }
        ],
        legal: [
          {
            required: true,
            message: '请输入法人代表!'
          }
        ],
        contact: [
          {
            required: true,
            message: '请输入联系人!'
          }
        ],
        platform: [
          {
            required: true,
            message: '请选择计时厂商!'
          }
        ],
        shortname: [
          {
            required: true,
            message: '请输入培训机构简称!'
          }
        ],
        creditcode: [
          {
            required: true,
            message: '请输入统一社会信用代码!'
          }
        ],
        phone: [
          {
            required: true,
            message: '请填写联系电话!'
          }
        ],
        busiscope: [
          {
            required: true,
            message: '请选择经营范围!'
          }
        ],
        busistatus: [
          {
            required: true,
            message: '请选择经营状态!'
          }
        ],
        levels: [
          {
            required: true,
            message: '请选择分类等级!'
          }
        ],
        coachnumber: [
          {
            required: true,
            message: '请输入教练员总数!'
          }
        ],
        grasupvnum: [
          {
            required: true,
            message: '请填写考核员总数!'
          }
        ],
        safmngnum: [
          {
            required: true,
            message: '请填写安全员总数!'
          }
        ],
        tracarnum: [
          {
            required: true,
            message: '请填写教练车总数!'
          }
        ],
        praticefield: [
          {
            required: true,
            message: '请填写教练场总面积!'
          }
        ]
      },
      institutionFormTitle:'',
      url: {
        save:'/gzpt/attachmentInfo/saveAttachment',
        del:'/gzpt/attachmentInfo/delAttachment',
        get:'/gzpt/attachmentInfo/findTypeList',
        add: '/gzpt/institution/add',
        edit: '/gzpt/institution/edit',
        queryById: '/gzpt/institution/queryById'
      }
    }
  },
  computed: {
    formDisabled() {
      return this.disabled
    }
  },
  created() {
    //备份model原始值
    this.modelDefault = JSON.parse(JSON.stringify(this.model))
    setTimeout(
      ()=>{
        this.getAttachment()
      },100
    )
    // 获取当前年份
    const currentYear = dayjs().year()
    this.years = new Array(10).fill(undefined).map((item, index) => currentYear + 1 - index)
  },
  watch:{
    //监听八大台账的弹窗
    ledgerModalVisible(newVal, oldVal) {
      if (newVal){
        if (this.addLedgerForm.uploadType === 'month') {
          this.cycles=this.months
        }else{
          this.cycles=this.quarter
        }
      }
    }
  },
  methods: {
    yearFormatter(row,column) {
      if (row.uploadYearType === 1) {
        return row.uploadYear+'-上半年'
      }else if(row.uploadYearType === 2) {
        return row.uploadYear+'-下半年'
      }

    },
    viewFile(data){
      if (data.uploadFile!==undefined){
        let end = data.uploadFile.lastIndexOf('?')
        let start = data.uploadFile.lastIndexOf('.')
        let type = data.uploadFile.substring(start+1, end)
        if (type!=='pdf'){
          this.previewVisible=true
          this.imgUrl=data.uploadFile
          this.imgUrls=[data.uploadFile]
          return
        }
        window.open(data.uploadFile, "_blank",'location=no')
      }


    },
    //保存附件数据
    saveAttachment(subType,contentData){

      let id =undefined;
      let flag=true;
      if (subType==='securitySystem'){
        this.$refs.securitySystemModelRef.validate(valid => {
          flag=valid;
        })
        id=this.attachmentModel.securitySystem?.id
      }else if (subType==='securityAgencies'){
        this.$refs.securityAgenciesModelRef.validate(valid => {
          flag=valid;
        })
        id=this.attachmentModel.securityAgencies?.id
      }else if(subType==='safetyAssessment'){
        this.$refs.safetyAssessmentModelRef.validate(valid => {
          flag=valid;
        })
        id=this.attachmentModel.safetyAssessment?.id
      }else if (subType==='safetyAccountability'){
        this.$refs.safetyAccountabilityModelRef.validate(valid => {
          flag=valid;
        })
        id=this.attachmentModel.safetyAccountability?.id
      }else {
        id=contentData.id
      }

      if (flag){
        let  data ={
          id:id,//编辑或覆盖文件的时候此id必传
          associateId: this.model.id,
          type: 1,//1驾培机构，2教练员 3教练车
          subType: subType ,//小项的类型

          content: Base64.encode(JSON.stringify(contentData)) //base64加密一下内容

        }
        let that = this;
        postAction(this.url.save,data)
          .then((res) => {
            if (res.code===200){
              that.$message.success(res.message)
              this.securitySystemModel={}
              this.securityAgenciesModel={}
              this.safetyAssessmentModel={}
              this.safetyAccountabilityModel={}
              this.getAttachment()
            }else {
              that.$message.error(res.message)
            }

          })

      }
    },
    //获取所有培训机构附件数据
    getAttachment(){

      let params = {
        associateId:this.model.id,
        type: 1,
      }
      let that = this

      getAction(this.url.get,params)
        .then((res) => {
          that.attachmentModel=res.result

          if (res.result.securityFunding!==undefined){
            if (!(res.result.securityFunding instanceof Array)){
              that.fundingData= [res.result.securityFunding];
            }else {
              that.fundingData= res.result.securityFunding;
            }

          }else {
            that.fundingData=[]
          }

          if (res.result.emergencyDrills!==undefined){
            if (!(res.result.emergencyDrills instanceof Array)){
              that.emergencyDrillsTableData= [res.result.emergencyDrills];
            }else {
              that.emergencyDrillsTableData= res.result.emergencyDrills;
            }
          }else {
            that.emergencyDrillsTableData=[]
          }


        if (res.result.ledger!==undefined){
          if (!(res.result.ledger instanceof Array)){
            that.ledgerTableData= [res.result.ledger];

          }else {
            that.ledgerTableData= res.result.ledger;
          }

        }else {
          that.ledgerTableData=[]
        }


      })

    },

    handleEdit(row,type){

      this.title='编辑'
      this.type = type
      let jsonRowStr = JSON.stringify(row)

      if (type==='securityFunding'){
        this.addForm=JSON.parse(jsonRowStr)
        this.securityFundModalVisible=true;
      }else if(type==='emergencyDrills'){
        this.emergencyDrillsModel=JSON.parse(jsonRowStr)
        this.emergencyDrillsVisible=true;

      }else if(type==='ledger'){
        this.addLedgerForm=JSON.parse(jsonRowStr)
        this.ledgerModalVisible=true;
      }

    },
    isDisabled(){
      return this.title==='查看'
    },
    //安全经费的查看
    handleFundingCheck(row,type) {
      this.title='查看'
      this.type = type
      let jsonRowStr = JSON.stringify(row)

      if (type==='securityFunding'){
        this.addForm=JSON.parse(jsonRowStr)
        this.securityFundModalVisible=true;
      }else if(type==='emergencyDrills'){
        this.emergencyDrillsModel=JSON.parse(jsonRowStr)
        this.emergencyDrillsVisible=true;

      }else if(type==='ledger'){
        this.addLedgerForm=JSON.parse(jsonRowStr)
        this.ledgerModalVisible=true;
      }
    },
    //八大台账新增编辑保存
    submitAddLedgerForm(){
      this.$refs.bdtzAddFormRef.validate(valid => {
        if (valid) {
          this.saveAttachment(this.type,this.addLedgerForm)
          this.ledgerModalVisible=false;
          this.getAttachment()
        }
      })
      //console.log('提交的表单数据:', this.addForm)
      //this.$message.success('新增成功！')
    },
    saveEmergencyDrills(){
      this.$refs.emergencyDrillsModelRef.validate(valid => {
        if (valid) {
          this.saveAttachment('emergencyDrills',this.emergencyDrillsModel)
          this.emergencyDrillsVisible=false;
        }
      })
    },
    //删除相关附件
    handleDel(row){
      let params = {
        id:row.id,
      }
      getAction(this.url.del,params).then(res=>{
        if (res.code===200){
          this.$message.success(res.message)
          this.getAttachment()
        }else {
          this.$message.error(res.message)
        }
      })


    },

    uploadTypeChange(value){
      this.addLedgerForm.typeTime=''
      if (value==='month'){
        this.cycles=this.months
      }else {
        this.cycles=this.quarter
      }
    },
    typeTimeChange(value){

    },
    showFundingModal(type) {
      this.title='新增'
      this.type=type;
      if (type==='securityFunding'){
        this.addForm={
          uploadYear: '',
          uploadFile: ''
        }
        this.$refs.addFormRef?.resetFields()
        this.securityFundModalVisible=true;
      }else {
        this.emergencyDrillsModel={
          uploadYear:'',
          uploadYearType:'',
          uploadFile:'',
        }
        this.$refs.emergencyDrillsModelRef?.resetFields()
        this.emergencyDrillsVisible = true
      }




    },
    showLedgerModal(type){
      this.title='新增'
      this.type=type;
      this.ledgerModalVisible=true;
      this.$refs.bdtzAddFormRef?.resetFields()
      this.addLedgerForm={
        uploadYear:'',
        uploadType:'',
        uploadFile:'',
        typeTime:'',
      }
    },
    uploadTypeFormatter(row){
      if (row.uploadType==='month'){
        let find = this.months.find(item=>item.value===row.typeTime)
        return find.label
      }else {
        let find = this.quarter.find(item=>item.value===row.typeTime)
        return find.label
      }

    },
    // showAddModal() {
    //   this.addModalVisible = true; // 显示弹窗
    // },
    handleFileUploadSuccess(response, file) {
      this.addForm.uploadFile = file.name // 保存上传的文件名
      this.$message.success('文件上传成功！')
    },
    // 上传成功的回调
    submitAddForm() {

      let that = this;
      this.$refs.addFormRef.validate(valid => {
        if (valid) {
          that.saveAttachment(that.type,that.addForm)
          this.securityFundModalVisible = false // 关闭弹窗
        } else {
          this.$message.error('请填写所有必填项！')
        }
      })
    },
    resetAddForm() {
      this.addForm = {
        institutionName: '',
        uploadYear: '',
        uploadFile: null
      }
    },
    // 关闭弹窗
    handleClose() {
      this.dialogVisible = false
    },
    // 保存弹窗内容
    saveDialog() {
      this.$message({
        message: '保存成功',
        type: 'success'
      })
      this.dialogVisible = false
    },
    handleDetailPop() {
      this.dialogVisible = true
    },
    add() {
      this.institutionFormTitle='新增'
      this.edit(this.modelDefault)
    },
    edit(record) {
      this.model = Object.assign({}, record)
      this.visible = true
    },
    submitForm() {
      const that = this
      // 触发表单验证
      this.$refs.form.validate(valid => {
        if (valid) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          httpAction(httpurl, this.model, method)
            .then(res => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok')
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    }
  }
}
</script>

<style lang="less">
.InstitutionFormDialog {
  z-index: 999 !important;
}
.safety-production-management{
  .ant-row{
    display: flex;
    justify-content: space-between;
    //align-items: center;
  }
}
</style>
