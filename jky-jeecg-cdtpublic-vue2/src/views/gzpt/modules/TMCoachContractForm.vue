<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="教练姓名" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="name">
              <a-input v-model="model.name" placeholder="教练姓名"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="性别" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sex">
              <!--                <a-input v-model="model.sex" placeholder="请输入性别"  ></a-input>-->
              <j-dict-select-tag type="list" v-model="model.sex" dictCode="sex"
                                 placeholder="请输入性别" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
          <a-form-model-item label="身份证号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="idcard">
            <a-input v-model="model.idcard" placeholder="请输入身份证号"  ></a-input>
          </a-form-model-item>
        </a-col>
          <a-col :span="24">
            <a-form-model-item label="驾校名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="inscode">
				<a-input v-model="model.insname" placeholder="" v-if="title!='新增'" disabled></a-input>
				<j-tree-select-depart v-if="title=='新增'" placeholder="请选择培训机构" v-model="model.inscode" >
				</j-tree-select-depart>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="上传合同" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="pdfurl">
<!--              <a-input v-model="model.pdfurl" placeholder="请输入合同存放路径"  ></a-input>-->
              <j-upload v-model="model.pdfurl" fileTypeList="pdf" :multiple="true" :number="1" fileType="pdf">
              </j-upload>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="合同签订/创建时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="createtime">
				 <j-date placeholder="请选择合同签订/创建时间" v-model="model.createtime" style="width: 100%" />			
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'
	import JTreeSelectDepart from '@/components/common/JTreeSelectDepart.vue'
  export default {
    name: 'TMCoachContractForm',
    components: {
			JTreeSelectDepart
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
				title:'',
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
           pdfurl: [
              { required: true, message: '请输入合同存放路径!'},
           ],
        },
        url: {
          add: "/jky/tMCoachContract/add",
          edit: "/jky/tMCoachContract/edit",
          queryById: "/jky/tMCoachContract/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }

        })
      },
    }
  }
</script>