<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="学员编号" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="stunum">
              <a-input v-model="model.stunum" placeholder="请输入学员编号"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="合同存放路径" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="pdfurl">
              <a-input v-model="model.pdfurl" placeholder="请输入合同存放路径"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="合同签订/创建时间" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="createtime">
              <a-input v-model="model.createtime" placeholder="请输入合同签订/创建时间"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="基础费用" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="basicCost">
              <a-input v-model="model.basicCost" placeholder="请输入基础费用"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="第二部分费用" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="secondCost">
              <a-input-number v-model="model.secondCost" placeholder="请输入第二部分费用" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="第三部分费用" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="thirdCoast">
              <a-input-number v-model="model.thirdCoast" placeholder="请输入第三部分费用" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="冻结金额" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="frozenAmount">
              <a-input-number v-model="model.frozenAmount" placeholder="请输入冻结金额" style="width: 100%" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'StudentContractForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
           stunum: [
              { required: true, message: '请输入学员编号!'},
           ],
           pdfurl: [
              { required: true, message: '请输入合同存放路径!'},
           ],
        },
        url: {
          add: "/gzpt/studentContract/add",
          edit: "/gzpt/studentContract/edit",
          queryById: "/gzpt/studentContract/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>