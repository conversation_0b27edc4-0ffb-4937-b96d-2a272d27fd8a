<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="培训机构">
              <j-tree-select-depart placeholder="请选择培训机构" v-model="queryParam.inscode"> </j-tree-select-depart>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
            <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus" v-has="'gzpt:t_m_institution_class:add'">新增</a-button>
      <a-button
        type="primary"
        icon="download"
        @click="handleExportXls('t_m_institution_class')"
        v-has="'gzpt:t_m_institution_class:daochu'"
        >导出</a-button
      >
      <!--      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>-->
      <!-- 高级查询区域 -->
      <!--      <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query>-->
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay" v-has="'gzpt:institutionClass:removeBatch'">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete" />删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <!--      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>-->

      <a-table
        ref="table"
        size="middle"
        :scroll="{ x: true }"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        class="j-table-force-nowrap"
        @change="handleTableChange"
      >
        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text, record">
          <span v-if="!text" style="font-size: 12px; font-style: italic">无图片</span>
          <img
            v-else
            :src="getImgView(text)"
            :preview="record.id"
            height="25px"
            alt=""
            style="max-width: 80px; font-size: 12px; font-style: italic"
          />
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px; font-style: italic">无文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
            下载
          </a-button>
        </template>
        <template slot="imgSlot" slot-scope="text, record">
          <span v-if="!text" style="font-size: 12px; font-style: italic">无图片</span>
          <img
            v-else
            :src="text"
            :preview="record.id"
            height="25px"
            alt=""
            style="max-width: 80px; font-size: 12px; font-style: italic"
            @click="showBig(text)"
          />
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)" v-has="'gzpt:t_m_institution_class:edit'">编辑</a>

          <a-divider type="vertical" v-has="'gzpt:t_m_institution_class:edit'" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item v-has="'gzpt:institutionClass:remove'">
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </a-table>
    </div>

    <t-m-institution-class-modal ref="modalForm" @ok="modalFormOk"></t-m-institution-class-modal>
    <a-modal v-model="isshowBig" :footer="null">
      <div class="showBig">
        <img :src="bigUrl" />
      </div>
    </a-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import TMInstitutionClassModal from './modules/TMInstitutionClassModal'
import JTreeSelectDepart from '@/components/common/JTreeSelectDepart.vue'
export default {
  name: 'TMInstitutionClassList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    JTreeSelectDepart,
    TMInstitutionClassModal,
  },
  data() {
    return {
      description: 't_m_institution_class管理页面',
      // 表头
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          },
        },
        // {
        //   title:'培训机构编码',
        //   align:"center",
        //   dataIndex: 'inscode'
        // },
        {
          title: '图片',
          align: 'center',
          dataIndex: 'photopath',
          scopedSlots: {
            customRender: 'imgSlot',
          },
        },
        {
          title: '培训机构名称',
          align: 'center',
          dataIndex: 'inscode_dictText',
        },
        {
          title: '培训车型',
          align: 'center',
          dataIndex: 'traintype_dictText',
        },

        {
          title: '班级名称',
          align: 'center',
          dataIndex: 'classname',
        },
        {
          title: '班级简介',
          align: 'center',
          dataIndex: 'classintro',
        },
        {
          title: '班级价格',
          align: 'center',
          dataIndex: 'claprice',
        },
        // {
        //   title:'基础费用',
        //   align:"center",
        //   dataIndex: 'basicCost'
        // },
        // {
        //   title:'第二部分费用',
        //   align:"center",
        //   dataIndex: 'secondCost'
        // },
        // {
        //   title:'第三部分费用',
        //   align:"center",
        //   dataIndex: 'thirdCoast'
        // },
        {
          title: '班级备注',
          align: 'center',
          dataIndex: 'claremark',
        },
        // {
        //   title:'班级教练员',
        //   align:"center",
        //   dataIndex: 'coach'
        // },
        // {
        //   title:'课程编号',
        //   align:"center",
        //   dataIndex: 'classno'
        // },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 147,
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        list: '/zlb/tmc/tMInstitutionClass/pclist',
        delete: '/zlb/tmc/tMInstitutionClass/delete',
        deleteBatch: '/zlb/tmc/tMInstitutionClass/deleteBatch',
        exportXlsUrl: '/zlb/tmc/tMInstitutionClass/exportXls',
        importExcelUrl: '/zlb/tmc/tMInstitutionClass/importExcel',
      },
      dictOptions: {},
      superFieldList: [],
      bigUrl: '',
      isshowBig: false,
    }
  },
  created() {
    this.getSuperFieldList()
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  methods: {
    showBig(text) {
      this.isshowBig = true
      this.bigUrl = text
    },
    initDictConfig() {},
    getSuperFieldList() {
      let fieldList = []
      fieldList.push({ type: 'string', value: 'inscode', text: '培训机构编码' })
      fieldList.push({ type: 'string', value: 'name', text: '培训机构名称' })
      fieldList.push({ type: 'string', value: 'classname', text: '班级名称' })
      fieldList.push({ type: 'string', value: 'classintro', text: '班级简介' })
      fieldList.push({ type: 'string', value: 'claprice', text: '班级价格' })
      fieldList.push({ type: 'string', value: 'claremark', text: '班级备注' })
      fieldList.push({ type: 'string', value: 'coach', text: '班级教练员' })
      fieldList.push({ type: 'string', value: 'classno', text: '课程编号' })
      this.superFieldList = fieldList
    },
  },
}
</script>
<style scoped lang="less">
@import '~@assets/less/common.less';
/deep/ .ant-table{
td{
  height: 50px;
}
}
</style>