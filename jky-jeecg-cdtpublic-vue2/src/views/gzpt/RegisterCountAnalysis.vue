<template>
  <a-card :bordered="false">
    <a-spin :spinning="spinning">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="6" :md="6" :sm="24">
            <a-form-item label="培训机构">
              <j-tree-select-depart dict="treeselect" v-model="queryParam.inscode"
                placeholder="请选择培训机构"></j-tree-select-depart>
            </a-form-item>
          </a-col>
          <a-col :xl="10" :lg="10" :md="10" :sm="24">
            <a-form-item label="统计对比月份">
              <a-month-picker
                placeholder="请选择起始月份"
                v-model="queryParam.createTimeStart"
                @change="getEndTimeScope"
                format="YYYY-MM"
                valueFormat="YYYYMM"
              /> - 
              <a-month-picker
                placeholder="请选择结束月份"
                v-model="queryParam.createTimeEnd"
                :disabledDate="disabledDate"
                format="YYYY-MM"
                valueFormat="YYYYMM"
              />
            </a-form-item>
          </a-col>
          <a-col :xl="4" :lg="4" :md="4" :sm="24">
            <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- table区域-begin -->
    <div>
      <div class="chart-area" ref="chartLine"></div>
    </div>
    </a-spin>
  </a-card>
</template>

<script>
  import moment from 'moment';
  import JTreeSelectDepart from '@/components/common/JTreeSelectDepart'
  import { getAction, postAction } from '@/api/manage';
  
  const echarts = require('echarts');
  
  export default {
    name: 'RegisterCountAnalysis',
    components: {
      JTreeSelectDepart
    },
    data () {
      return {
        description: 'RegisterCountAnalysis管理页面',
        // 表头
        createTimeRange: [],
        chartOption: {},
        chartLine: null,
        xData: [],
        nowData: [],
        beforeData: [],
        queryParam: {
          inscode: '',
          createTimeStart: moment(new Date()).subtract(12, 'month').format('YYYYMM'),
          createTimeEnd: moment(new Date()).format('YYYYMM'),
        },
        spinning: false
      }
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    mounted() {
      this.searchQuery();
    },
    methods: {
      drawChart() {
        if (this.chartLine) {
          this.chartLine.dispose();
          this.chartLine = null;
        }
        this.chartLine = echarts.init(this.$refs.chartLine);
        this.chartLine.setOption(this.chartOption);
      },
      initChartOption() {
        this.chartOption = {
          legend: {
            show: true,
          },
          tooltip: {
            show: true,
            trigger: 'axis'
          },
          xAxis: {
            type: 'category',
            data: this.xData
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '当前数据',
              data: this.nowData,
              type: 'line'
            },
            {
              name: '上年数据',
              data: this.beforeData,
              type: 'line'
            }
          ]
        };
      },
      searchQuery() {
        this.spinning = true;
        getAction('/gzpt/statistics/analyseYear2Year', this.queryParam).then((res) => {
          this.spinning = false;
          if (res.success) {
            this.xData = res.result.x;
            this.nowData = res.result.now;
            this.beforeData = res.result.before;
            this.initChartOption();
            this.drawChart()
          }
        }).catch(() => {
          this.spinning = false;
        })
      },
      getEndTimeScope(v) {
        this.disabledDate()
      },
      disabledDate(current) {
        return this.queryParam.createTimeStart && moment(this.queryParam.createTimeStart).add(12, 'month') < current || (current < this.queryParam.createTimeStart);
      }
    } 
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
  
  .chart-area {
    width: 100%;
    height: 500px;
  }
</style>