<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="培训机构">
              <JTreeSelectDepart
                dict="treeselect"
                v-model="queryParam.inscode"
                placeholder="请输入培训机构"
              ></JTreeSelectDepart>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="对象编号">
              <a-input v-model="queryParam.objnum" placeholder="请输入对象编号" />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="对象名称">
              <a-input v-model="queryParam.objname" placeholder="请输入对象名称" />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="异常类型">
              <!--              <a-input v-model="queryParam.type" placeholder="请输入异常类型" />-->
              <a-tree-select
                v-model="queryParam.type"
                :tree-data="abnormalMaps"
                :replaceFields="{ title: 'label', value: 'id' }"
                :show-count="true"
                placeholder="请选择异常类型"
              />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="处理状态">
              <j-dict-select-tag v-model="queryParam.status" placeholder="请选择处理状态" dictCode="sys_group_status" />
            </a-form-item>
          </a-col>
          <a-col :xl="4" :lg="6" :md="12" :sm="24">
            <a-button @click="searchQuery" type="primary">查询</a-button>
            <a-button style="margin-left: 8px" @click="searchReset">重置</a-button>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
<!--      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>-->
      <a-button type="primary" icon="download" @click="handleExportXls('异常数据管理')">导出</a-button>
      <a-button @click="handleAudit" type="primary" :disabled="selectedRowKeys.length == 1 ? false : true">审核</a-button>
<!--      <a-button @click="workOpen = true" type="primary" icon="" :disabled="selectedRowKeys.length == 1 ? false : true"
        >提交工单</a-button
      >
      <a-button @click="handWorkBack" type="primary" icon="" :disabled="selectedRowKeys.length == 1 ? false : true"
        >撤回工单</a-button
      >
      <a-button @click="handAbnormalNo" type="primary" :disabled="selectedRowKeys.length == 1 ? false : true"
        >加入疑似违规
      </a-button>-->
      <!-- 高级查询区域 -->
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"> <a-icon type="delete" />删除 </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px">
          批量操作
          <a-icon type="down" />
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择
        <a style="font-weight: 600">{{ selectedRowKeys.length }}</a
        >项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{ x: true }"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        class="j-table-force-nowrap"
        @change="handleTableChange"
      >
        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text, record">
          <span v-if="!text" style="font-size: 12px; font-style: italic">无图片</span>
          <img
            v-else
            :src="getImgView(text)"
            :preview="record.id"
            height="25px"
            alt=""
            style="max-width: 80px; font-size: 12px; font-style: italic"
          />
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px; font-style: italic">无文件</span>
          <a-button v-else :ghost="true" type="primary" icon="download" size="small" @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link"
              >更多
              <a-icon type="down" />
            </a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetailNew(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>
      </a-table>
    </div>
    <a-modal v-model="seqOpen" :title="title" @ok="groupSubmit" :width="800">
      <a-form-model ref="seqform" :model="seqform" :rules="seqOpenRules">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="异常类型" prop="seq" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <!--              <a-select v-model="seqform.seq">
                <a-select-option :value="dict.seq" v-for="dict in abnormalList">
                  {{dict.name}}
                </a-select-option>
              </a-select>-->
              <a-tree-select
                v-model="seqform.seq"
                :tree-data="abnormalMaps"
                :replaceFields="{ title: 'label', value: 'id' }"
                :show-count="true"
                placeholder="请选择异常类型"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-modal>
    <a-modal v-model="workOpen" title="提交工单" @ok="submitGropWork" :width="800">
      <a-form-model ref="workform" :model="workform" :rules="workrules">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="附件" prop="file" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-upload
                fileType="image"
                :multiple="true"
                :number="1"
                v-model="workform.file"
                placeholder="请选择异常类型"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-modal>
    <t-m-abnormal-group-modal ref="modalForm" @ok="modalFormOk"></t-m-abnormal-group-modal>
    <a-modal v-model="detailOpen" title="详情" :width="1200" footer="">
      <check-detail ref="checkDetail"></check-detail>
    </a-modal>
    <a-modal v-model="auditOpen" title="异常处理" @ok="submitAudit" :width="800">
      <a-form-model ref="auditform" :model="auditform" :rules="auditrules">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="处理结果" prop="status" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-select v-model="auditform.status" placeholder="请选择处理结果">
                <a-select-option value="1">有效</a-select-option>
                <a-select-option value="2">无效</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="原因" prop="reason" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-textarea v-model="auditform.reason"></a-textarea>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-modal>
  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import TMAbnormalGroupModal from './modules/TMAbnormalGroupModal'
import { workorderBack } from '@/api/gzpt/recordDetail'
import { getAbnormalMapList } from '@/api/gzpt/abnormal'
import { submitWork, addAbnormalNo } from '@/api/gzpt/abnormalGroup'
import JTreeSelectDepart from '@/components/common/JTreeSelectDepart.vue'
import CheckDetail from './modules/checkDetail'
import { postAction } from '@/api/manage'
export default {
  name: 'TMAbnormalGroupList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    CheckDetail,
    JTreeSelectDepart,
    TMAbnormalGroupModal,
  },
  data() {
    return {
      description: 't_m_abnormal_group管理页面',
      // 表头
      columns: [
        {
          title: '序号',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          },
        },
        {
          title: '对象编号',
          align: 'center',
          dataIndex: 'objnum',
        },
        {
          title: '对象名称',
          align: 'center',
          dataIndex: 'objname',
        },
        {
          title: '对象类型',
          align: 'center',
          dataIndex: 'type_dictText',
          // customRender: function (t, r, index) {
          //   if (t === "1") {
          //     return '业户'
          //   }
          //   if (t === "2") {
          //     return '学员'
          //   }
          //   if (t === "3") {
          //     return '教练员'
          //   }
          //   if (t === "4") {
          //     return '教练车'
          //   }
          // },
        },
        {
          title: '培训机构名称',
          align: 'center',
          dataIndex: 'insname',
        },
        // {
        //   title: '异常类型',
        //   align: 'center',
        //   dataIndex: 'abnormalSeq',
        // },
        {
          title: '异常类型名称',
          align: 'center',
          dataIndex: 'abnormalName',
        },
        {
          title: '处理状态',
          align: 'center',
          dataIndex: 'status_dictText',
          // customRender: function (t, r, index) {
          //   if (t === 0) {
          //     return '未处理'
          //   }
          //   if (t === 1) {
          //     return '已处理'
          //   }
          // },
        },
        {
          title: '生成时间',
          align: 'center',
          dataIndex: 'crdate',
          customRender: function (text) {
            return !text ? '' : text.length > 10 ? text.substr(0, 10) : text
          },
        },
        {
          title: '处理时间',
          align: 'center',
          dataIndex: 'disposeDate',
          customRender: function (text) {
            return !text ? '' : text.length > 10 ? text.substr(0, 10) : text
          },
        },
        {
          title: '县区',
          align: 'center',
          dataIndex: 'district',
        },
        {
          title: '是否提交工单',
          align: 'center',
          dataIndex: 'isWorkorder',
          customRender: function (t, r, index) {
            if (t === 0) {
              return '未提交'
            }
            if (t === 1) {
              return '提交'
            }
          },
        },
        {
          title: '提交时间',
          align: 'center',
          dataIndex: 'submitDate',
          customRender: function (text) {
            return !text ? '' : text.length > 10 ? text.substr(0, 10) : text
          },
        },
        {
          title: '原因',
          align: 'center',
          dataIndex: 'reason',
        },
        {
          title: '操作人',
          align: 'center',
          dataIndex: 'operator',
        },
        // {
        //   title: '是否显示该异常组',
        //   align: "center",
        //   dataIndex: 'isShow_dictText'
        // },
        // {
        //   title: '是否是新监管添加',
        //   align: "center",
        //   dataIndex: 'isNewjgpt_dictText'
        // },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 147,
          scopedSlots: {
            customRender: 'action',
          },
        },
      ],
      url: {
        list: '/gzpt/abnormalGroup/list',
        delete: '/gzpt/abnormalGroup/delete',
        deleteBatch: '/gzpt/abnormalGroup/deleteBatch',
        exportXlsUrl: '/gzpt/abnormalGroup/exportXls',
        importExcelUrl: '/gzpt/abnormalGroup/importExcel',
      },
      title:'',
      dictOptions: {},
      superFieldList: [],
      workOpen: false,
      workform: {},
      workrules: {},
      auditOpen:false,
      auditform:{},
      auditrules:{
        status:[{required:true,message:'请选择处理结果'}],
        reason:[{required:true,message:'请选择处理结果'}],
      },
      list: [],
      ids: [],
      detailOpen: false,
      abnormalMaps: null,
      labelCol: {
        xs: {
          span: 24,
        },
        sm: {
          span: 5,
        },
      },
      wrapperCol: {
        xs: {
          span: 24,
        },
        sm: {
          span: 16,
        },
      },
      seqOpen: false,
      seqOpenRules: {
        seq: [
          {
            required: true,
            message: '请选择异常类型',
          },
        ],
      },
      seqform: {
        seq: null,
        ids: null,
        maxCrdate: null,
      },
    }
  },
  created() {
    this.abnormalmapList()
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  methods: {
    groupSubmit() {
      this.$refs.seqform.validate((valid) => {
        if (valid) {
          var ids = []
          var maxCrdate = null
          this.list.forEach(function (item, index) {
            ids.push(item.id)
            if (maxCrdate == null) {
              maxCrdate = item.crdate
            } else if (maxCrdate > item.crdate) {
              maxCrdate = item.crdate
            }
          })
          this.seqform.ids = ids
          this.seqform.maxCrdate = maxCrdate
          let that = this
          this.$confirm({
            title: '是否提交？',
            content: '',
            onOk() {
              addAbnormalNo(that.seqform).then((res) => {
                if (res.success) {
                  that.$message.success('操作成功')
                  that.$refs.seqform.resetFields()
                  that.seqOpen = false
                  that.loadData()
                } else {
                  that.$message.error(res.message)
                }
              })
            },
          })
        }
      })
    },
    handAbnormalNo() {
      this.seqOpen = true
      this.title = '疑似违规'
      this.type = 1
      this.seqform = {
        seq: null,
        ids: null,
      }
    },
    abnormalmapList() {
      getAbnormalMapList().then((response) => {
        this.abnormalMaps = response.result
        console.log('data>>>.', response)
      })
    },
    initDictConfig() {},
    // 多选框选中数据
    onSelectChange(selection, record) {
      this.selectedRowKeys = selection
      this.ids = this.selectedRowKeys
      this.list = record
    },
    //提交工单
    submitGropWork() {
      let that = this
      this.$confirm({
        title: '确定提交工单？',
        content: '',
        onOk() {
          let fileFormData = new FormData()
          if (that.workform.file != null) {
            fileFormData.append('file', that.workform.file)
          }
          fileFormData.append('id', that.ids[0])
          fileFormData.append('workType', 2)
          submitWork(fileFormData).then((res) => {
            if (res.success) {
              that.$message.success('提交成功')
              that.workOpen = false
              that.$refs.workform.resetFields()
              that.loadData()
            } else {
              that.$message.error(res.message)
            }
          })
        },
        onCancel() {},
      })
    },
    handleDetailNew(record) {
      this.detailOpen = true
      this.$nextTick(() => {
        this.$refs.checkDetail.getData(record)
      })
    },

    /* 工单撤销*/
    handWorkBack() {
      let that = this
      var data = {
        id: this.ids[0],
      }
      this.$confirm({
        title: '确定撤销工单？',
        content: '',
        onOk() {
          workorderBack(data).then((res) => {
            that.$message.success('操作成功')
            that.loadData()
          })
        },
      })
    },

    //审核
    handleAudit(){
      this.auditOpen=true;
      this.auditform={}
    },
    submitAudit(){
      const that = this;
      this.$refs.auditform.validate(valid => {
        if (valid) {
          // that.confirmLoading = true;
          that.auditform.parentId =that.selectedRowKeys[0]
          that.auditform.type  =2
          that.auditform.calsstype =0
          postAction('/gzpt/abnormalGroup/audit',that.auditform).then((res)=>{
            if(res.success){
              that.$message.success(res.message);
              // that.$emit('ok');
              that.loadData()
              that.selectedRowKeys=[]
              that.auditOpen=false
              that.auditform={}
            }else{
              that.$message.warning(res.message);
            }
          }).finally(() => {
            // that.confirmLoading = false;
          })
        }

      })
    },

  },
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>
