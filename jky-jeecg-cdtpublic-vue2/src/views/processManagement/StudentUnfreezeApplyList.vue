<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-form-item label="学员姓名">
              <a-input placeholder="请输入学员姓名" v-model="queryParam.stuname"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="身份证号">
              <a-input placeholder="请输入身份证号" v-model="queryParam.idcard"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a-button @click="handleBatchAudit" type="primary" icon="check-circle" style="margin-left: 8px" :disabled="!hasSelected">批量审核</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
      
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <!-- 如有需要，可在此处添加“新增申请”等按钮 -->
      <!-- 例如: <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button> -->
    </div>

    <!-- 表格区域 -->
    <div>
      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange">

        <span slot="action" slot-scope="text, record">
          <a @click="handleAudit(record)">审核</a>
        </span>

      </a-table>
    </div>

    <!-- 审核弹窗 -->
    <a-modal
      title="学员解冻审核"
      :visible="auditModalVisible"
      @ok="handleAuditSubmit"
      @cancel="handleAuditCancel"
      :confirmLoading="confirmLoading"
      okText="提交"
      cancelText="取消">
      <a-form-model ref="auditForm" :model="auditFormModel" :rules="auditFormRules" :label-col="{ span: 5 }" :wrapper-col="{ span: 18 }">
        <a-form-model-item label="审核状态" prop="auditStatus">
          <a-radio-group v-model="auditFormModel.auditStatus">
            <a-radio value="1">审核通过</a-radio>
            <a-radio value="2">审核不通过</a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="审核意见" prop="auditReason">
          <a-textarea v-model="auditFormModel.auditReason" placeholder="请输入审核意见（审核不通过时必填）" :rows="4"/>
        </a-form-model-item>
      </a-form-model>
    </a-modal>

  </a-card>
</template>

<script>
import '@/assets/less/TableExpand.less' // 引入常用的表格样式
import { JeecgListMixin } from '@/mixins/JeecgListMixin' // 引入Jeecg封装的列表混入
import { postAction } from '@/api/manage' // 引入API请求方法

export default {
  name: 'StudentUnfreezeApplyList',
  mixins: [JeecgListMixin], // 使用Jeecg列表混入
  components: {},
  data() {
    return {
      description: '学员解冻申请列表页面',
      // 查询参数
      queryParam: {
        stuname: '',
        idcard: ''
      },
      // 表头定义
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '学员姓名',
          align: 'center',
          dataIndex: 'stuname' 
        },
        {
          title: '身份证号',
          align: 'center',
          dataIndex: 'idcard' 
        },
        {
          title: '驾校名称',
          align: 'center',
          dataIndex: 'insname' 
        },
        {
          title: '申请类型',
          align: 'center',
          dataIndex: 'applyType_dictText' // Jeecg会自动处理字典文本
        },
        {
          title: '当前冻结阶段',
          align: 'center',
          dataIndex: 'regState_dictText' // Jeecg会自动处理字典文本
        },
        {
          title: '申请金额',
          align: 'center',
          dataIndex: 'balanceAmount'
        },
        {
          title: '申请状态',
          align: 'center',
          dataIndex: 'status_dictText' // Jeecg会自动处理字典文本
        },
        {
          title: '申请时间',
          align: 'center',
          dataIndex: 'createTime',
          customRender: function(text){
            return !text?"":text.substr(0,10)
          }
        },
        {
          title: '银行名称',
          align: 'center',
          dataIndex: 'bankname'
        },
        {
          title: '驾校审核意见',
          align: 'center',
          dataIndex: 'auditReason'
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right', // 固定操作列到右侧
          width: 100,     // 给操作列一个合适的宽度
          scopedSlots: { customRender: 'action' }
        }
      ],
      // 请求URL配置
      url: {
        list: '/gzpt/bankRegApply/list', // 列表数据接口
        audit: '/gzpt/bankRegApply/audit'  // 审核提交接口
      },
      // 审核弹窗相关状态
      auditModalVisible: false, // 弹窗是否可见
      confirmLoading: false,    // 弹窗提交按钮的加载状态
      currentRecordId: null,    // 当前操作的记录ID (用于单条审核 或 当前批量项)
      batchAuditQueue: [],      // 队列，用于逐个处理批量审核的项
      currentBatchItemId: null, // 当前正在通过批量流程审核的项ID
      selectedRowKeys: [],      // 选中的行 (来自JeecgListMixin或在此定义)
      // 审核表单模型
      auditFormModel: {
        auditStatus: '1', // 默认为“审核通过”
        auditReason: ''
      },
      // 审核表单校验规则
      auditFormRules: {
        auditStatus: [{ required: true, message: '请选择审核状态!' }],
        auditReason: [
          {
            validator: (rule, value, callback) => {
              if (this.auditFormModel.auditStatus === '2' && !value) {
                callback(new Error('审核不通过时，审核意见不能为空!'));
              } else {
                callback();
              }
            }
          }
        ]
      },
      disableMixinCreated: false // JeecgListMixin的配置，如果不需要混入的created执行，则设为true
    }
  },
  computed: {
    hasSelected() {
      return this.selectedRowKeys.length > 0;
    }
  },
  methods: {
    // 初始化查询参数 (如果启用了查询表单)
    // initQueryParam() {
    //   this.queryParam = { stuname: '', idcard: '' }
    // },

    // 打开审核弹窗 (供单条审核 和 批量审核的单个项 调用)
    handleAudit(record) {
      this.currentRecordId = record.id; // 这个ID是当前弹窗要处理的ID
      // 如果是批量流程的一部分，标记当前批量项ID
      // this.currentBatchItemId = this.batchAuditQueue.length > 0 ? record.id : null;
      // (currentBatchItemId is set before calling this in batch mode)

      this.auditFormModel = { auditStatus: '1', auditReason: '' }; 
      this.auditModalVisible = true;
      this.$nextTick(() => {
        if (this.$refs.auditForm) {
          this.$refs.auditForm.clearValidate();
        }
      });
    },

    // 启动批量审核流程
    handleBatchAudit() {
      if (!this.hasSelected) {
        this.$message.warning('请至少选择一条记录进行审核！');
        return;
      }
      // 从 selectedRowKeys 初始化队列，这里我们直接使用 selectedRowKeys 作为队列的源
      // 但为了能 .shift(), 我们复制一份
      this.batchAuditQueue = [...this.selectedRowKeys]; 
      this.currentBatchItemId = null; // 清除上一次的标记
      this.processNextBatchItem(); // 开始处理第一个
    },

    // 处理批量队列中的下一个项目
    processNextBatchItem() {
      if (this.batchAuditQueue.length > 0) {
        const itemIdToProcess = this.batchAuditQueue.shift(); // 取出队列的第一个ID
        this.currentBatchItemId = itemIdToProcess; // 标记当前正在处理的批量项ID

        // 从dataSource中找到完整的record对象
        const recordToProcess = this.dataSource.find(item => item.id === itemIdToProcess);

        if (recordToProcess) {
          this.handleAudit(recordToProcess); // 调用通用的弹窗开启方法
        } else {
          this.$message.error(`未能找到ID为 ${itemIdToProcess} 的记录，跳过此项。`);
          this.currentBatchItemId = null; // 清除标记，因为此项无法处理
          this.processNextBatchItem(); // 尝试处理下一个
        }
      } else {
        // 队列为空，批量审核完成
        this.$message.success('所有选中项均已处理完毕!');
        this.currentBatchItemId = null; // 清除标记
        this.onClearSelected(); // 清除表格选择 (JeecgListMixin方法)
        this.loadData(1); // 刷新列表
      }
    },

    // 关闭审核弹窗
    handleAuditCancel() {
      this.auditModalVisible = false;
      const wasBatchItem = this.currentBatchItemId;
      this.currentRecordId = null;
      this.currentBatchItemId = null; // 清除当前批量项标记

      if (wasBatchItem) {
        // 如果是在处理批量队列中的某一项时取消了弹窗
        this.batchAuditQueue = []; // 清空剩余队列，停止批量处理
        this.$message.info('批量审核已取消。');
        this.onClearSelected(); // 清除表格选择
        this.loadData(1); // 刷新列表以显示已处理（如果有）和未处理项
      }
    },

    // 提交审核 (处理单条和批量中的单条)
    handleAuditSubmit() {
      this.$refs.auditForm.validate(valid => {
        if (valid) {
          if (this.auditFormModel.auditStatus === '2' && !this.auditFormModel.auditReason) {
            this.$message.warning('审核不通过时，审核意见不能为空!');
            return;
          }
          this.confirmLoading = true;
          const params = {
            id: this.currentRecordId, // ID已在handleAudit中设置，无论是单条还是批量中的当前项
            auditStatus: this.auditFormModel.auditStatus,
            auditReason: this.auditFormModel.auditReason
          };

          postAction(this.url.audit, params).then(res => {
            if (res.success) {
              this.$message.success(res.message || '审核操作成功!');
            } else {
              this.$message.error(res.message || '审核操作失败!');
            }
          }).catch(err => {
            this.$message.error('请求失败: ' + (err.message || '未知错误'));
          }).finally(() => {
            this.confirmLoading = false;
            this.auditModalVisible = false; // 关闭当前弹窗
            
            if (this.currentBatchItemId) {
              // 如果当前提交的是批量队列中的一项
              // this.currentBatchItemId = null; // 在processNextBatchItem开头设置新的，或在队列末尾清空
              this.processNextBatchItem(); // 处理队列中的下一项 (无论成功失败都尝试下一个)
            } else {
              // 非批量审核（即普通单条审核）
              this.loadData(1); // 刷新列表
              this.currentRecordId = null; // 清理单条审核的ID
            }
          });
        } else {
          this.$message.warning('请检查表单填写是否正确!');
          return false;
        }
      });
    },

    // JeecgListMixin 提供的选择变化处理方法
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      // this.selectionRows = selectedRows; // 如果需要完整的行数据
    }
  },
  created() {
    // JeecgListMixin 会自动调用 loadData()，如果需要覆盖或扩展，可以在这里处理
    // 例如，如果查询参数需要特殊初始化，可以在此进行
    // this.initQueryParam(); // 如果有查询表单，取消此行注释
  }
}
</script>

<style scoped>
/* 在这里添加页面特定的样式 */
.table-page-search-submitButtons {
  /* 查询按钮区域样式微调 */
  display: block;
  margin-bottom: 24px;
  white-space: nowrap;
}
</style>
