{"easycom": {"^u-(.*)": "@/uni_modules/uview-ui/components/u-$1/u-$1.vue"}, "pages": [{"path": "pages/login/login", "style": {}}, {"path": "pages/education/ExamResult", "style": {}}, {"path": "pages/education/selectExamForCoach", "style": {}}, {"path": "pages/education/checkCollectionExam", "style": {}}, {"path": "pages/education/selectExam", "style": {}}, {"path": "pages/education/ExamQuestion", "style": {}}, {"path": "pages/education/CourseDetail", "style": {}}, {"path": "pages/homePage", "style": {}}, {"path": "pages/coach/firstPage", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/coach/schedulingAppointments/coachCourseDetail/index", "style": {"navigationBarTitleText": "课程详情", "enablePullDownRefresh": false}}, {"path": "pages/coach/schedulingAppointments/selectStudents/index", "style": {"navigationBarTitleText": "选择学员", "enablePullDownRefresh": false}}, {"path": "pages/coach/schedulingAppointments/index", "style": {"navigationBarTitleText": "排班预约", "enablePullDownRefresh": false}}, {"path": "pages/coach/schedulingAppointments/addCourse/index", "style": {"navigationBarTitleText": "新增排班", "enablePullDownRefresh": false}}, {"path": "pages/coach/homePage", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/coach/classHour/classDetail", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/onlineRegist/register", "style": {}}, {"path": "pages/common/exit", "style": {}}, {"path": "pages/common/success", "style": {}}, {"path": "pages/payment/paying", "style": {}}, {"path": "pages/payment/payingStatus", "style": {}}, {"path": "pages/physicalExamination/hospitalList", "style": {}}, {"path": "pages/physicalExamination/hospitalDetail", "style": {}}, {"path": "pages/physicalExamination/reservationDetail", "style": {}}, {"path": "pages/physicalExamination/report", "style": {}}, {"path": "pages/onlineRegist/signUp", "style": {}}, {"path": "pages/onlineRegist/myRegistration", "style": {}}, {"path": "pages/onlineRegist/schoolDetail", "style": {}}, {"path": "pages/onlineRegist/modules/carType", "style": {}}, {"path": "pages/onlineRegist/modules/Coach", "style": {}}, {"path": "pages/onlineRegist/modules/Comment", "style": {}}, {"path": "pages/onlineRegist/modules/introduce", "style": {}}, {"path": "pages/payment/dzqz", "style": {}}, {"path": "pages/sign/index", "style": {}}, {"path": "pages/coach/carList", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/coach/carDetail", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/coach/education", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/studentPage/homePage", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/studentPage/personal", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/studentPage/serviceEvaluation", "style": {"navigationBarTitleText": "驾校排名", "enablePullDownRefresh": false}}, {"path": "pages/studentPage/myOrder", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/studentPage/comment", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/studentPage/evaluate/Index", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/studentPage/evaluate/evaluationList", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/studentPage/evaluate/evaluationDetail", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/studentPage/contract/contractList", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/studentPage/contract/openPdf", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/studentPage/contract/volumeUp", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/studentPage/payList", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/studentPage/train/appointTrain", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/studentPage/classHour/classDetail", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/studentPage/classHour/classList", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/studentPage/evaluate/evaluate", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/studentPage/examine", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/studentPage/complaint/complaintList", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/studentPage/complaint/complaint", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/studentPage/policyLaws", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/onlineRegist/coachList", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/studentPage/contract/viewPdf", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/coach/studentList", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/coach/studentDetail", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/coach/userCenter", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/studentPage/newsDetail", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/studentPage/train/appointRecord", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/studentPage/evaluate/noEvaluate", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/sign/index", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/canvasPage", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/studentPage/transferSteps", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/studentPage/logoutSteps", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/studentPage/uploadLearningProof", "style": {"navigationBarTitleText": "上传学习证明", "enablePullDownRefresh": false}}, {"path": "pages/education/CourseVideo", "style": {"navigationBarTitleText": "", "enablePullDownRefresh": false}}, {"path": "pages/studentPage/bankRegApply/bankRegApplyList", "style": {"navigationBarTitleText": "银行冻结申请列表", "enablePullDownRefresh": false}}], "globalStyle": {"mp-alipay": {"transparentTitle": "always", "allowsBounceVertical": "NO"}, "navigationBarBackgroundColor": "#0081ff", "navigationBarTitleText": "衢学车", "navigationStyle": "custom", "navigationBarTextStyle": "white"}, "usingComponts": true, "condition": {"current": 0, "list": [{"name": "表单", "path": "pages/login/login", "query": ""}]}}