<template>
  <view
    :class="
      uiStyle === 'elder' ? 'mainContainer elderMainContainer' : 'mainContainer'
    "
  >
    <view class="head-banner">
      <view class="top">
        <img src="~@/static/studentPage/personMenu1.png" />
        <view class="personInfo">
          <p>{{ userInfo.name }}</p>
          <p>{{ userInfo.phone }}</p>
        </view>
      </view>
    </view>
    <view class="schoolInfo">
      <view class="content">
        <img src="~@/static/studentPage/school.png" />
        <view class="info">
          <p>我的驾校</p>
          <p>{{ userInfo.insname }}</p>
        </view>
      </view>
      <view class="content">
        <img src="~@/static/studentPage/coach.png" />
        <view class="info">
          <p>我的教练</p>
          <p v-if="userInfo.bankname">*{{ userInfo.bankname.substring(1) }}</p>
        </view>
      </view>
    </view>
    <uni-section title="学车培训" style="margin-top: 10px">
      <div class="menus">
        <view
          class="grid-item-box"
          :key="index"
          v-for="(item, index) in menusList"
          style="background-color: #fff"
          @click="gotoUrl(item)"
        >
          <img :src="item.icon" />
          <p class="text">{{ item.title }}</p>
        </view>
<!--        <div class="grid-item-box" @click="checkTransferSchool">-->
<!--          <img src="@/static/studentPage/transferMenu.png" />-->
<!--          <p class="text">转校</p>-->
<!--        </div>-->
        <!--        <div-->
        <!--          class="grid-item-box"-->
        <!--          @click="-->
        <!--            () => {-->
        <!--              $refs.changeTrainTypePopup.open();-->
        <!--            }-->
        <!--          "-->
        <!--          v-if="personInfo.traintype === 'C1' || personInfo.traintype === 'C2'"-->
        <!--        >-->
        <!--          <img src="@/static/studentPage/change-car-logo.png" />-->
        <!--          <p class="text">变更车型</p>-->
        <!--        </div>-->
        <div class="grid-item-box" @click="checkLogoutStatus">
          <img src="@/static/studentPage/logoutMenu.png" />
          <p class="text">退学</p>
        </div>
<!--        <div class="grid-item-box" @click="refundAccount">-->
<!--          <img src="@/static/studentPage/logoutMenu.png" />-->
<!--          <p class="text">退款账户</p>-->
<!--        </div>-->
      </div>
    </uni-section>
    <!-- 变更车型弹窗 -->
    <uni-popup ref="changeTrainTypePopup" type="dialog">
      <uni-popup-dialog
        type="warn"
        title="提示"
        :content="
          '您当前学习车型为' +
          personInfo.traintype +
          '，申请变更为' +
          (personInfo.traintype === 'C2' ? 'C1' : 'C2')
        "
        @confirm="handleChangeTrainType"
        @close="
          () => {
            $refs.changeTrainTypePopup.close();
          }
        "
      ></uni-popup-dialog>
    </uni-popup>
    <transter-process
      ref="transterProcess"
      :userInfo="userInfo"
    ></transter-process>
    <logout-process ref="logoutProcess" :userInfo="userInfo"></logout-process>
  </view>
</template>

<script>
import request from "@/utils/request.js";
import { encrypt } from "@/utils/util.js";
import { USER_INFO, STUDY_CAR } from "@/common/util/constants";
import TransterProcess from "./transferProcess.vue";
import LogoutProcess from "./logoutProcess.vue";
export default {
  components: {
    TransterProcess,
    LogoutProcess,
  },
  data() {
    return {
      // showRefundAccount: true,
      uiStyle: "normal",
      menusList: [
        {
          icon: require("@/static/studentPage/personMenu1.png"),
          title: "我的合同",
          url: "/pages/studentPage/contract/openPdf",
        },
        // {
        //   icon: require("@/static/studentPage/alipay.png"),
        //   title: "资金托管\n(支付宝)",
        //   url: "",
        //   type: "alipay",
        // },
        {
          icon: require("@/static/studentPage/personMenu2.png"),
          title: "学时确认",
          url: "/pages/studentPage/myOrder",
        },
        {
          icon: require("@/static/studentPage/personMenu3.png"),
          title: "学时明细",
          url: "/pages/studentPage/classHour/classDetail",
        },
        {
          icon: require("@/static/studentPage/personMenu4.png"),
          title: "预约记录",
          url: "/pages/studentPage/train/appointRecord",
        },
        {
          icon: require("@/static/studentPage/personMenu5.png"),
          title: "结业考核",
          url: "/pages/studentPage/examine",
        },
        {
          icon: require("@/static/studentPage/personMenu6.png"),
          title: "我的评价",
          url: "/pages/studentPage/evaluate/Index?edit=true",
        },
        {
          icon: require("@/static/studentPage/personMenu6.png"),
          title: "多笔冻结",
          url: "/pages/studentPage/bankRegApply/bankRegApplyList",
        },
      ],
      userInfo: {
        bankname: "",
      },
      personInfo: {},
    };
  },
  onShow() {
    ZWJSBridge.onReady(() => {
      ZWJSBridge.setTitle({
        title: "个人中心",
      });

      ZWJSBridge.getUiStyle().then((res) => {
        switch (res.uiStyle) {
          case "elder":
            this.uiStyle = "elder";
            break;
          case "normal":
            this.uiStyle = "normal";
            break;
          default:
            break;
        }
      });
    });
  },
  mounted() {
    this.personInfo = uni.getStorageSync(STUDY_CAR);
    if (this.personInfo.stunum) {
      this.menusList.push({
        icon: require("@/static/studentPage/menu-comment.png"),
        title: "投诉建议",
        url: "/pages/studentPage/comment",
      });

    // 添加上传学习证明菜单项
    this.menusList.push({
      icon: require("@/static/studentPage/uploadLearningProof.png"),
      title: "上传学习证明",
      url: "/pages/studentPage/uploadLearningProof",
    });
    }
    this.getPersonInfo();
  },
  methods: {
    refundAccount() {},
    gotoUrl(item) {
      if (item.type === "alipay") {
        window.location.href =
          "alipays://platformapi/startapp?appId=****************";
      } else {
        uni.navigateTo({
          url: item.url,
        });
      }
    },
    async getPersonInfo() {
      let res = await request(this.$api + "/zlb/tmc/tMCoach/person", {
        data: {
          id: uni.getStorageSync(USER_INFO),
        },
      });
      if (res.page && res.page.records && res.page.records[0]) {
        this.userInfo = res.page.records[0];
        uni.setStorageSync(STUDY_CAR, res.page.records[0]);
      }
      if (res.status === 3) {
        if (res.isSign) {
          this.menusList[0].url = "/pages/studentPage/contract/viewPdf";
        }
      } else {
        uni.showToast({
          title: res.msg,
          icon: "none",
          duration: 3000,
        });
      }
    },
    handleChangeTrainType() {
      uni.showLoading({
        mask: true,
      });
      request(this.$api + "/zlb/stu/carEdit", {
        method: "post",
        data: {
          stunum: this.userInfo.stunum,
          traintype: this.personInfo.traintype === "C1" ? "C2" : "C1",
        },
      })
        .then((res) => {
          uni.showToast({
            title: "申请成功！",
            icon: "none",
            duration: 5000,
          });
          this.$refs.changeTrainTypePopup.close();
        })
        .finally(() => {
          uni.hideLoading();
        });
    },
    checkLogoutStatus() {
      request(this.$api + "/zlb/stuLogoutApply/hasLogoff", {
        method: "POST",
        data: encrypt({
          stunum: this.userInfo.stunum,
        }),
      }).then((res) => {
        if (res.hasLogoff) {
          uni.navigateTo({
            url: "/pages/studentPage/logoutSteps",
          });
        } else {
          this.$refs.logoutProcess.handleLogout();
        }
      });
    },
    // 校验转校申请状态
    checkTransferSchool() {
      request(this.$api + "/gzpt/stuTransfer/processProgress", {
        method: "POST",
        data: encrypt(
          JSON.stringify({
            stunum: this.userInfo.stunum,
          })
        ),
      }).then((res) => {
        if (res) {
          uni.navigateTo({
            url: "/pages/studentPage/transferSteps",
          });
        } else {
          this.$refs.transterProcess.handleTransfer();
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.mainContainer {
  height: 100vh;
  width: 100%;
  background-color: #f8f8f8;

  .head-banner {
    background: url("@/static/studentPage/personBanner.png") no-repeat;
    background-size: 100% 100%;
    height: 27%;
    padding-top: 0.1px;

    .top {
      display: flex;
      align-items: center;
      width: 90%;
      margin: 18% auto 5% auto;

      .personInfo {
        margin-left: 10px;
        color: white;

        p:nth-child(1) {
          font-size: 2.6vh;
          margin-bottom: 5px;
        }
      }
    }

    .progress-box {
      width: 90%;
      margin: 0 auto;

      .carInfo {
        font-size: 1.5vh;
        display: flex;
        justify-content: space-between;
        color: white;
        margin-bottom: 5px;
      }

      /deep/.uni-progress-bar {
        border-radius: 5px;
      }

      /deep/.uni-progress-inner-bar {
        border-radius: 5px;
      }
    }
  }

  .schoolInfo {
    width: 90%;
    background-color: #fff;
    margin: -10% auto 0 auto;
    border-radius: 5px;
    display: flex;
    padding: 17px 0;

    .content {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 50%;
      padding: 0 10px;

      img {
        width: 45px;
      }

      .info {
        margin-left: 10px;
        width: calc(100% - 65px);

        p:nth-child(1) {
          font-size: 2.4vh;
          margin-bottom: 5px;
          color: #333333;
        }

        p:nth-child(2) {
          font-size: 1.5vh;
          color: #999999;
          //	overflow: hidden;
          //	white-space: nowrap;
          //	text-overflow: ellipsis;
          word-break: break-all;
        }
      }
    }
  }

  /deep/.uni-section__content-title {
    margin-bottom: 10px;
    font-size: 2.4vh !important;
    font-weight: 550;
  }

  .menus {
    width: 100%;
    background-color: #fff;
    margin: 0 auto;
    margin-top: -7%;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    display: flex;
    flex-wrap: wrap;
    padding: 17px 0;
    color: #000000;

    .grid-item-box {
      text-align: center;
      width: 25%;
      margin-bottom: 15px;
      .text {
        white-space: pre;
      }
      img {
        width: 50%;
        margin-bottom: 5px;
      }
    }
  }
}
.class-info {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-evenly;
  p {
    width: 47%;
    padding: 5px 0;

    span {
      width: 85%;
      display: inline-block;
    }
  }
  p:nth-child(2n + 1) {
    border-right: 1px solid #efefef;
  }
}
.elderMainContainer {
  /deep/ p {
    font-size: 38rpx !important;
  }
  /deep/ span {
    font-size: 38rpx !important;
  }
  /deep/ .uni-input-placeholder {
    font-size: 38rpx !important;
  }
  /deep/ .ok {
    font-size: 38rpx !important;
  }
}
.logoff-popup-title {
  color: #f0ad4e;
  text-align: center;
  font-size: 16px;
  padding: 1rem;
  border-top-left-radius: 0.3rem;
  border-top-right-radius: 0.3rem;
  background-color: #fff;
}
.logoff-popup-content {
  font-size: 14px;
  padding: 0rem 2rem 1rem;
  color: #6c6c6c;
  background-color: #fff;
  width: 80vw;
}
.transfer-popup-title {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem;
  border-top-left-radius: 0.3rem;
  border-top-right-radius: 0.3rem;
  background-color: #fff;
}
.transfer-popup-content {
  padding: 1rem 1rem 0.5rem;
  background-color: #fff;
  width: 90vw;
}
.transfer-popup-footer {
  border-top: 1px solid #f5f5f5;
  border-bottom-left-radius: 0.3rem;
  border-bottom-right-radius: 0.3rem;
  background-color: #fff;
  display: flex;
  .cancel {
    color: #aaa;
    width: 50%;
    text-align: center;
    padding: 0.6rem 0;
    border-right: 0.1px solid #eee;
  }
  .ok {
    color: #007aff;
    width: 50%;
    text-align: center;
    padding: 0.6rem 0;
  }
}
/deep/.uni-forms-item {
  align-items: center;
}
/deep/.uni-badge {
  z-index: 20;
}
</style>
