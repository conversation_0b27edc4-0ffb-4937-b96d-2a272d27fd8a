<template>
  <view class="container">
    <view class="list-container">
      <view v-for="(item, index) in applyList" :key="index" class="list-item">
        <view class="custom-card">
          <view class="card-header">
            <text class="card-title">{{ item.status_dictText }}</text>
          </view>
          <view class="card-body">
            <view class="card-row">
              <text class="label">学员名称:</text>
              <text class="value">{{ item.stuname }}</text>
            </view>
            <view class="card-row">
              <text class="label">驾校名称:</text>
              <text class="value">{{ item.insname }}</text>
            </view>
            <view class="card-row">
              <text class="label">银行名称:</text>
              <text class="value">{{ item.bankname }}</text>
            </view>
            <view class="card-row">
              <text class="label">当前冻结阶段:</text>
              <text class="value">{{ item.regState_dictText }}</text>
            </view>
            <view class="card-row create-time-row">
              <text class="label">创建时间:</text>
              <text class="value">{{ item.createTime }}</text>
            </view>
            <view class="card-row">
              <text class="label">申请类型:</text>
              <text :class="['card-title', 'apply-type-text', getApplyTypeClass(item.applyType)]">{{ item.applyType_dictText }}</text>
            </view>
          </view>
          <view class="card-foot">
            <button class="details-button" @click="openDetailsModal(item)">
              查看详情
            </button>
          </view>
        </view>
      </view>

      <view class="add-card" @click="handleAdd">
        <view class="add-icon">
          <text class="add-icon-text">+</text>
        </view>
        <text class="add-text">新增申请</text>
      </view>
    </view>

    <view v-if="applyList.length === 0 && loadStatus !== 'loading'" class="empty-container">
      <view class="empty-icon">📋</view>
      <text class="empty-text">暂无冻结申请记录</text>
    </view>

    <u-modal :show="showDetailsModal" title="申请详情" @close="closeDetailsModal" @cancel="closeDetailsModal" :showConfirmButton="false" :closeOnClickOverlay="true">
      <view class="modal-content">
        <view class="card-row">
          <text class="label">学员姓名:</text>
          <text class="value">{{ selectedItemDetails.stuname }}</text>
        </view>
        <view class="card-row">
          <text class="label">身份证号:</text>
          <text class="value">{{ selectedItemDetails.idcard }}</text>
        </view>
        <view class="card-row">
          <text class="label">驾校名称:</text>
          <text class="value">{{ selectedItemDetails.insname }}</text>
        </view>
        <view class="card-row">
          <text class="label">银行名称:</text>
          <text class="value">{{ selectedItemDetails.bankname }}</text>
        </view>
        <view class="card-row">
          <text class="label">监管状态:</text>
          <text class="value">{{ selectedItemDetails.regState_dictText }}</text>
        </view>
        <view class="card-row">
          <text class="label">审核状态:</text>
          <text class="value">{{ selectedItemDetails.status_dictText }}</text>
        </view>
        <view class="card-row">
          <text class="label">申请类型:</text>
          <text :class="['card-title', 'apply-type-text', getApplyTypeClass(selectedItemDetails.applyType)]">{{ selectedItemDetails.applyType_dictText }}</text>
        </view>
        <view class="card-row">
          <text class="label">冻结金额:</text>
          <text class="value">{{ selectedItemDetails.balanceAmount }}</text>
        </view>
        <view class="card-row" style="align-items: baseline">
          <text class="label">创建时间:</text>
          <text class="value">{{ selectedItemDetails.createTime }}</text>
        </view>
        <view class="card-row">
          <text class="label">备注:</text>
          <text class="value">{{ selectedItemDetails.remark }}</text>
        </view>
      </view>
      <view slot="confirmButton">
        <u-button type="primary" @click="handleConfirmApplication" style="margin-right: 10px">确认</u-button>
        <u-button @click="closeDetailsModal">关闭</u-button>
      </view>
    </u-modal>
  </view>
</template>

<script>
import request from "@/utils/request";
import { USER_INFO } from "@/common/util/constants";

export default {
  data() {
    return {
      applyList: [],
      studentId: null,
      pageNo: 1,
      pageSize: 10,
      loadStatus: "more",
      showDetailsModal: false,
      selectedItemDetails: {},
      zlbUserId: null,
      loading: false,
      finished: false,
      pageNum: 1,
    };
  },
  onLoad() {
    this.fetchApplyList();
  },
  methods: {
    async fetchApplyList() {
      try {
        const res = await request(this.$api + `/zlb/bankRegApply/list`, {
          method: "GET",
          data: {
            id: uni.getStorageSync(USER_INFO),
            pageNo: this.pageNo,
            pageSize: 1000,
          },
        });
        this.applyList = res.records;
      } catch (error) {
        console.error("获取申请列表失败:", error);
        this.loadStatus = "more";
        uni.showToast({
          title: "获取数据失败",
          icon: "none",
        });
      }
    },

    openDetailsModal(item) {
      this.selectedItemDetails = item;
      this.showDetailsModal = true;
    },

    closeDetailsModal() {
      this.showDetailsModal = false;
    },

    async handleAdd() {
      try {
        const res = await request(this.$api + `/zlb/bankRegApply/add`, {
          method: "post",
          data: {
            id: uni.getStorageSync(USER_INFO),
          },
        });
        console.log(res);
        uni.showToast({
          title: res,
          icon: "success",
        });
        this.pageNo = 1;
        this.applyList = [];
        this.fetchApplyList();
      } catch (error) {
        uni.showToast({
          title: error,
          icon: "none",
          duration: 3000,
        });
      }
    },
    async handleConfirmApplication() {
      try {
        const res = await request(this.$api + `/zlb/bankRegApply/confirm`, {
          method: "post",
          data: {
            id: uni.getStorageSync(USER_INFO),
            applyId: this.selectedItemDetails.id,
          },
        });
        console.log(res, 777);
        uni.showToast({
          title: res,
          icon: "success",
        });
        this.closeDetailsModal();
        this.fetchApplyList();
      } catch (error) {
        uni.showToast({
          title: error,
          icon: "none",
        });
        console.error("确认申请失败:", error);
      }
    },
    getApplyTypeClass(applyType) {
      if (applyType === '1') {
        return 'freeze-text'; // 冻结
      } else if (applyType === '2') {
        return 'unfreeze-text'; // 解冻
      }
      return '';
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
}

.list-container {
  padding: 20rpx;
}

.custom-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: flex-end;
  padding: 15rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .card-title {
    font-size: 28rpx;
    color: #007aff;
    font-weight: 500;
  }
}

.card-body {
  padding: 20rpx;

  .card-row {
    display: flex;
    align-items: flex-start;
    padding: 8rpx 0;
    font-size: 28rpx;
    line-height: 1.5;

    .label {
      color: #666;
      margin-right: 10rpx;
      white-space: nowrap;
      flex-shrink: 0;
      width: 180rpx;
    }

    .value {
      color: #333;
      flex-grow: 1;
      word-break: break-word;
    }
  }
  .create-time-row .label,
  .create-time-row .value {
    align-self: baseline;
  }
}

.card-foot {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 20rpx;
  border-top: 1rpx solid #f0f0f0;

  .details-button {
    background-color: #007aff;
    color: white;
    font-size: 24rpx;
    padding: 8rpx 16rpx;
    border-radius: 8rpx;
    border: none;
    line-height: 1.5;
    margin-left: 0px;
    margin-right: 0px;
  }
  .details-button::after {
    border: none;
  }

  .apply-type-text {
    font-size: 28rpx;
  }
}

.freeze-text {
  color: #ff4d4f; // 例如：红色表示冻结
}

.unfreeze-text {
  color: #52c41a; // 例如：绿色表示解冻
}

.add-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;

  &:active {
    opacity: 0.8;
  }
}

.add-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #0081ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}

.add-icon-text {
  color: #ffffff;
  font-size: 48rpx;
  font-weight: 300;
  line-height: 1;
}

.add-text {
  font-size: 28rpx;
  color: #303133;
}

.empty-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 30rpx;
  color: #909399;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}
/deep/ .u-modal__content {
  padding: 0rpx !important;
  .modal-content {
    background-color: #fff;
    display: flex;
    flex-direction: column;
    padding: 0rpx 10rpx;
    .card-row {
      display: flex;
      align-items: flex-start;
      padding: 8rpx 0;
      font-size: 28rpx;
      line-height: 1.5;

      .label {
        color: #666;
        margin-right: 10rpx;
        white-space: nowrap;
        flex-shrink: 0;
        width: 180rpx;
      }

      .value {
        color: #333;
        flex-grow: 1;
        word-break: break-word;
      }
    }
  }
}

.remarks-item .detail-value {
  white-space: pre-wrap;
}
</style>
