<template>
  <view class="mainContainer">
    <view class="infos">
      <view class="main-info">
        <p>车牌号码</p>
        <p>{{ dataSource.licnum }}</p>
      </view>
      <view class="main-info">
        <p>车型</p>
        <p>{{ dataSource.perdritype }}</p>
      </view>
      <view class="main-info">
        <p>品牌</p>
        <p>{{ dataSource.brand }}</p>
      </view>
      <view class="main-info">
        <p>生产厂家</p>
        <p>{{ dataSource.manufacture }}</p>
      </view>
      <view class="main-info">
        <p>教练编号</p>
        <p>{{ dataSource.coachnum }}</p>
      </view>
      <view class="main-info">
        <p>所属驾校</p>
        <p>{{ dataSource.insname }}</p>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      dataSource: {},
    };
  },
  onLoad(option) {
    if (option.record) {
      this.dataSource = JSON.parse(option.record);
    }
  },
};
</script>

<style scoped lang="less">
.infos {
  .main-info {
    padding: 15px 10px;
    display: flex;
    justify-content: space-between;

    p:nth-child(2) {
      color: rgb(157, 157, 157);
    }
  }

  .main-info:nth-child(2n) {
    background-color: rgb(250, 250, 250);
  }

  .main-info:nth-child(2n + 1) {
    background-color: #fff;
  }
}
</style>
