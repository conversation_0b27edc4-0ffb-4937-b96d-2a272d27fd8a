<template>
  <view class="page-wrapper">
    <view class="top-header">
      <div class="status">
        <img src="static/examination/clock-logo.png" class="status-logo" />
        <span>待支付</span>
      </div>
      <div class="status-text">你的驾校报名已提交，请尽快付款</div>
    </view>
    <div class="bill-border"></div>
    <div class="pay-bill">
      <div class="item title">
        <p>支付金额</p>
        <p>￥4000</p>
      </div>
      <div class="item">
        <p>支付内容</p>
        <p>驾校培训费用</p>
      </div>
      <div class="item">
        <p>报名驾校</p>
        <p>上虞区驾校</p>
      </div>
      <div class="item">
        <p>报名时间</p>
        <p>2022-09-27 10:49:34</p>
      </div>
    </div>
    <div class="pay-way">
      <p class="title">请选择支付方式</p>
      <div class="options">
        <div class="item">
          <div class="left">
            <img src="static/examination/alipay.png" />
            <p>支付宝</p>
          </div>
          <div class="right">
            <radio
              :checked="payway === 'alipay'"
              @click="handlePayWay('alipay')"
            ></radio>
          </div>
        </div>
        <div class="item">
          <div class="left">
            <img src="static/examination/bankpay.png" />
            <p>云支付</p>
          </div>
          <div class="right">
            <radio
              :checked="payway === 'bankpay'"
              @click="handlePayWay('bankpay')"
            ></radio>
          </div>
        </div>
      </div>
    </div>

    <div class="agree-contract">
      <radio
        :checked="agree"
        @click="() => (agree = !agree)"
        style="transform: scale(0.7)"
        color="#6490FB"
      ></radio>
      <p>我已签署并同意</p>
      <navigator url=""><span>《衢州市机动车驾驶员合同》</span></navigator>
    </div>
    <div class="pay-footer">
      <button class="pay-button" type="primary" @click="handlePay">
        确认支付
      </button>
    </div>
  </view>
</template>

<script>
import request from "@/utils/request.js";
export default {
  name: "PaymentPaying",
  data() {
    return {
      payway: "alipay",
      agree: false,
      dataSource: {},
    };
  },
  created() {
    ZWJSBridge.onReady(() => {
      ZWJSBridge.setTitle({
        title: "驾校报名支付",
      })
        .then((result) => {
          console.log(result);
        })
        .catch((error) => {
          console.log(error);
        });
      ZWJSBridge.getUiStyle()
        .then((result) => {
          switch (result.uiStyle) {
            case "normal":
              break;
            case "elder":
              require("@/static/newStyle.less");
              break;
            default:
              break;
          }
          console.log(result);
        })
        .catch((error) => {
          console.log(error);
        }) //浙里办APP 6.11.0 版本以下版本标准模式兼容
        .catch((error) => {
          setUiStyle("catch 标准模式兼容");
          console.log(error);
        });
    });
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      // request(this.$api + '', {
      //   method: 'GET',
      //   data: {
      //   }
      // }).then((res) => {
      //   this.dataSource = res;
      // })
    },
    handlePayWay(way) {
      this.payway = way;
    },
    handlePay() {
      // request(this.$api + '', {
      //   method: 'POST',
      //   data: {
      //   }
      // }).then((res) => {
      // uni.showToast({
      //   title: '正在跳转支付...',
      //   icon: 'none',
      // })
      // })
    },
  },
};
</script>

<style lang="less" scoped>
.page-wrapper {
  background-color: #f8f8f8;
  min-height: 100vh;
  padding-top: 0.1px;
  position: relative;

  .top-header {
    background-color: #6490fb;
    color: #fff;
    padding: 2rem 3rem 4rem;

    .status {
      font-size: 1.5rem;
      display: flex;
      align-items: center;
      img {
        width: 1.5rem;
        margin-right: 0.6rem;
      }
    }

    .status-text {
      margin-top: 0.5rem;
      font-size: 1rem;
    }
  }
  .bill-border {
    width: 95%;
    height: 1rem;
    background: #3b6be6;
    border-radius: 13px;
    position: absolute;
    left: 2.5%;
    top: 7.5rem;
  }
  .pay-bill {
    background-color: #fff;
    padding: 1rem 2rem;
    width: calc(90% - 4rem);
    box-shadow: 0px 1px 8px 0px rgba(194, 200, 211, 0.2);
    position: absolute;
    left: 5%;
    top: 8rem;

    .item {
      display: flex;
      justify-content: space-between;
      color: #333333;
      margin: 0.7rem 0;
    }

    .title {
      font-size: 1.3rem;
      font-weight: bolder;
      margin-bottom: 1.5rem;
      p:nth-child(2) {
        color: #ff0000;
      }
    }
  }

  .pay-way {
    margin-top: 12rem;
    padding: 1rem 1rem;
    width: calc(100% - 2rem);
    .title {
      color: #666666;
      margin-bottom: 0.5rem;
    }

    .options {
      padding: 0.6rem 1rem;
      background: #ffffff;
      box-shadow: 0px 1px 8px 0px rgba(194, 200, 211, 0.2);
      border-radius: 10px;

      .item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.2rem 0;
        .left {
          display: flex;
          align-items: center;
          img {
            width: 2rem;
            margin-right: 1rem;
          }
        }
      }

      .item:nth-child(1) {
        border-bottom: 1px solid #eeeeee;
      }
    }
  }

  .agree-contract {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;

    span {
      color: #6490fb;
    }
  }
  .pay-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #fff;
    padding: 0.8rem;

    .pay-button {
      width: calc(100% - 1.6rem);
      margin-left: 0.1rem;
      border-radius: 2rem;
      background-color: #2696f4;
    }
  }
}
</style>
