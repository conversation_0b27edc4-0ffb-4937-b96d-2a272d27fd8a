<template>
  <view style="background-color: #fff"> </view>
</template>

<script>
import Vue from "vue";
import { ACCESS_TOKEN, USER_NAME, USER_INFO } from "@/common/util/constants";
export default {
  data() {
    return {
      PageCur: "home",
      commponent1Key: 0,
      commponent2Key: 0,
    };
  },
  onLoad: function () {
    if (this.$route.params.isback != 1) {
      dd.getAuthCode()
        .then((res) => {
          this.$http
            .get("/sys/zwdd/zwddLogin?authCode=" + res.code)
            .then((res) => {
              if (res.data.code == 200) {
                this.$store.commit("SET_TOKEN", "");
                uni.setStorageSync(ACCESS_TOKEN, "");
                this.$store.commit("SET_TOKEN", res.data.result.token);
                uni.setStorageSync(ACCESS_TOKEN, res.data.result.token);

                // 设置会员昵称
                aplus_queue.push({
                  action: "aplus.setMetaInfo",
                  arguments: ["_user_nick", res.data.result.userInfo.username],
                });
                // 设置会员ID
                aplus_queue.push({
                  action: "aplus.setMetaInfo",
                  arguments: ["_user_id", res.data.result.userInfo.workNo],
                });
                aplus_queue.push({
                  action: "aplus.setMetaInfo",
                  arguments: [
                    "_dev_id",
                    "设备ID是业务定义的，用于定义唯一的设备标识。这个目前没有要求，可不设置。",
                  ],
                });

                // 如采集用户信息是异步行为，需要先设置完用户信息后再执行这个START埋点
                // 此时被block住的日志会携带上用户信息逐条发出
                aplus_queue.push({
                  action: "aplus.setMetaInfo",
                  arguments: ["_hold", "START"],
                });
                this.$Router.replaceAll({ name: "ygjgIndex" });
              } else {
                this.$Router.replaceAll({ name: "login" });
                // console.log(res.data.message);
              }
            })
            .then((res) => {
              // this.getData();
            });
        })
        .catch((err) => {
          this.$Router.replaceAll({ name: "login" });
        });
    } else {
      this.$Router.replaceAll({ name: "login" });
    }
    // this.PageCur='home'
    // ++this.commponent1Key
    // ++this.commponent2Key
  },
  methods: {
    NavChange: function (e) {
      this.PageCur = e.currentTarget.dataset.cur;
    },
  },
};
</script>

<style></style>
