<template>
  <view :class="uiStyle === 'elder' ? 'lists elderLists' : 'lists'">
    <view class="listsItem" v-for="(item, index) in dataList">
      <view class="left">
        <img src="~@/static/noImg.png" />
      </view>
      <view class="right">
        <div class="right1">{{ item.name }}</div>
        <div class="right3">{{ item.address }}</div>
        <!-- <div class="right2">距您 {{item.district}} km</div> -->

        <!-- <div class="right4"><p >规模大</p> <p>网络约课</p></div> -->
      </view>
    </view>
  </view>
</template>

<script>
import request from "@/utils/request.js";
export default {
  data() {
    return {
      siteList: [],
      currentPage: 0,
    };
  },
  props: ["dataList", "uiStyle"],
  methods: {},
};
</script>

<style scoped lang="less">
.lists {
  .listsItem {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid rgba(132, 132, 132, 0.2);
    height: auto;
    color: grey;
    font-size: 0.9rem;

    .left {
      width: 30%;
      width: 5rem;
      height: 5rem;
      border-radius: 0.5rem;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .right {
      width: calc(100% - 5rem);
      padding-left: 25rpx;

      .right1 {
        margin-bottom: 0.4rem;
        font-size: 1.2rem;
        color: #333;
        align-items: baseline;
        font-weight: 550;
      }

      .right2 {
        margin-bottom: 0.4rem;
        color: #999999;
        font-size: 0.9rem;
      }

      .right3 {
        font-size: 0.9rem;
        margin-bottom: 0.4rem;
        color: #999999;
      }
    }
  }
}

.elderLists {
  .right1 {
    font-size: 1.5rem !important;
  }
  .right3,
  .right2 {
    font-size: 1.4rem !important;
  }
}
</style>
